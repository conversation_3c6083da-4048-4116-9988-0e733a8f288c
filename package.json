{"name": "jbs", "version": "1.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --port 4173", "upload_qiniu": "node build_to_qiniu.js"}, "dependencies": {"@nutui/nutui": "3.2.3", "axios": "^0.27.2", "cnjm-postcss-px-to-viewport": "^1.0.0", "html2canvas": "^1.4.1", "jquery": "^3.6.1", "js-cookie": "^3.0.1", "mui-player": "^1.7.0", "normalize.css": "^8.0.1", "pinia": "^2.0.21", "screenfull": "^6.0.2", "three": "^0.148.0", "vant": "^4.0.0-rc.0", "vue": "^3.2.37", "vue-router": "^4.1.3"}, "devDependencies": {"@vitejs/plugin-vue": "^3.0.1", "less": "^3.13.1", "less-loader": "^5.0.0", "unplugin-vue-components": "^0.22.7", "vite": "^3.0.4", "qiniu": "^7.3.2"}}