# 剧本杀系统多机构支持改造方案（简化版）

## 1. 现状分析

### 1.1 当前系统架构
- **单租户系统**: 所有用户、剧本、记录都在同一个数据空间
- **无数据隔离**: 实体类没有机构标识字段
- **权限控制**: 基于JWT令牌的用户身份验证，但无机构级别权限控制
- **配置管理**: Redis配置使用固定的Hash Key，无机构区分

### 1.2 实体关系分析

**核心实体依赖关系：**
```
OrganizationEntity (新增)
├── AdminUserEntity (管理员属于机构)
├── UserEntity (用户属于机构)
├── ScriptEntity (剧本属于机构)
├── ScriptSeriesEntity (剧本系列属于机构)
├── HelpQuestionEntity (帮助问题属于机构)
└── FileEntity (文件属于机构)

ScriptRecordEntity (游戏记录)
├── 关联 UserEntity (通过userId)
└── 关联 ScriptEntity (通过scriptId)
└── 间接属于机构 (通过用户和剧本的机构)

OrderEntity (订单)
├── 关联 UserEntity (通过userId)
└── 关联 ScriptEntity (通过scriptId)
└── 间接属于机构 (通过用户和剧本的机构)

SysLogEntity (系统日志)
└── 关联 UserEntity/AdminUserEntity (通过userId)
└── 间接属于机构 (通过操作用户的机构)
```

## 2. 改造方案设计

### 2.1 数据模型改造

#### 2.1.1 新增机构实体
```java
@Document(collection = "organization")
public class OrganizationEntity {
    @Id
    @JsonIgnore
    public ObjectId id;
    
    // 机构ID
    public String organId = new SnowflakeIdWorker(1, 0).nextId();
    
    // 机构名称
    public String name;
    
    // 机构代码
    public String code;
    
    // 机构状态 (0-禁用, 1-启用)
    public Integer status = 1;
    
    // 创建时间
    public Long createTime = System.currentTimeMillis();
    
    // 机构配置信息
    public JSONObject configInfo = new JSONObject();
    
    // 逻辑删除
    public Integer deleted = 0;
}
```

#### 2.1.2 实体类添加organId字段

**需要添加organId的实体：**
- `AdminUserEntity` - 管理员属于机构
- `UserEntity` - 用户属于机构
- `ScriptEntity` - 剧本属于机构
- `ScriptSeriesEntity` - 剧本系列属于机构
- `HelpQuestionEntity` - 帮助问题属于机构
- `FileEntity` - 文件属于机构

**不需要添加organId的实体：**
- `ScriptRecordEntity` - 通过userId和scriptId间接关联机构
- `OrderEntity` - 通过userId和scriptId间接关联机构
- `SysLogEntity` - 通过userId间接关联机构

#### 2.1.3 实体改造示例

```java
// UserEntity 改造示例
@Document(collection = "user")
public class UserEntity {
    // 现有字段...
    
    // 新增：所属机构ID
    public String organId;
    
    // 现有字段...
}

// AdminUserEntity 改造示例  
@Document(collection = "admin_user")
public class AdminUserEntity {
    // 现有字段...
    
    // 新增：所属机构ID
    public String organId;
    
    // 现有字段...
}
```

### 2.2 数据访问层改造

#### 2.2.1 Repository接口扩展
为每个需要机构隔离的Repository添加按机构查询的方法：

```java
// UserRepository 扩展示例
public interface UserRepository extends MongoRepository<UserEntity, ObjectId> {
    // 现有方法...
    
    // 新增：按机构查询
    List<UserEntity> findByOrganId(String organId);
    Page<UserEntity> findByOrganId(String organId, Pageable pageable);
    UserEntity findFirstByUserIdAndOrganId(String userId, String organId);
    Boolean existsByAccountAndOrganId(String account, String organId);
    Boolean existsByEmailAndOrganId(String email, String organId);
    
    // 修改现有查询方法，添加机构过滤
    @Query(value = "{'organId': ?1, $and: [?0]}")
    Page<UserEntity> getPageListByOrgan(Document d, String organId, Pageable pageable);
    
    @Query(value = "{'organId': ?1, $and: [?0]}")
    List<UserEntity> getListByOrgan(Document d, String organId);
}
```

#### 2.2.2 新增OrganizationRepository
```java
public interface OrganizationRepository extends MongoRepository<OrganizationEntity, ObjectId> {
    Boolean existsByOrganId(String organId);
    OrganizationEntity findFirstByOrganId(String organId);
    OrganizationEntity findFirstByCode(String code);
    Boolean existsByCode(String code);
    
    @Query(value = "?0")
    Page<OrganizationEntity> getPageList(Document d, Pageable pageable);
    
    @Query(value = "?0") 
    List<OrganizationEntity> getList(Document d);
}
```

### 2.3 业务逻辑层改造

#### 2.3.1 Model层改造要点

**UserModel改造：**
- 所有用户查询添加机构过滤
- 用户注册时自动设置机构ID
- 邮箱/账号唯一性检查限制在机构内

**AdminUserModel改造：**
- 管理员只能管理同机构的数据
- 登录时验证机构权限
- 跨机构访问控制

**ScriptRecordModel改造：**
- 创建记录时验证用户和剧本是否属于同一机构
- 查询记录时通过用户机构进行过滤

**OrderModel改造：**
- 订单创建时验证用户和剧本机构一致性
- 订单查询按机构过滤

#### 2.3.2 新增OrganizationModel
```kotlin
class OrganizationModel {
    // 创建机构
    fun createOrganization(name: String, code: String): OrganizationEntity
    
    // 获取机构信息
    fun getOrganizationInfo(organId: String): OrganizationEntity?
    
    // 验证机构状态
    fun validateOrganizationStatus(organId: String): Boolean
    
    // 机构配置管理
    fun updateOrganizationConfig(organId: String, config: JSONObject)
}
```

### 2.4 控制器层改造

#### 2.4.1 TokenInterceptor改造
- JWT令牌中包含机构信息（通过subject字段存储organId）
- 请求时自动注入当前用户的机构ID到request attribute

#### 2.4.2 控制器改造示例

```kotlin
@RestController
@RequestMapping(value = ["/v1/user/"])
class UserController {

    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(
        @RequestAttribute adminUserId: String,
        @RequestAttribute organId: String,  // 从token中自动注入
        @RequestBody query: String,
        pageable: Pageable
    ): OutResponse<Any> {
        // 添加机构过滤
        val queryObject = JSONObject.parseObject(query)
        queryObject.put("organId", organId)

        return OutResponse("000000", "",
            StaticBean.userRepository.getPageListByOrgan(
                Document.parse(queryObject.toJSONString()),
                organId,
                pageable
            )
        )
    }
}
```

### 2.5 配置管理改造

#### 2.5.1 Redis配置键改造

**当前配置键格式：**
```
config_jbs_jxsr
```

**改造后格式：**
```
config_jbs_jxsr:{organId}
```

#### 2.5.2 ConfigController改造

```kotlin
@RestController
@RequestMapping("/v1/config")
class ConfigController {

    @GetMapping("/hash")
    @NeedAdminToken
    fun getHashConfigInfo(
        @RequestAttribute organId: String,  // 从token中自动注入
        @RequestParam field: String
    ): OutResponse<Any> {
        // 构建机构专属配置键
        val key = "${StaticBean.commonConfig.redisHashKeyName}:${organId}"

        return OutResponse("000000", "", ConfigModel().getHashConfig(key, field))
    }
}
```

### 2.6 认证授权改造

#### 2.6.1 TokenModel改造

**关键改动：将organId存储在JWT的subject字段中**

```kotlin
class TokenModel {
    // 生成令牌时将organId作为subject
    fun genTokenAndSave(
        userId: String,
        roleName: String,
        organId: String,  // 用户的机构ID
        expireDay: Int
    ): String {
        val jwtModel = JWTModel()
        jwtModel.expireDays = expireDay
        // 第三个参数改为organId
        val token = jwtModel.gen_token("jbs", userId, organId, roleName)

        val tokenName = genTokenKey(roleName, token)
        StaticBean.redisService.set(tokenName, userId, jwtModel.expireDays * 24 * 3600L)
        return token
    }
}
```

#### 2.6.2 TokenInterceptor改造

```kotlin
// 在TokenInterceptor中解析token时，将organId注入到request
val organId = claims.subject  // organId存储在subject中
req.setAttribute("organId", organId)
```

## 3. 数据迁移方案

### 3.1 迁移步骤

1. **创建机构表**
   - 创建默认机构
   - 设置机构基础信息

2. **实体表结构升级**
   - 为现有实体添加organId字段
   - 设置默认机构ID

3. **数据迁移脚本**
   - 将现有数据关联到默认机构
   - 更新Redis配置键格式

4. **索引优化**
   - 为organId字段添加索引
   - 创建复合索引优化查询

### 3.2 迁移脚本示例

```javascript
// MongoDB迁移脚本
// 1. 创建默认机构
db.organization.insertOne({
    "organId": "default_organ_001",
    "name": "默认机构", 
    "code": "DEFAULT",
    "status": 1,
    "createTime": new Date().getTime(),
    "configInfo": {},
    "deleted": 0
});

// 2. 为用户表添加机构ID
db.user.updateMany(
    {"organId": {$exists: false}},
    {$set: {"organId": "default_organ_001"}}
);

// 3. 为管理员表添加机构ID  
db.admin_user.updateMany(
    {"organId": {$exists: false}},
    {$set: {"organId": "default_organ_001"}}
);

// 4. 其他实体表类似处理...
```

## 4. 系统配置改造

### 4.1 CommonConfig配置类扩展

```java
@Configuration
public class CommonConfig {
    // 现有配置...

    // 构建机构专属Redis键
    public String buildOrganRedisKey(String organId) {
        return redisHashKeyName + ":" + organId;
    }
}
```

## 5. 实施计划

### 5.1 第一阶段：数据模型改造
- 创建OrganizationEntity实体
- 为核心实体添加organId字段
- 扩展Repository接口
- 数据迁移脚本

### 5.2 第二阶段：业务逻辑改造
- Model层添加机构过滤逻辑
- TokenModel改造（organId存储在subject中）
- TokenInterceptor改造（注入organId到request）

### 5.3 第三阶段：接口改造
- Controller层添加机构过滤
- ConfigController改造支持机构配置
- 所有接口直接支持多机构

## 6. 总结

本简化改造方案专注于最小化开发工作量，核心改动包括：

1. **数据模型扩展**：为核心实体添加organId字段
2. **JWT令牌改造**：将organId存储在subject字段中
3. **自动机构注入**：TokenInterceptor自动将organId注入到request
4. **业务逻辑过滤**：所有查询自动添加机构过滤
5. **配置隔离**：Redis配置键增加机构后缀

改造完成后，系统将直接支持多机构模式，每个机构拥有独立的数据空间，通过JWT令牌自动实现数据隔离。
