# 剧本杀系统多机构支持改造方案

## 1. 现状分析

### 1.1 当前系统架构
- **单租户系统**: 所有用户、剧本、记录都在同一个数据空间
- **无数据隔离**: 实体类没有机构标识字段
- **权限控制**: 基于JWT令牌的用户身份验证，但无机构级别权限控制
- **配置管理**: Redis配置使用固定的Hash Key，无机构区分

### 1.2 实体关系分析

**核心实体依赖关系：**
```
OrganEntity (新增)
├── AdminUserEntity (管理员属于机构)
├── UserEntity (用户属于机构)  
├── ScriptEntity (剧本属于机构)
├── ScriptSeriesEntity (剧本系列属于机构)
├── HelpQuestionEntity (帮助问题属于机构)
└── FileEntity (文件属于机构)

ScriptRecordEntity (游戏记录)
├── 关联 UserEntity (通过userId)
└── 关联 ScriptEntity (通过scriptId)
└── 间接属于机构 (通过用户和剧本的机构)

OrderEntity (订单)
├── 关联 UserEntity (通过userId)
└── 关联 ScriptEntity (通过scriptId)  
└── 间接属于机构 (通过用户和剧本的机构)

SysLogEntity (系统日志)
└── 关联 UserEntity/AdminUserEntity (通过userId)
└── 间接属于机构 (通过操作用户的机构)
```

## 2. 改造方案设计

### 2.1 数据模型改造

#### 2.1.1 新增机构实体
```java
@Document(collection = "organization")
public class OrganizationEntity {
    @Id
    @JsonIgnore
    public ObjectId id;
    
    // 机构ID
    public String organId = new SnowflakeIdWorker(1, 0).nextId();
    
    // 机构名称
    public String name;
    
    // 机构代码
    public String code;
    
    // 机构状态 (0-禁用, 1-启用)
    public Integer status = 1;
    
    // 创建时间
    public Long createTime = System.currentTimeMillis();
    
    // 机构配置信息
    public JSONObject configInfo = new JSONObject();
    
    // 逻辑删除
    public Integer deleted = 0;
}
```

#### 2.1.2 实体类添加organId字段

**需要添加organId的实体：**
- `AdminUserEntity` - 管理员属于机构
- `UserEntity` - 用户属于机构
- `ScriptEntity` - 剧本属于机构
- `ScriptSeriesEntity` - 剧本系列属于机构
- `HelpQuestionEntity` - 帮助问题属于机构
- `FileEntity` - 文件属于机构

**不需要添加organId的实体：**
- `ScriptRecordEntity` - 通过userId和scriptId间接关联机构
- `OrderEntity` - 通过userId和scriptId间接关联机构
- `SysLogEntity` - 通过userId间接关联机构

#### 2.1.3 实体改造示例

```java
// UserEntity 改造示例
@Document(collection = "user")
public class UserEntity {
    // 现有字段...
    
    // 新增：所属机构ID
    public String organId;
    
    // 现有字段...
}

// AdminUserEntity 改造示例  
@Document(collection = "admin_user")
public class AdminUserEntity {
    // 现有字段...
    
    // 新增：所属机构ID
    public String organId;
    
    // 现有字段...
}
```

### 2.2 数据访问层改造

#### 2.2.1 Repository接口扩展
为每个需要机构隔离的Repository添加按机构查询的方法：

```java
// UserRepository 扩展示例
public interface UserRepository extends MongoRepository<UserEntity, ObjectId> {
    // 现有方法...
    
    // 新增：按机构查询
    List<UserEntity> findByOrganId(String organId);
    Page<UserEntity> findByOrganId(String organId, Pageable pageable);
    UserEntity findFirstByUserIdAndOrganId(String userId, String organId);
    Boolean existsByAccountAndOrganId(String account, String organId);
    Boolean existsByEmailAndOrganId(String email, String organId);
    
    // 修改现有查询方法，添加机构过滤
    @Query(value = "{'organId': ?1, $and: [?0]}")
    Page<UserEntity> getPageListByOrgan(Document d, String organId, Pageable pageable);
    
    @Query(value = "{'organId': ?1, $and: [?0]}")
    List<UserEntity> getListByOrgan(Document d, String organId);
}
```

#### 2.2.2 新增OrganizationRepository
```java
public interface OrganizationRepository extends MongoRepository<OrganizationEntity, ObjectId> {
    Boolean existsByOrganId(String organId);
    OrganizationEntity findFirstByOrganId(String organId);
    OrganizationEntity findFirstByCode(String code);
    Boolean existsByCode(String code);
    
    @Query(value = "?0")
    Page<OrganizationEntity> getPageList(Document d, Pageable pageable);
    
    @Query(value = "?0") 
    List<OrganizationEntity> getList(Document d);
}
```

### 2.3 业务逻辑层改造

#### 2.3.1 Model层改造要点

**UserModel改造：**
- 所有用户查询添加机构过滤
- 用户注册时自动设置机构ID
- 邮箱/账号唯一性检查限制在机构内

**AdminUserModel改造：**
- 管理员只能管理同机构的数据
- 登录时验证机构权限
- 跨机构访问控制

**ScriptRecordModel改造：**
- 创建记录时验证用户和剧本是否属于同一机构
- 查询记录时通过用户机构进行过滤

**OrderModel改造：**
- 订单创建时验证用户和剧本机构一致性
- 订单查询按机构过滤

#### 2.3.2 新增OrganizationModel
```kotlin
class OrganizationModel {
    // 创建机构
    fun createOrganization(name: String, code: String): OrganizationEntity
    
    // 获取机构信息
    fun getOrganizationInfo(organId: String): OrganizationEntity?
    
    // 验证机构状态
    fun validateOrganizationStatus(organId: String): Boolean
    
    // 机构配置管理
    fun updateOrganizationConfig(organId: String, config: JSONObject)
}
```

### 2.4 控制器层改造

#### 2.4.1 权限控制增强

**新增机构权限注解：**
```java
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface NeedOrganPermission {
    String value() default ""; // 权限类型
}
```

**TokenInterceptor改造：**
- JWT令牌中包含机构信息
- 请求时自动注入当前用户的机构ID
- 跨机构访问拦截

#### 2.4.2 控制器改造示例

```kotlin
@RestController
@RequestMapping(value = ["/v1/user/"])
class UserController {
    
    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(
        @RequestAttribute adminUserId: String,
        @RequestBody query: String,
        pageable: Pageable
    ): OutResponse<Any> {
        // 获取管理员的机构ID
        val admin = StaticBean.adminUserRepository.findFirstByAdminUserId(adminUserId)
        val organId = admin.organId
        
        // 添加机构过滤
        val queryObject = JSONObject.parseObject(query)
        queryObject.put("organId", organId)
        
        return OutResponse("000000", "", 
            StaticBean.userRepository.getPageListByOrgan(
                Document.parse(queryObject.toJSONString()), 
                organId, 
                pageable
            )
        )
    }
}
```

### 2.5 配置管理改造

#### 2.5.1 Redis配置键改造

**当前配置键格式：**
```
config_jbs_jxsr
```

**改造后格式：**
```
config_jbs_jxsr:{organId}
```

#### 2.5.2 ConfigController改造

```kotlin
@RestController
@RequestMapping("/v1/config")
class ConfigController {
    
    @GetMapping("/hash")
    @NeedAdminToken
    fun getHashConfigInfo(
        @RequestAttribute adminUserId: String,
        @RequestParam field: String
    ): OutResponse<Any> {
        // 获取管理员机构ID
        val admin = StaticBean.adminUserRepository.findFirstByAdminUserId(adminUserId)
        val organId = admin.organId
        
        // 构建机构专属配置键
        val key = "${StaticBean.commonConfig.redisHashKeyName}:${organId}"
        
        return OutResponse("000000", "", ConfigModel().getHashConfig(key, field))
    }
}
```

### 2.6 认证授权改造

#### 2.6.1 JWT令牌增强

**令牌中添加机构信息：**
```kotlin
class JWTModel {
    fun gen_token(
        issuer: String, 
        id: String, 
        subject: String, 
        audience: String,
        organId: String  // 新增机构ID
    ): String {
        // JWT Claims中包含organId
    }
}
```

#### 2.6.2 TokenModel改造

```kotlin
class TokenModel {
    // 生成令牌时包含机构信息
    fun genTokenAndSave(
        userId: String,
        roleName: String, 
        organId: String,  // 新增
        expireDay: Int
    ): String
    
    // 验证令牌时检查机构权限
    fun checkTokenWithOrgan(token: String, organId: String): Boolean
}
```

## 3. 数据迁移方案

### 3.1 迁移步骤

1. **创建机构表**
   - 创建默认机构
   - 设置机构基础信息

2. **实体表结构升级**
   - 为现有实体添加organId字段
   - 设置默认机构ID

3. **数据迁移脚本**
   - 将现有数据关联到默认机构
   - 更新Redis配置键格式

4. **索引优化**
   - 为organId字段添加索引
   - 创建复合索引优化查询

### 3.2 迁移脚本示例

```javascript
// MongoDB迁移脚本
// 1. 创建默认机构
db.organization.insertOne({
    "organId": "default_organ_001",
    "name": "默认机构", 
    "code": "DEFAULT",
    "status": 1,
    "createTime": new Date().getTime(),
    "configInfo": {},
    "deleted": 0
});

// 2. 为用户表添加机构ID
db.user.updateMany(
    {"organId": {$exists: false}},
    {$set: {"organId": "default_organ_001"}}
);

// 3. 为管理员表添加机构ID  
db.admin_user.updateMany(
    {"organId": {$exists: false}},
    {$set: {"organId": "default_organ_001"}}
);

// 4. 其他实体表类似处理...
```

## 4. 系统配置改造

### 4.1 application.yml配置调整

```yaml
jbs:
  # 支持多机构的Redis配置键前缀
  redisHashKeyPrefix: "config_jbs_jxsr"
  
  # 默认机构配置
  defaultOrganId: "default_organ_001"
  
  # 机构相关配置
  organization:
    # 是否启用多机构模式
    multiTenantEnabled: true
    # 机构代码长度限制
    codeMaxLength: 20
```

### 4.2 CommonConfig配置类扩展

```java
@Configuration
public class CommonConfig {
    // 现有配置...
    
    @Value("${jbs.redisHashKeyPrefix}")
    public String redisHashKeyPrefix;
    
    @Value("${jbs.defaultOrganId}")
    public String defaultOrganId;
    
    @Value("${jbs.organization.multiTenantEnabled:true}")
    public Boolean multiTenantEnabled;
    
    // 构建机构专属Redis键
    public String buildOrganRedisKey(String organId) {
        return redisHashKeyPrefix + ":" + organId;
    }
}
```

## 5. 接口兼容性处理

### 5.1 向后兼容策略

1. **渐进式改造**：保留原有接口，新增机构相关接口
2. **默认机构处理**：未指定机构的请求自动使用默认机构
3. **配置开关**：通过配置控制是否启用多机构模式

### 5.2 API版本管理

```kotlin
// v1版本：兼容现有接口
@RequestMapping(value = ["/v1/user/"])

// v2版本：完整多机构支持
@RequestMapping(value = ["/v2/user/"])
```

## 6. 测试方案

### 6.1 单元测试
- 机构数据隔离测试
- 权限控制测试
- 配置管理测试

### 6.2 集成测试
- 多机构并发访问测试
- 数据一致性测试
- 性能影响测试

### 6.3 迁移测试
- 数据迁移完整性验证
- 业务功能回归测试
- 配置迁移验证

## 7. 风险评估与应对

### 7.1 主要风险
1. **数据迁移风险**：现有数据可能丢失或错误
2. **性能影响**：增加机构过滤可能影响查询性能
3. **兼容性风险**：现有客户端可能无法正常工作

### 7.2 应对措施
1. **完整备份**：迁移前完整备份数据库
2. **分步实施**：分阶段逐步上线新功能
3. **监控告警**：加强系统监控和异常告警
4. **回滚方案**：准备快速回滚机制

## 8. 实施计划

### 8.1 第一阶段：基础改造（2周）
- 实体类添加organId字段
- Repository接口扩展
- 基础数据迁移

### 8.2 第二阶段：业务逻辑改造（3周）
- Model层业务逻辑调整
- 权限控制增强
- 配置管理改造

### 8.3 第三阶段：接口改造（2周）
- Controller层改造
- 认证授权升级
- API兼容性处理

### 8.4 第四阶段：测试上线（1周）
- 全面测试验证
- 生产环境部署
- 监控和优化

## 9. 总结

本改造方案采用渐进式多租户架构，通过为核心实体添加机构标识，实现数据隔离和权限控制。方案具有以下特点：

1. **最小化改动**：在现有架构基础上扩展，减少系统风险
2. **数据安全**：通过机构ID实现严格的数据隔离
3. **向后兼容**：保持现有接口的兼容性
4. **可扩展性**：支持未来更复杂的多租户需求

改造完成后，系统将支持多个机构独立使用，每个机构拥有独立的用户、剧本和配置空间，同时保持良好的性能和安全性。
