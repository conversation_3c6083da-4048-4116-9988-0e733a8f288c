## 241030 需求
一、机构自主运营模式（流水在机构）
1、每个机构自主申请微信商户号
2、APP里面调用机构的商户号进行支付
3、如何确认分润
（1）无订单系统方式
智云鸿道只能定期登录商户号后台确认流水，（万一商户删除流水，智云鸿道就无法查询确认，不知道能不能删）
（2）有订单系统方式
APP支付时，会把记录上传到智云鸿道订单系统，另存一份记录用作查询，更安全。
4、对方机构转账到智云鸿道银行卡

二、智云鸿道大平台模式（流水在智云鸿道）
1、智云鸿道申请一个商户号
2、APP里面调用智云鸿道的商户号进行支付
3、支付时会传输是哪个机构、哪个用户、哪个商品的支付信息到智云鸿道订单系统进行标记存储
4、智云鸿道订单系统计算各个机构的分润
（1）立即分润。
通过订单系统能够知道每笔订单要分给哪个机构，支付成功后立即进行分润转账。a、转账到对方个人微信 b、转账到对方商户号（这种情况，对方机构也要申请一个微信商户号）
（2）定期转账
当某个商户的金额达到一定额度或时间期限时，进行转账。
转账可以
a、转账到对方个人微信 b、转账到对方商户号（这种情况，对方机构也要申请一个微信商户号 c、财务手动转账到对方银行卡

## 241114设计
先把支付简单化，重点是支付后体验和后面的订单管理

怎么收费
    一个用户、激活一个项目收一次费

有几个端，几种使用方法
    1、共用平板。支付二维码、支付、体验、体验完成后自动退出登录或定时自动退出
    2、个人手机。激活

有几个订单系统
    1、每个机构自己的剧本杀后台，供机构自己查询
    2、智云鸿道ERP系统。
        接收所有机构所有项目的激活、付费信息，掌握主动权