# 241120

## 订单推送系统变更日志

### 功能说明
1. 订单推送失败后支持自动重试
2. 使用Redis存储任务状态和重试队列
3. 定时扫描重试队列，自动处理待重试任务
4. 支持手动触发重试功能

### 主要逻辑
1. 订单推送流程：
   - 创建推送任务，生成唯一taskId
   - 首次推送执行
   - 推送成功：更新订单状态，清理Redis数据
   - 推送失败：进入重试队列

2. 重试机制：
   - 失败任务进入Redis重试队列
   - 支持配置多次重试间隔
   - 超过最大重试次数后清理Redis数据
   - 每5秒扫描一次重试队列
   - 使用线程池并发处理重试任务

3. Redis数据结构：
   - 任务详情：`{redisKeyPrefix}:{taskId}` -> JSON字符串
   - 重试队列：`taskQueueKey` -> Sorted Set (score为下次执行时间)

4. 数据清理：
   - 推送成功时清理Redis数据
   - 超过重试次数时清理Redis数据
   - 避免Redis数据持续累积

### 注意事项
1. 重试间隔通过配置文件管理
2. 使用独立线程池处理重试任务
3. 所有关键操作都有日志记录
4. 支持事务处理确保数据一致性

# 241227 江西上饶辛弃疾
- [x] 取消学校名称
- [x] 删除实验码相关逻辑
- [x] 新增开始剧本任务接口