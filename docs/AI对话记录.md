## 增加多机构支持功能 augment 总结
当前是一个剧本杀的java后端程序，代码路径是 src/main，请你阅读所有src/main下面的所有代码，对其进行完整分析和总结，最终输出到docs目录下的一个新md文件中。

### augment 探索增加多机构支持
当前项目是一个单一租户系统，当前仅支持一个机构里面的用户和剧本。现在想要把这个系统进行升级改造，使其支持多个机构，每个机构可以有自己的用户和剧本和剧本记录。
我觉得可以采用如下的思路：
1、查看src/main/kotlin/com/cdzyhd/jbs/entity 这个mongo实体类，里面有管理员后台、用户、剧本、剧本记录等实体定义,每个实体都要支持多机构，所以要有organId。比如AdminUserEntity就要属于某个organId以支持多机构管理登录,UserEntity也要属于某个organId,ScriptEntity也要属于某个organId,但是ScriptRecordEntity又不用属于某个organId,因为它是属于某个用户和某个剧本的。所以你要先理清每个实体类之间的父子依赖关系，然后进行修改。
2、查看src/main/kotlin/com/cdzyhd/jbs/controller/v1 控制器，理解各个接口关系
3、查看src/main/kotlin/com/cdzyhd/jbs/model 理清Controller依赖的Model关系
4、另外要注意 src/main/kotlin/com/cdzyhd/jbs/controller/v1/ConfigController.kt 是读取的redis配置，所以redis配置的has的field也要支持标记organId
5、不要只关注上面我说的，你需要自己根据项目代码进行判断，还需要改哪些地方才能实现多机构支持的需求。

请你分析完上述需求后，总结方案，输出到 docs下的一个新md文件中。

####
1、不需要 NeedOrganPermission这个注解和相关功能
2、src/main/kotlin/com/cdzyhd/jbs/model/TokenModel.kt jwtModel.gen_token("jbs", userId, userId, roleName) 的第三个参考改成organId即可，这样可以在token中记录organId
3、不需要在配置中设置 是否启用多个机构功能，默认就是启用的，不需要设置默认机构 defaultOrganId
4、不需要保留原来接口，直接用新的接口即可，都需要多机构支持
5、不需要测试、不需要回滚

请你根据上述要求，修改方案。目的是最小化开发即可。

### 
很好，请你再次整体理解最终可行的改造方案，并列出todos,然后依次完成所有改造功能，开始吧！

### todo 待修复
非常好，经过修改后的java kotlin后端可以成功运行，现在请你分析经过这样修改后的接口，直接对接没有修改接口的原后台，还需要怎样修改，我们的原则是尽量继续只修改后端代码，就可以兼容原后台。


1、我觉得关键是在必要的接口地方使用 NeedAdminToken这个后台用户的注解或者NeedToken这个前台用户的注解，识别到在用户token中的organId,然后在传入接口中，进行organId相关的筛选。
参考 src/main/kotlin/com/cdzyhd/jbs/interceptor/TokenInterceptor.java中的token验证和organId设置方法，在接口处使用 @NeedAdminToken注解配合@RequestAttribute adminUserId: String获取后台用户id；@NeedToken注解配合@RequestAttribute uid: String获取普通用户userId；@RequestAttribute OrganId: String都可以获取前台或后台用户所属的organId
2、比如 src/main/kotlin/com/cdzyhd/jbs/controller/v1/AdminUserController.kt 这个后台用户接口，adminLogin 管理员登录需要输入机构名称，进行登录；

我们先完成AdminUserController接口的修改，并且对应的修改后台功能
后台路径是 fe-jbs-admin，登录页路径是 fe-jbs-admin/src/views/login/index.vue，依赖的model是fe-jbs-admin/src/model/AdminUserModel.js，依赖的api封装是 fe-jbs-admin/src/api/AdminUserApi.js