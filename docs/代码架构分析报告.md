# 剧本杀后端系统代码架构分析报告

## 项目概述

这是一个基于Spring Boot + Kotlin/Java的剧本杀游戏后端系统，采用MongoDB作为数据存储，Redis作为缓存，支持微信支付集成和AR功能。

### 技术栈
- **框架**: Spring Boot 2.x
- **语言**: Kotlin + Java混合开发
- **数据库**: MongoDB
- **缓存**: Redis
- **构建工具**: Maven/Gradle
- **其他**: JWT认证、EasyAR集成、微信支付

## 项目结构

```
src/main/kotlin/com/cdzyhd/jbs/
├── JbsApplication.kt           # 应用程序入口
├── a/                          # 工具类
├── annotation/                 # 自定义注解
├── aop/                       # AOP切面
├── config/                    # 配置类
├── controller/v1/             # 控制器层
├── entity/                    # 实体类
├── event/                     # 事件处理
├── exception/                 # 异常处理
├── filter/                    # 过滤器
├── interceptor/               # 拦截器
├── model/                     # 业务模型
├── otherEntity/               # 其他实体
├── po/                        # 持久化对象
├── repository/                # 数据访问层
├── service/                   # 服务层
├── util/                      # 工具类
└── vo/                        # 视图对象
```

## 核心模块分析

### 1. 应用程序入口 (JbsApplication.kt)

```kotlin
@SpringBootApplication
@EnableTransactionManagement
@EnableScheduling
class JbsApplication
```

- 启用事务管理
- 启用定时任务调度
- 标准Spring Boot应用入口

### 2. 实体层 (Entity)

#### 核心实体类：

**UserEntity** - 用户实体
- 支持学生和教师角色
- 包含基本用户信息（姓名、账号、邮箱等）
- 密码采用MD5+Salt加密
- 支持逻辑删除

**ScriptEntity** - 剧本实体
- 剧本基本信息（名称、简介、标签）
- 支持地点信息和价格设置
- 关联剧本系列
- 包含评分配置

**ScriptRecordEntity** - 剧本记录实体
- 记录用户游戏过程
- 包含任务进度、得分、用时等信息
- 支持复杂的游戏状态管理

**OrderEntity** - 订单实体
- 支持多种订单状态（待支付、已支付、已取消等）
- 集成微信支付
- 包含订单金额、支付时间等信息

**AdminUserEntity** - 管理员用户实体
- 管理员权限控制
- 支持多平台角色配置
- 扩展信息存储

### 3. 控制器层 (Controller)

采用RESTful API设计，主要控制器包括：

- **UserController**: 用户管理相关API
- **ScriptController**: 剧本管理API
- **ScriptRecordController**: 游戏记录管理API
- **OrderController**: 订单管理API
- **AdminUserController**: 管理员用户API
- **FileController**: 文件上传下载API
- **ARController**: AR功能相关API
- **ToolsController**: 工具类API

### 4. 业务模型层 (Model)

**核心业务模型：**

**UserModel** - 用户业务逻辑
- 用户注册、登录、密码管理
- 邮箱验证和找回密码
- 剧本激活状态管理
- Excel批量导入用户

**OrderModel** - 订单业务逻辑
- 订单创建和状态管理
- 支付成功处理
- 异步订单推送到上游系统
- 支持订单重试机制

**ScriptRecordModel** - 游戏记录业务逻辑
- 游戏记录创建和管理
- 复杂的评分计算算法
- 任务进度跟踪
- 游戏完成度统计

**TokenModel** - 认证令牌管理
- JWT令牌生成和验证
- Redis存储令牌状态
- 支持多角色令牌管理

### 5. 数据访问层 (Repository)

基于Spring Data MongoDB，主要Repository包括：
- UserRepository
- ScriptRepository  
- ScriptRecordRepository
- OrderRepository
- AdminUserRepository
- FileRepository
- SysLogRepository

### 6. 配置管理 (Config)

**CommonConfig** - 通用配置
- Redis配置
- API地址配置
- EasyAR配置
- 订单系统集成配置

**WebMvcConfigurer** - Web配置
- 拦截器配置
- 跨域配置
- 静态资源配置

**MongoConfig** - MongoDB配置
- 数据库连接配置
- 事务支持配置

**RedisConfig** - Redis配置
- 连接池配置
- 序列化配置

### 7. 认证与权限控制

**注解驱动的权限控制：**
- `@NeedToken`: 需要普通用户令牌
- `@NeedAdminToken`: 需要管理员令牌

**TokenInterceptor** - 令牌拦截器
- JWT令牌验证
- 多平台令牌支持
- 自动令牌刷新

### 8. 异常处理

**GlobalExceptionHandler** - 全局异常处理
- 统一异常响应格式
- 自定义业务异常处理
- 参数验证异常处理

**CommonException** - 自定义业务异常
- 错误码和错误信息封装
- 支持国际化

### 9. AOP切面

**SysLogAspect** - 系统日志切面
- 自动记录操作日志
- 支持异步日志处理
- 操作审计功能

### 10. 工具类

**主要工具类：**
- **SnowflakeIdWorker**: 分布式ID生成
- **EmailTools**: 邮件发送工具
- **ImportExcelUtil**: Excel导入工具
- **LogUtil**: 日志工具

## 核心业务流程

### 1. 用户注册流程
1. 用户提交注册信息
2. 系统验证邮箱格式和账号唯一性
3. 发送邮箱验证码
4. 用户验证邮箱后完成注册
5. 生成JWT令牌并存储到Redis

### 2. 剧本激活流程
1. 用户选择剧本
2. 创建订单（待支付状态）
3. 异步推送订单到上游系统
4. 用户完成支付
5. 更新订单状态为已支付
6. 激活用户的剧本权限

### 3. 游戏记录流程
1. 验证剧本激活状态
2. 创建游戏记录
3. 跟踪任务进度和收集线索
4. 记录决策选择
5. 计算最终得分
6. 完成游戏并保存记录

### 4. 评分算法
总分100分，包含三个维度：
- **任务用时(10分)**: 50分钟内得满分，超时按比例扣分
- **线索收集(30分)**: 每缺一条线索扣5分
- **决策选择(60分)**: 每错一次决策扣10分

## 系统特色功能

### 1. AR集成
- 集成EasyAR SDK
- 支持图像识别和跟踪
- 提供AR令牌管理

### 2. 订单重试机制
- 支持订单推送失败重试
- 采用指数退避策略
- 最多重试15次

### 3. 异步任务处理
- 使用Spring的TaskScheduler
- 支持订单推送、邮件发送等异步任务
- 线程池配置优化

### 4. 多平台支持
- 支持多个独立平台的用户系统
- 统一的令牌验证机制
- 跨平台数据隔离

## 配置说明

### 应用配置 (application.yml)
- 服务端口: 9797
- MongoDB连接配置
- Redis连接配置
- 文件存储路径配置
- EasyAR API配置
- 订单系统集成配置

### 关键配置项
```yaml
jbs:
  redisHashKeyName: "config_jbs_jxsr"
  storageFolder: "/disk1/jars/jbs_jxsr/upload/"
  apiUrl: "http://jbsjxsr.cdzyhd.com/api/"
  webUrl: "http://jbsjxsr.cdzyhd.com/"
```

## 数据库设计

### MongoDB集合
- **user**: 用户信息
- **script**: 剧本信息  
- **script_record**: 游戏记录
- **order**: 订单信息
- **admin_user**: 管理员用户
- **file**: 文件信息
- **sys_log**: 系统日志
- **help_question**: 帮助问题

### Redis键设计
- 令牌存储: `jbs~{roleName}~{token}`
- 配置缓存: `config_jbs_jxsr`
- 订单推送队列: `jbs_order:push:queue`

## 安全机制

### 1. 认证机制
- JWT令牌认证
- 令牌过期自动刷新
- Redis存储令牌状态

### 2. 权限控制
- 基于注解的权限控制
- 多级权限管理
- 接口级别的权限验证

### 3. 数据安全
- 密码MD5+Salt加密
- 敏感信息JSON序列化忽略
- 逻辑删除机制

## 性能优化

### 1. 缓存策略
- Redis缓存热点数据
- 配置信息缓存
- 令牌状态缓存

### 2. 异步处理
- 订单推送异步化
- 邮件发送异步化
- 日志记录异步化

### 3. 数据库优化
- MongoDB索引优化
- 分页查询优化
- 聚合查询优化

## 监控与日志

### 1. 系统日志
- 操作日志自动记录
- 异常日志详细记录
- 支持日志级别配置

### 2. 业务监控
- 订单状态监控
- 用户行为跟踪
- 系统性能监控

## 部署架构

### 生产环境配置
- 应用服务器: 端口9797
- MongoDB: 139.159.228.22:27777
- Redis: 139.159.228.22:63636
- 文件存储: /disk1/jars/jbs_jxsr/upload/

### 扩展性设计
- 支持水平扩展
- 无状态应用设计
- 外部化配置管理

## 总结

该剧本杀后端系统是一个功能完整、架构清晰的企业级应用，具有以下特点：

1. **技术栈现代化**: 采用Spring Boot + Kotlin/Java混合开发
2. **架构设计合理**: 分层清晰，职责明确
3. **功能丰富**: 支持用户管理、剧本管理、订单支付、AR集成等
4. **安全性高**: 完善的认证授权机制
5. **性能优化**: 缓存、异步处理等优化措施
6. **可维护性强**: 代码规范，注释完整
7. **扩展性好**: 支持多平台、多租户

系统整体设计体现了现代Web应用的最佳实践，是一个值得学习和参考的项目。
