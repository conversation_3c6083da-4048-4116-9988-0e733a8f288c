## easyAR配置

15天有效期
'crsAppId': '20a1b264e28dfe1741853a23b62d8520',
'token': '
pN9P1fHI83aWWcQhm5D4nY9eCutl12rkGqhNKCU2RqPtCHkdYGlpId+triimiUldPmbvSm/1MkZEGbDGDAMwVbYEXTzIPWSjRCDuv8I2FICH7TQGE7yT9AOu7ji5kPxOCDvTiC6EhhqobTLAXpyp2kp/Lj1qynek5gnTWS/mg5/SbHcsoY/oyuLXLiHnbWYTAiWk+QzoG7SJGvI9+rNuImHKkyor8t90mgJ4Iuq7Yn92PF0CQwvta8faTObGyPj0psbFAGP+9JgIUz0hL0yHvnQiWcTpk6IF6Mr1ywsSNrdQ0ZKetWAcVOxoAF/isugJ5xUKH34SOgqvqU4BsfaoY24+bgvI/Q/Z+KCetleCji3CFiyhXPc+6clOAlCmNoA0g0g8mYXlWRnff9PHzZXUVZ3UyT/qazEzyUYCI9O6B3sFZEOwtpL0X+3gi+ZG4gF4A5RAHBvM37iQyNvgRcY0uA=='
// APIKey+APISecret生成token

nginx 转发需要用 密钥 sense4.1及其之后 的clientUrl 并且proxy_pass 需要 /结尾

识别成功后返回对象

```javascript
target:{
    active: "1"
    allowSimilar: "0"
    appKey: "20a1b264e28dfe1741853a23b62d8520"
    detectableDistinctiveness: 2
    detectableFeatureCount: 4
    detectableFeatureDistribution: 3
    detectableRate: 4
    grade: "4"
    meta: ""
    name: "测试图片"
    size: "5"
    targetId: "4c4c7de3-8e54-4709-ab05-b1f3ca7f572e"
    trackableDistinctiveness: 2
    trackableFeatureCount: 4
    trackableFeatureDistribution: 3
    trackablePatchAmbiguity: 0
    trackablePatchContrast: 0
    trackableRate: 2
    type: "ImageTarget"
}
```

安全保障 useEasyAr 可以获取一个有失效的token

client url 是 client/search
