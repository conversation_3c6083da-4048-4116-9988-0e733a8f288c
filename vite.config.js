import { fileURLToPath, URL } from 'node:url'
import { resolve } from 'path'
import path from 'node:path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite';
import { VantResolver } from 'unplugin-vue-components/resolvers';

import postcsspxtoviewport from 'cnjm-postcss-px-to-viewport'
const pathResolve = (dir) => {
  return resolve(__dirname, ".", dir)
}

const alias = {
  '@': pathResolve("src")
}

// https://vitejs.dev/config/
export default defineConfig({
  // base:"https://resouce.cdzyhd.com/jbs/fe_dist/20230072502/",
  base:"/",
  plugins: [vue(),  Components({
    resolvers: [VantResolver()],
  }),],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    host: '0.0.0.0',
    port: 8848
  },
  css: {
    postcss: {
      plugins: [
        postcsspxtoviewport({
          unitToConvert: "px", // 要转化的单位
          viewportWidth: 375, // UI设计稿的宽度
          unitPrecision: 3, // 转换后的精度，即小数点位数
          propList: ["*"], // 指定转换的css属性的单位，*代表全部css属性的单位都进行转换
          viewportUnit: "vw", // 指定需要转换成的视窗单位，默认vw
          fontViewportUnit: "vw", // 指定字体需要转换成的视窗单位，默认vw
          selectorBlackList: ["ignore","class-ignore"], // 指定不转换为视窗单位的类名，
          minPixelValue: 1, // 默认值1，小于或等于1px则不进行转换
          mediaQuery: true, // 是否在媒体查询的css代码中也进行转换，默认false
          replace: true, // 是否转换后直接更换属性值
          exclude: [], // 设置忽略文件，用正则做目录名匹配
          landscape: false, // 是否处理横屏情况
          // 如果没有使用其他的尺寸来设计，下面这个方法可以不需要，比如vant是375的
          customFun: ({ file }) => {
            let filePath = path.join(file)
            // nutui
            if (filePath.indexOf("node_modules/_@nutui_nutui@3.2.3@@nutui") > -1) {
              return 350;
            }
            if (filePath.indexOf("node_modules/_vant@4.0.0-rc.3@vant") > -1) {
              return 750;
            }
            return 375;
          }
        }),
      ],
    },
    preprocessorOptions: {
      less: {
        modifyVars: {
          hack: `true; @import (reference) "${path.resolve(__dirname, 'src/style/app.less')}";`,  // src/css/common.less 是你需要全局变量 （你定义的定义的方法 和 变量等）
        },
        javascriptEnabled: true
      }
    }
  }
})
