import {getConfig, editConfig} from "@/api/ConfigApi";
import {CONFIG_NAME} from "@/config/main";

/**
 * 配置model
 */
class ConfigModel {
  // 获取配置
  static async getConfig(field) {
    let [data] = await getConfig(CONFIG_NAME, field);
    return data.data;
  }

  // 设置配置
  static async editConfig( field, info) {
    let [data] = await editConfig(CONFIG_NAME, field, info);
    return true;
  }
}

export {ConfigModel}
