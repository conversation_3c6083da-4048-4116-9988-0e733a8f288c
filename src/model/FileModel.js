import {uploadOne, deleteOne, addOrEdit} from "@/api/FileApi";
import FileSaver from 'file-saver';

/**
 * File model
 */
class FileModel {
  // 上传单个文件
  static async uploadOne(file, info) {
    let formData = new FormData()
    formData.append("file", file)
    formData.append("name", file.name)
    formData.append("byteSize", file.size)
    formData.append("fileType", file.type)
    formData.append("lastModified", file.lastModified)
    formData.append("info", info ? JSON.stringify(info) : "{}") // 文件额外信息
    let [data] = await uploadOne(formData)
    if (data.code === "000000") {
      return data.data
    } else {
      return false
    }
  }

  // 删除一个文件
  static async deleteOne(file) {
    let [data] = await deleteOne(
      {fileId: file.fileId}
    )
    if (data.code === "000000") {
      return data.data
    } else {
      return false
    }
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }


  // 前端下载一个文件
  static downloadOneFile(fileUrl, fileName, type, name, id) {
    // 调用下载
    FileSaver.saveAs(fileUrl, fileName);
  }
}

export {FileModel}
