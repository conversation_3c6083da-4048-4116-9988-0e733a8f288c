import {CommonModel} from "@/model/CommonModel";
import {
  getPageList,
  addOrEdit,
  getList,
  getOne,
  deleteOne, resetPwd,
} from "@/api/UserApi";
import axios from "axios";
import {getToken} from "@/utils/auth";
import {msg_err} from "@/utils/ele_component";
import {API_URL} from "@/config/main";

class UserModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      userId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 重置密码
  static async resetPwd(id) {
    let [res] = await resetPwd({
      userId: id
    })
    if (res.code === "000000") {
      return true
    } else {
      return false
    }
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 批量导入账号，列表中账号和邮箱至少需要提供一个
  static importAccounts(file) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      formData.append('startNum', "1")
      axios.create({
        baseURL: API_URL
      }).request({
        url: `v1/user/importAccounts`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data', 'Authorization': "Bearer " + getToken()},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === "000000") {
            resolve(true)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }

}

export {UserModel}
