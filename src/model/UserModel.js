import {msg_err, msg_success} from "../utils/nut_component";
import {
    getInfoByToken,
    loginInPlatform,
    regAccount,
    sendRegEmail,
    changePassword,
    getUserIndexInfo, sendForgetEmail
} from "../api/UserApi";
import Cookies from 'js-cookie'

const TokenKey = 'Jbs-User-Token'
import {MainStore} from "../store/MainStore";
import router from "../router";

class UserModel {
    // 登录判断，如果未登录跳转到登录页
    static loginCheck() {
        if (!this.getToken()) {
            msg_err("尚未登录，请先登录")
            setTimeout(() => {
                router.push("/login")
            }, 2000)

        }
    }

    // 登录平台-邮件
    static async loginInPlatform(username, password) {
        let [res] = await loginInPlatform({
            username,
            password,

        })
        if (res.code === "000000") {
            let result = res.data
            // 保存token到cookie中
            let token = result["token"]
            this.setToken(token)
            // 保存用户信息到store中
            let mainStore = MainStore()
            let userInfo = result["userInfo"]
            mainStore.setUserInfo(userInfo)
            // 删除任务记录id到cookie
            Cookies.remove("Jbs-User-RecordId")
            return true
        } else {
            return false
        }
    }

    // 注册账号
    static async regAccount(regInfo) {
        let [res] = await regAccount(regInfo)
        if (res.code === "000000") {
            return res.data
        }
    }

    // 发送注册邮箱验证码
    static async sendRegEmail(email) {
        let [res] = await sendRegEmail({
            email
        })
        if (res.code === "000000") {
            let result = res.data
            if (result) {
                msg_success(`验证码已发送到${result}中\n请输入邮箱中的6位数字验证码！`)
                return true
            } else {
                msg_success("邮件发送失败！")
                return false
            }
        }
    }

    // 发送忘记密码邮件
    static async sendForgetEmail(email) {
        let [res] = await sendForgetEmail({
            email
        })
        if (res.code === "000000") {
            let result = res.data
            if (result) {
                msg_success(`重置密码邮件已发送到${result}中\n请按照邮件中提示操作！`)
                return true
            } else {
                msg_success("邮件发送失败！")
                return false
            }
        }
    }

    // 通过token获取用户信息
    static async getInfoByToken() {
        let [res] = await getInfoByToken({})
        if (res.code === "000000") {
            // 保存用户信息到store中
            let userInfo = res.data
            let mainStore = MainStore()
            mainStore.setUserInfo(userInfo)
            mainStore.$patch({
                hasLogin: true
            })
            return res.data
        } else {
            return false
        }
    }

    // 获取用户个人中心信息
    static async getUserIndexInfo() {
        let [res] = await getUserIndexInfo({})
        if (res.code === "000000") {
            return res.data
        } else {
            return false
        }
    }

    // 修改密码
    static async changePassword(passwordInfo) {
        let [res] = await changePassword(passwordInfo)
        if (res.code === "000000") {
            return true
        } else {
            return false
        }
    }

    // 退出登录
    static loginOut() {
        this.removeToken()
        Cookies.remove("Jbs-User-RecordId")
        window.location.href = "/"
    }

    // 获取token
    static getToken() {
        return Cookies.get(TokenKey)
    }

    // 设置token
    static setToken(token, loginRole) {
        return Cookies.set(TokenKey, token)
    }

    // 移出token
    static removeToken() {
        return Cookies.remove(TokenKey)
    }
}

export {UserModel}
