/**
 * 定位逻辑
 * 已当前gps点为起点
 * wgs84 坐标系
 * 小区域5*5km近似于二维坐标系？
 * 小米10Ultra空旷环境下 定位经度3.79米
 * 经纬度小数点后8位
 *
 * todo 四个角范围测试
 * 1000*1000演示白色地图
 * 安全区测试
 *
 * 定位精度显示和判断
 * dot的运动方向
 *
 * UI给图的规范
 * 1、矩形 2、正北朝上 3、正确的缩放比例 4、地图的分辨率 5、任务开始点的经纬度 6、矩形4个顶点的经纬度 7、每个任务点的标记 8、任务状态变化标记
 */

class GpsModel {
    // 地图X分辨率 px
    mapXPix = 1000;
    // 地图Y分辨率 px
    mapYPix = 1000;
    // 起始点x坐标 px
    mapXStartPx = 500;
    // 起始点y坐标 px
    mapYStartPx = 500;

    // 地图左上角经纬度
    // mapTopLeftCoordinate = [120, 60]
    mapTopLeftCoordinate = [104.31480, 30.610770]
    // 地图右上角经纬度
    // mapTopRightCoordinate = [150, 60]
    mapTopRightCoordinate = [104.31481, 30.610770]
    // 地图右下角经纬度
    // mapBottomRightCoordinate = [150, 30]
    mapBottomRightCoordinate = []
    // 地图左下角经纬度 坐标系起点
    // mapBottomLeftCoordinate = [120, 30]
    mapBottomLeftCoordinate = []
    // 缩放比例 宽度/经度差
    // zoomScale = 1000 / 0.00003528
    // zoomScale = 1000 / 0.00001 / 15
    zoomScale = 6500000 / 4
    // 当前位置
    positionNow = {};
    // 当前经度
    lonNow = 0;
    // 当前纬度
    latNow = 0;
    // 当前位置点标记element
    dot = undefined;

    // 任务坐标要求坐标
    taskCoordinate = []
    // 距离判断误差 米
    distanceError = 5;
    // 任务编号
    taskId = ""
    // 到达计数
    arrivedTime = 0
    // 是否已到达过
    arrived = false
    // 是否需要判断位置是否达到
    needArrive = false

    /**
     * wgs84 计算两点之间的距离 高精度算法
     * https://www.cnblogs.com/HPhone/archive/2012/12/05/2803672.html
     */
    static wgs84PointsDistance(lon1, lat1, lon2, lat2) {
        function toRad(value) {
            return value * Math.PI / 180;
        }

        var a = 6378137, b = 6356752.3142, f = 1 / 298.257223563;
        var L = toRad(lon2 - lon1);
        var U1 = Math.atan((1 - f) * Math.tan(toRad(lat1)));
        var U2 = Math.atan((1 - f) * Math.tan(toRad(lat2)));
        var sinU1 = Math.sin(U1), cosU1 = Math.cos(U1);
        var sinU2 = Math.sin(U2), cosU2 = Math.cos(U2);
        var lambda = L, lambdaP, iterLimit = 100;
        do {
            var sinLambda = Math.sin(lambda), cosLambda = Math.cos(lambda);
            var sinSigma = Math.sqrt((cosU2 * sinLambda) * (cosU2 * sinLambda) + (cosU1 * sinU2 - sinU1 * cosU2 * cosLambda) * (cosU1 * sinU2 - sinU1 * cosU2 * cosLambda));
            if (sinSigma === 0)
                return 0;
            var cosSigma = sinU1 * sinU2 + cosU1 * cosU2 * cosLambda;
            var sigma = Math.atan2(sinSigma, cosSigma);
            var sinAlpha = cosU1 * cosU2 * sinLambda / sinSigma;
            var cosSqAlpha = 1 - sinAlpha * sinAlpha;
            var cos2SigmaM = cosSigma - 2 * sinU1 * sinU2 / cosSqAlpha;
            if (isNaN(cos2SigmaM))
                cos2SigmaM = 0;
            var C = f / 16 * cosSqAlpha * (4 + f * (4 - 3 * cosSqAlpha));
            lambdaP = lambda;
            lambda = L + (1 - C) * f * sinAlpha * (sigma + C * sinSigma * (cos2SigmaM + C * cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM)));
        } while (Math.abs(lambda - lambdaP) > (1e-12) && --iterLimit > 0);
        if (iterLimit === 0) {
            return NaN
        }
        var uSq = cosSqAlpha * (a * a - b * b) / (b * b);
        var A = 1 + uSq / 16384 * (4096 + uSq * (-768 + uSq * (320 - 175 * uSq)));
        var B = uSq / 1024 * (256 + uSq * (-128 + uSq * (74 - 47 * uSq)));
        var deltaSigma = B * sinSigma * (cos2SigmaM + B / 4 * (cosSigma * (-1 + 2 * cos2SigmaM * cos2SigmaM) - B / 6 * cos2SigmaM * (-3 + 4 * sinSigma * sinSigma) * (-3 + 4 * cos2SigmaM * cos2SigmaM)));
        var s = b * A * (sigma - deltaSigma);
        var fwdAz = Math.atan2(cosU2 * sinLambda, cosU1 * sinU2 - sinU1 * cosU2 * cosLambda);
        var revAz = Math.atan2(cosU1 * sinLambda, -sinU1 * cosU2 + cosU1 * sinU2 * cosLambda);
        return s;
    }
}

export {GpsModel}
