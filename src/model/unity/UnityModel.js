import $ from "jquery"

class UnityModel {
    // 加载unity 资源-2022
    static async loadWebGlResource2022(loaderPath) {
        return new Promise(function (resolve, reject) {
            let script1 = document.createElement('script');
            script1.id = btoa("webgl_UnityLoader.js_" + loaderPath).replaceAll("=", "")
            script1.src = loaderPath;
            document.body.append(script1);
            script1.addEventListener('load', e => {
                console.log(loaderPath + " webgl loaded")
                resolve(true)
            });
        });
    }

    // 载入unity 资源-2019
    static async loadWebGlResource2019(path) {
        return new Promise(function (resolve, reject) {
            let css1 = document.createElement('link');
            css1.rel = 'stylesheet';
            css1.id = btoa("webgl_style.css_" + path).replaceAll("=", "")
            css1.type = "text/css"
            css1.href = path + 'TemplateData/style.css';
            document.body.append(css1);
            css1.addEventListener('load', e => {
                let script1 = document.createElement('script');
                script1.src = path + 'TemplateData/UnityProgress.js';
                script1.id = btoa("webgl_UnityProgress.js_" + path).replaceAll("=", "")
                document.body.append(script1);
                script1.addEventListener('load', e => {
                    let script2 = document.createElement('script');
                    script2.id = btoa("webgl_UnityLoader.js_" + path).replaceAll("=", "")
                    script2.src = path + 'Build/UnityLoader.js';
                    document.body.append(script2);
                    script2.addEventListener('load', e => {
                        console.log(path + " webgl loaded")
                        resolve(true)
                    });
                });
            });
        });
    }

    // 删除unity资源-2019
    static removeUnityResource2019(path) {
        $(`#${btoa("webgl_style.css_" + path).replaceAll("=", "")}`).remove()
        $(`#${btoa("webgl_UnityProgress.js_" + path).replaceAll("=", "")}`).remove()
        $(`#${btoa("webgl_UnityLoader.js_" + path).replaceAll("=", "")}`).remove()
    }

    // 删除unity资源-2022
    static removeUnityResource2022(path) {
        $(`#${btoa("webgl_UnityLoader.js_" + path).replaceAll("=", "")}`).remove()
    }

    // 调用某个unity方法
    /**
     * todo 判断可以在运行时使用 SystemInfo.graphicsDeviceType 来确定 Unity 实例是使用 OpenGLES3 (WebGL2.0) 还是 OpenGLES2 (WebGL1.0) 进行渲染。
     * todo 性能注意事项 https://docs.unity.cn/cn/2019.4/Manual/webgl-performance.html
     * https://docs.unity.cn/cn/2021.2/Manual/webgl-interactingwithbrowserscripting.html
     */
    static callUnityMethod(unityInstance, sceneName, methodName, paramString) {
        unityInstance.SendMessage(sceneName, methodName, paramString);
    }
}

export {UnityModel}
