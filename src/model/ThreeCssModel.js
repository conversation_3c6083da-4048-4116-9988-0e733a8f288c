import $ from "jquery";

class ThreeCssModel {
    // 后台播放声音
    static playBgSound(url) {
        return new Promise((resolve, reject) => {
            let strAudio = "<audio id='audioPlay' src='" + url + "' hidden='true'>";
            $("body").append(strAudio);
            let audio = document.getElementById("audioPlay");
            audio.oncanplay = function () {
                audio.play();
                resolve([audio, audio.duration])
            }
        })
    }
}

export {ThreeCssModel}
