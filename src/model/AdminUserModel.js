import {
  login,
  loginOut,
  getMineInfo,
  getUserList,
  add,
  edit,
  deleteOne,
  changePassword
} from '@/api/AdminUserApi'
import Cookies from 'js-cookie'

/**
 * 管理员用户管理model
 */
class AdminUserModel {
  // 用户登录
  static async userLogin(account, password) {
    let [data] = await login({
      "loginType": "username",
      account,
      password
    }).catch(res => {

    })

    if (data.code === "000000") {
      return data.data;
    } else {

      return false;
    }
  }

  // 判断已登录用户自己的用户信息
  static async getMineInfo(loginRole) {
    let [data] = await getMineInfo({loginRole})
    if (data.code === "000000") {
      return data.data;
    } else {
      return false;
    }
  }

  // 新增
  static async add(entity) {
    let [data] = await add(entity);
    return data;
  }

  // 修改
  static async edit(entity) {
    let [data] = await edit(entity);
    return data;
  }

  // 修改用户密码
  static async changePassword(newPassword, role) {
    let [data] = await changePassword({
      newPassword,
      role,
    });
    return data;
  }

  // 获取不分页列表
  static async getUserList(document) {
    let [data] = await getUserList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(adminUserId) {
    let [data] = await deleteOne({
      adminUserId
    });
    return data;
  }

}

export {AdminUserModel}
