import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList, getOne, deleteOne, getOneScriptRecordList} from "@/api/ScriptRecordApi";
import {dateFormat, scoreFormat, scoreUseTimeFilter} from "@/filters";
import {excel_export_from_json} from "@/utils/excel";

class ScriptRecordModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      scriptId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }


  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取某个剧本的剧本记录
  static async getOneScriptRecordList(page, size, sort, document) {
    let [data] = await getOneScriptRecordList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 导出开放脚本记录列表
  static async exportRecordList(startNumber, endNumber, query) {
    // 获取课程详情
    let scriptName = "辛弃疾";
    // 计算
    let skipNumber = startNumber - 1
    let limitNumber = endNumber - skipNumber;
    // 获取列表
    query["skipNumber"] = skipNumber
    query["limitNumber"] = limitNumber
    let [list,] = await ScriptRecordModel.getOneScriptRecordList(-1, 20, "", query)

    // 导出Excel 数据字段和表头
    const filter = ["scriptName", "userEmail", "score", "startTime", "endTime", "usedTime", "completed", "taskNumber", "decisionNumber", "clueNumber"];
    const header = ["剧本", "用户邮箱/账号", "得分", "开始时间", "结束时间", "用时", "是否已完成", "完成任务数量", "完成决策数量", "获取线索数量"];
    // map reduce生成arr
    let formatJson = function (filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        let value = '';
        switch (j) {
          case "scriptName":
            value = scriptName
            break
          case "userEmail":
            value = v["userEntity"][0]["email"] ? v["userEntity"][0]["email"] : v["userEntity"][0]["account"]
            break
          case "score":
            value = scoreFormat(v["score"])
            break
          case "completed":
            value = v[j] ? "是" : "否"
            break
          case "startTime":
            value = dateFormat(v["startTime"])
            break
          case "endTime":
            value = dateFormat(v["endTime"])
            break
          case "usedTime":
            value = v[j] ? scoreUseTimeFilter(v["usedTime"]) : ""
            break
          default:
            value = v[j]
        }
        return value
      }))
    }
    excel_export_from_json(list, header, filter, formatJson, `${scriptName}第${startNumber}条到第${endNumber}剧本列表_${dateFormat(new Date())}`)
  }
}

export {ScriptRecordModel}
