import {getUserTaskList, getUserOneRecordInfo, updateRecordInfo} from "@/api/ScriptRecordApi";
import {TaskStore} from "../store/TaskStore";
import Cookies from 'js-cookie'
import router from "../router/index"
import {TaskModel} from "./TaskModel";
import {getRecordInfo, restartScript, startNewScript} from "../api/ScriptRecordApi";
import {msg_confirm, msg_success} from "../utils/nut_component";
import fdldqTaskConfig from "../scripts/fdldq/taskConfig";

class ScriptRecordModel {
    // 获取我的任务列表
    static async getUserTaskList() {
        let [data] = await getUserTaskList({});
        return data.data;
    }

    // 保存本次任务信息到cookie
    static saveRecordInfoToCookie(recordId) {
        // 保存任务记录id到cookie
        Cookies.set("Jbs-User-RecordId", recordId)
    }

    // 从cookie中获取记录的recordId
    static getRecordIdFromCookie() {
        return Cookies.get("Jbs-User-RecordId")
    }

    // 如果不在剧本中就跳转回主界面
    static ifNotScriptToJump() {
        if (this.getRecordIdFromCookie()) {
            return true
        } else {
            router.push({
                name: "Hall",
                query: {}
            })
            return false
        }
    }

    // 初始化任务记录
    static async initRecord(recordInfo) {
        let taskStore = TaskStore()
        // 保存id到cookie
        this.saveRecordInfoToCookie(recordInfo["scriptRecordId"])
        await taskStore.$patch({// todo 任务步骤参数可以参考大平台
            startTime: recordInfo["startTime"],
            recordId: recordInfo["scriptRecordId"],
            nowTaskId: recordInfo["nowTaskId"],
            nextTaskId: recordInfo["nextTaskId"],
            recordInfo: recordInfo["recordInfo"], // patch设置object时，会合并同字段
            roleId: recordInfo["roleId"],
        })
        // 计算任务统计
        await this.calStatisticNumber()
        return true
    }

    // 通过记录id获取用户某个记录信息
    static async getUserOneRecordInfo(recordId) {
        let [res] = await getUserOneRecordInfo({
            scriptRecordId: recordId
        }).catch(err => {
            Cookies.remove("Jbs-User-RecordId")
        });
        if (res.code === "000000") {
            return res.data
        } else {
            Cookies.remove("Jbs-User-RecordId")
            return false
        }
    }

    // 获取用户某个记录信息并存入store
    static async getUserOneRecordInfoSaveToStore(recordId) {
        let recordInfo = await this.getUserOneRecordInfo(recordId)
        if (recordInfo.completed !== true) {// 任务在进行中
            await this.initRecord(recordInfo)
        } else {// 任务已完成
            await this.endScript()
        }
    }

    // 更新某个记录信息
    static async updateRecordInfo(method, info) {
        let [res] = await updateRecordInfo(this.getRecordIdFromCookie(), method, info);
        if (res.code === "000000") {
            let savedRecordInfo = res.data
            // 保存到store
            let taskStore = TaskStore()
            taskStore.setRecordInfo(savedRecordInfo)
            return true
        } else {
            return false
        }
    }

    // 获取某个记录信息
    static async getRecordInfo(method, info) {
        let [res] = await getRecordInfo(this.getRecordIdFromCookie(), method, info);
        if (res.code === "000000") {
            let getInfo = res.data
            return getInfo
        } else {

            return false
        }
    }

    // 计算并更新剧本统计状态
    static async calStatisticNumber() {
        let taskStore = TaskStore()
        let recordInfo = taskStore.recordInfo
        let gotTaskNumber = 0
        let gotClueNumber = 0
        let gotDecisionNumber = 0
        for (let i in recordInfo) {
            let taskInfo = recordInfo[i]
            if (!i.startsWith("S")) { // 如果不是系统任务
                gotTaskNumber += 1
            }
            gotClueNumber += taskInfo["clueList"].length // 计算已获取线索数量
            gotDecisionNumber += taskInfo["selectQuestionAnswerList"].length // 计算已决策数量
        }
        taskStore.$patch({
            gotTaskNumber: gotTaskNumber,
            gotClueNumber: gotClueNumber,
            gotDecisionNumber: gotDecisionNumber,
        })
    }

    // 开始剧本
    static async startScript() {
        await TaskModel.initOneTask("S1") // 初始化

        await msg_confirm("测试模式", `为了方便您进行快速体验，当前为测试模式，所有需要定位、扫码的地方都可以根据提示跳过！请切换到横屏使用本系统。`)
        router.push("/fdldq/start")
        //await TaskModel.goToOneTaskPage("S1") // 进入
    }

    // 结束剧本
    static async endScript() {
        // 删除任务记录id到cookie
        Cookies.remove("Jbs-User-RecordId")
        // 跳转到我的任务页
        router.push({
            name: "myTask",
            query: {}
        })
    }

    // 继续某个剧本
    static async continueScript(scriptRecordId) {
        // todo 后台是否需要记录 长时间未完成 继续的时间问题
        Cookies.set("Jbs-User-RecordId", scriptRecordId)
        // 获取剧本详情
        let recordInfo = await this.getUserOneRecordInfo(scriptRecordId)
        // let nowTime = new Date().getTime();
        // let startTime = recordInfo.startTime();
        // if(nowTime-startTime>1*24*3600){
        //
        // }
        // 判断记录状态
        if (recordInfo.nowTaskId === "S1") {// 如果是选角色界面
            await this.startScript();
        } else {
            window.location.href = "/ar_main"
        }
    }

    // 重新做某个剧本
    /**
     * todo 当前逻辑，重新开一份新记录
     * 可能逻辑，删除原来记录
     */
    static async restartScript(scriptRecord) {
        let scriptId = scriptRecord.scriptId
        let [recordResult] = await restartScript({
            scriptId
        })  // 初始化任务记录信息
        await ScriptRecordModel.initRecord(recordResult.data)
        msg_success("重新开始剧本任务成功！请稍等，正在进入剧本!");
        setTimeout(async () => {
            await ScriptRecordModel.startScript()
        }, 1000)
    }

    // 开始一个新剧本
    static async startNewScript(scriptId) {
        let [recordResult] = await startNewScript({
            scriptId
        })  // 初始化任务记录信息
        await ScriptRecordModel.initRecord(recordResult.data)
        msg_success("开始剧本任务成功！请稍等，正在进入剧本!");
        setTimeout(async () => {
            await ScriptRecordModel.startScript()
        }, 1000)
    }
}

export {ScriptRecordModel}
