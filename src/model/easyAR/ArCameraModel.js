// AR摄像头model
import $ from "jquery";

class ArCameraModel {
    constructor(containerId) {
        this.containerId = containerId
        this.container = document.querySelector('#' + containerId);
        this.initVideo()
    }

    /**
     * 尝试打开摄像头
     */
    async openCameraInPage() {
        let option = {audio: false, video: {facingMode: {exact: 'environment'}}}
        return new Promise((resolve, reject) => {
            this.openCamera(option).then(() => {
                resolve("camera_open_success")
            }).catch(err => {
                console.error(err);
                if (err.message === "Requested device not found") {
                    alert("未找到摄像头!");
                } else {
                    alert(`摄像头打开失败\n${err}`);
                }
                resolve("camera_open_fail")
            });
        })
    }

    // 关闭摄像头
    closeCamera() {
        if (this.videoElement && this.videoElement.srcObject) {
            this.videoElement.srcObject.getTracks().forEach(track => {
                console.info('stop camera');
                track.stop();
            });
        }
    }

    /**
     * 打开摄像头
     * 摄像头设置参数请查看： https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints
     * @returns {Promise<T>}
     * @param constraints
     */
    openCamera(constraints) {
        // 如果已打开摄像头，则需要先关闭。
        this.closeCamera();
        return new Promise((resolve, reject) => {
            navigator.mediaDevices.getUserMedia(constraints).then(stream => {
                this.videoElement.srcObject = stream;
                this.videoElement.style.display = 'block';
                this.videoElement.style.objectFit = "cover"
                this.videoElement.play().then(() => {
                }).catch(err => {
                    console.error(`摄像头视频绑定失败\n${err}`);
                    reject(err);
                });
                this.videoElement.onloadedmetadata = () => {
                    const cameraSize = {
                        width: this.videoElement.offsetWidth,
                        height: this.videoElement.offsetHeight
                    };
                    console.info(`camera size ${JSON.stringify(cameraSize)}`);
                    // 简单处理横/竖屏
                    if (window.innerWidth < window.innerHeight) {
                        // 竖屏
                        if (cameraSize.height < window.innerHeight) {
                            this.videoElement.setAttribute('height', `${window.innerHeight}px`);
                        }
                    } else {
                        // 横屏
                        if (cameraSize.width < window.innerWidth) {
                            this.videoElement.setAttribute('width', `${window.innerWidth}px`);
                        }
                    }
                    resolve(true);
                };
            }).catch(err => {
                reject(err);
            });
        });
    }

    /**
     * 创建视频详情元素，播放摄像头视频流
     */
    initVideo() {
        let videoElement = $(`#${this.containerId} #arVideoBackground`);
        if (videoElement.length !== 0) {
            console.log(videoElement)
            this.videoElement = videoElement[0]
        } else {
            this.videoElement = document.createElement('video');
        }
        this.videoElement.setAttribute('id', 'arVideoBackground');
        this.videoElement.setAttribute('playsinline', 'playsinline');
        this.videoElement.setAttribute('width', `${window.innerWidth}px`);
        this.videoElement.setAttribute('height', `${window.innerHeight}px`);
        this.container.appendChild(this.videoElement);
        console.log("初始化视频背景成功")
    }
}

export {ArCameraModel}
