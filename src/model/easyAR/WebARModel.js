/**
 * WebAR基础类
 * 摄像头设置参数请查看： https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints
 * 如果打开摄像头后，播放视频有卡顿，请尝试设置 frameRate，height与width
 */
import {ToolsModel} from "../ToolsModel";
import {ScriptModel} from "../ScriptModel";
import {API_URL} from "../../config/main";
import $ from 'jquery'

class WebARModel {
    /**
     * 初始化Web AR
     * @param interval 识别间隔(毫秒)
     * @param recognizeUrl 识别服务地址
     * @param token 用token认证识别
     * @param containerId webAR运行需要的容器id
     */
    constructor(interval, recognizeUrl, token, containerId = null) {
        this.isRecognizing = false;
        this.interval = interval;
        this.recognizeUrl = recognizeUrl;
        this.token = token;
        this.containerId=containerId
        this.container = document.querySelector('#' + containerId);
        this.initVideo();
        this.initCanvas();
    }

    // 关闭摄像头
    closeCamera() {
        if (this.videoElement && this.videoElement.srcObject) {
            this.videoElement.srcObject.getTracks().forEach(track => {
                console.info('stop camera');
                track.stop();
            });
        }
    }

    /**
     * 打开摄像头
     * 摄像头设置参数请查看： https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints
     * @returns {Promise<T>}
     * @param constraints
     */
    openCamera(constraints) {
        // 如果已打开摄像头，则需要先关闭。
        this.closeCamera();
        return new Promise((resolve, reject) => {
            navigator.mediaDevices.getUserMedia(constraints).then(stream => {
                this.videoElement.srcObject = stream;
                this.videoElement.style.display = 'block';
                this.videoElement.style.objectFit = "contain"
                this.videoElement.play().then(() => {
                }).catch(err => {
                    console.error(`摄像头视频绑定失败\n${err}`);
                    reject(err);
                });
                this.videoElement.onloadedmetadata = () => {
                    const cameraSize = {
                        width: this.videoElement.offsetWidth,
                        height: this.videoElement.offsetHeight
                    };
                    console.info(`camera size ${JSON.stringify(cameraSize)}`);
                    // 简单处理横/竖屏
                    if (window.innerWidth < window.innerHeight) {
                        // 竖屏
                        if (cameraSize.height < window.innerHeight) {
                            this.videoElement.setAttribute('height', `${window.innerHeight}px`);
                        }
                    } else {
                        // 横屏
                        if (cameraSize.width < window.innerWidth) {
                            this.videoElement.setAttribute('width', `${window.innerWidth}px`);
                        }
                    }
                    resolve(true);
                };
            }).catch(err => {
                reject(err);
            });
        });
    }

    /**
     * 截取摄像头图片
     * @returns {string}
     * todo 视频截图方式，object-fit的适应
     * 要在各个设备上表现一致 统一canvas和video的大小实现了
     */
    captureVideo() {
        this.canvasElement = document.querySelector('#scanCanvas');
        this.canvasElement.setAttribute('width', `${this.videoElement.offsetWidth}px`);
        this.canvasElement.setAttribute('height', `${this.videoElement.offsetHeight}px`);
        this.canvasContext.drawImage(this.videoElement, window.innerWidth * 0.2, 0, this.videoElement.offsetWidth * 0.6, this.videoElement.offsetHeight);
        // this.canvasContext.drawImage(this.videoElement, 0, 0, this.videoElement.offsetWidth, this.videoElement.offsetHeight);
        return this.canvasElement.toDataURL('image/jpeg', 0.8).split('base64,')[1];
    }

    /**
     * 创建视频详情元素，播放摄像头视频流
     */
    initVideo() {
        let videoElement=$(`#${this.containerId} #scanVideo`);
        if(videoElement.length!==0){
            console.log(videoElement)
            this.videoElement=videoElement[0]
        }else{
            this.videoElement = document.createElement('video');
        }
        this.videoElement.setAttribute('id', 'scanVideo');
        this.videoElement.setAttribute('playsinline', 'playsinline');
        this.videoElement.setAttribute('width', `${window.innerWidth}px`);
        this.videoElement.setAttribute('height', `${window.innerHeight}px`);
        this.container.appendChild(this.videoElement);
        console.log("初始化视频播放成功")
    }

    /**
     * 创建canvas，截取摄像头图片时使用
     */
    initCanvas() {
        let canvasElement=document.querySelector("#scanCanvas")
        if(canvasElement){
            this.canvasElement=canvasElement
        }else{
            this.canvasElement = document.createElement('canvas');
        }
        this.canvasElement.setAttribute("id", "scanCanvas")
        this.canvasElement.setAttribute("style", "display:none")
        // this.canvasElement.setAttribute('width', `${window.innerWidth}px`);
        this.canvasElement.setAttribute('width', `${this.videoElement.offsetWidth}px`);
        this.canvasElement.setAttribute('height', `${this.videoElement.offsetHeight}px`);
        this.canvasContext = this.canvasElement.getContext('2d');
        document.body.appendChild(this.canvasElement);
    }

    /**
     * AR识别
     * @param successCallback 识别成功的回调
     * @param errCallback 识别失败的回调
     */
    startRecognize(successCallback,errCallback) {
        this.timer = window.setInterval(() => {
            console.log("正在识别")
            // 等待上一次识别结果
            if (this.isRecognizing) {
                return;
            }
            this.isRecognizing = true;
            // 从摄像头中抓取一张图片
            const image = {image: this.captureVideo(), notracking: true, appId: this.token.crsAppId};
            // 发送到服务器识别
            this.httpPost(image).then((msg) => {
                this.stopRecognize();
                successCallback(msg);
            }).catch((err) => {
                this.isRecognizing = false;
                errCallback(err)
            });
        }, this.interval);
    }

    /**
     * 识别二维码
     * @param callback
     */
    startRecognizeQrCode(successCallback,errCallback) {
        this.isRecognizingQrcode = false;
        this.timerQrCode = window.setInterval(() => {
            // 等待上一次识别结果
            if (this.isRecognizingQrcode) {
                return;
            }
            this.isRecognizingQrcode = true;
            // 从摄像头中抓取一张图片
            this.canvasElement = document.querySelector('#scanCanvas');
            this.canvasElement.setAttribute('width', `${this.videoElement.offsetWidth}px`);
            this.canvasElement.setAttribute('height', `${this.videoElement.offsetHeight}px`);
            this.canvasContext.drawImage(this.videoElement, window.innerWidth * 0.2, 0, this.videoElement.offsetWidth * 0.6, this.videoElement.offsetHeight);
            let $this = this;
            this.canvasElement.toBlob(function (blob) {
                const imageFile = new window.File([blob], "qrCode.png", {type: "image/png"});
                ToolsModel.decodeQrCode(imageFile).then(res => {
                    if (res !== "notMatch") { // 识别成功
                        $this.stopRecognize()
                        successCallback(res)
                    } else {// 识别失败
                        $this.isRecognizingQrcode = false;
                        errCallback(res)
                    }
                })
            }, "image/png")

        }, this.interval);
    }

    /**
     * 停止识别
     */
    stopRecognize() {
        this.isRecognizing = false;
        if (this.timer) {
            window.clearInterval(this.timer);
        }
        if (this.timerQrCode) {
            window.clearInterval(this.timerQrCode);
        }
    }

    // 请求
    httpPost(data) {
        return fetch(this.recognizeUrl, {
            method: 'POST',
            body: JSON.stringify(data),
            headers: {
                'Content-Type': 'application/json;Charset=UTF-8',
                'Authorization': this.token.token
            }
        }).then(res => res.json()).then(data => {
            var _a;
            if (!data || data.statusCode != 0) {
                console.error(data);
                return Promise.reject(((_a = data === null || data === void 0 ? void 0 : data.result) === null || _a === void 0 ? void 0 : _a.message) || 'ERROR');
            }
            return Promise.resolve(data.result);
        });
    }
}

export {WebARModel}
