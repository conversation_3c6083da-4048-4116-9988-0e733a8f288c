import {WebARModel} from "./WebARModel";
import {EasyARModel} from "./EasyARModel";

// 图形识别url
const clientEndUrl = "https://arjbs.xhyjbj.com/client/search";
// const clientEndUrl = "https://e4cd99a68cc8969fac820d94557c6b34.cn1.crs.easyar.com:8443/search";

class ARAPPModel {
    constructor(containerId, purpose, needCaptureImg) {
        // 基础配置
        this.openCameraBtnId = "openCameraBtn" // 打开摄像头按钮
        this.startArScanBtnId = "startArScanBtn" // 开始扫描识别AR按钮
        this.startQrScanBtnId = "startQrScanBtn" // 开始扫描识别Qr按钮
        this.stopScanBtnId = "stopScanBtn" // 停止识别按钮
        this.needCaptureImg = needCaptureImg ? needCaptureImg : false
        this.containerId = containerId
        this.purpose = purpose
        // 摄像头设置参数请查看： https://developer.mozilla.org/en-US/docs/Web/API/MediaTrackConstraints
        // 预置打开摄像头的参数
        this.cameras = [
            {label: '默认摄像头', value: {audio: false, video: true}},
            {label: '前置摄像头', value: {audio: false, video: {facingMode: {exact: 'user'}}}},
            {label: '后置摄像头', value: {audio: false, video: {facingMode: {exact: 'environment'}}}}
        ];
        // 识别成功后的回调
        this.callback = null;
        this.clientEndUrl = clientEndUrl;
        // this.listAllCamera(); // 不展示摄像头，默认直接使用后置摄像头
        this.initEvent();
        this.useEasyAr();
    }

    /**
     * 初始化WebAR对象
     * @param token 认证token
     */
    setToken(token) {
        this.webAR = new WebARModel(2000, this.clientEndUrl, token, document.querySelector('#' + this.containerId));
    }

    /**
     * 使用集成方法获取token及初始化WebAR对象
     */
    async useEasyAr() {
        EasyARModel.getEasyARToken().then(res => {
            this.setToken(res);
        })
    }

    // 显示所有摄像头
    listAllCamera() {
        this.cameraElement = document.querySelector('#videoDevice');
        this.cameras.forEach((item, index) => this.cameraElement.appendChild(new Option(item.label, `${index}`)));
    }

    show(target) {
        document.querySelector(`#${target}`).classList.remove('none');
    }

    hide(target) {
        document.querySelector(`#${target}`).classList.add('none');
    }

    /**
     * 尝试打开摄像头
     * @param p 摄像头参数
     */
    openCamera(p) {
        // this.stopRecognize();
        this.webAR.openCamera(p).then(() => {
            this.hide(this.openCameraBtnId)
            if (this.purpose === "ar") { // ar识别
                this.show(this.startArScanBtnId);
               if(this.needCaptureImg){
                   this.show("captureVideoBtn")
               }
            }
            if (this.purpose === "qr") {// qr识别
                this.show(this.startQrScanBtnId);
            }
            this.show(this.stopScanBtnId);
        }).catch(err => {
            console.error(err);
            alert(`摄像头打开失败\n${err}`);
        });
    }

    // 初始化事件
    initEvent() {
        // 打开设备上的摄像头
        document.querySelector(`#${this.openCameraBtnId}`).addEventListener('click', () => {
            // this.openCamera(this.cameras[this.cameraElement.value].value);
            this.openCamera({audio: false, video: {facingMode: {exact: 'environment'}}})
            // this.openCamera({audio: false, video: {audio: false, video: true}})
        });
        // 开启识别
        if (this.purpose === "ar") {
            document.querySelector(`#${this.startArScanBtnId}`).addEventListener('click', () => {
                // todo: 清除已渲染的内容(视业务需求)
                this.show('scanLine');
                this.hide(this.startArScanBtnId);
                this.webAR.startRecognize((msg) => {
                    this.stopRecognize();
                    this.show(this.startArScanBtnId);
                    if (this.callback) {
                        this.callback(msg);
                    }
                });
            }, false);
        }
        if (this.purpose === "qr") {
            // 开启识别 二维码
            document.querySelector('#startQrCodeScan').addEventListener('click', () => {
                // todo: 清除已渲染的内容(视业务需求)
                this.show('scanLine');
                this.hide(this.startQrScanBtnId);
                this.show(this.startQrScanBtnId);
                this.webAR.startRecognizeQrCode((msg) => {
                    if (this.callback) {
                        if (msg !== "识别失败") { // 识别成功
                            this.stopRecognize()
                            this.show(this.startQrScanBtnId);

                        } else {// 识别失败

                        }
                        this.callback(msg);
                    }
                });
            }, false);
        }
        // 停止识别
        document.querySelector(`#${this.stopScanBtnId}`).addEventListener('click', () => {
            this.stopRecognize();
            this.hide(this.startArScanBtnId);
            this.hide(this.stopScanBtnId);
            document.getElementById("scanVideo").setAttribute("style","display:none")
            this.webAR.closeCamera();
        }, false);

       if(this.needCaptureImg){
           document.querySelector(`#captureVideoBtn`).addEventListener('click', () => {
               console.log(document.querySelector("#scanVideo"))
               let data = "data:image/jpg;base64," + this.webAR.captureVideo()
               document.querySelector("#canvas-img").setAttribute("src", data)
               this.show("canvas-img")
           }, false);
       }
    }

    stopRecognize() {
        this.hide('scanLine');
        this.hide(this.startArScanBtnId);
        this.webAR.stopRecognize();
    }
}

export {ARAPPModel}
