import {getEasyARToken} from "../../api/ARApi";
import {WebARModel} from "./WebARModel";
import {EASY_AR_URL} from "../../config/main";

class EasyARModel {
    // 获取token
    static async getEasyARToken() {
        let [res] = await getEasyARToken({})
        if (res.code === "000000") {
            return res.data
        } else {
            return false
        }
    }


    // 获取一个easyAR实例
    static async getEasyArInstance(containerId) {
        let token = await this.getEasyARToken()
        return new WebARModel(2000, EASY_AR_URL, token, containerId)
    }

    // 获取一个QR实例
    static async getQRInstance(containerId) {
        return new WebARModel(500, EASY_AR_URL, "", containerId)
    }
}

export {EasyARModel}

// 横竖屏检测方法，当改变
function onWindowResize() {
    // window.addEventListener('resize', this.onWindowResize)
    setTimeout(() => {
        this.clientHeight = document.getElementById('app').clientHeight
        this.clientWidth = document.getElementById('app').clientWidth
        setTimeout(() => { // ios需要延时判断
            alert(window.orientation)
            if (window.orientation === 90 || window.orientation === -90) {
                alert("横屏")
            } else {
                // 竖屏
                alert("竖屏")
            }
        }, 500)
    }, 0)
}


