import {msg_err} from '@/utils/ele_component'

class BaseUploadModel {
  // 数组转为upload数组
  static arrayToUploadArray(arr) {
    let arr_return = []
    arr.forEach(function (item) {
      arr_return.push({
        url: item
      })
    })
    return arr_return
  }

  // upload数组转为普通数组
  static uploadArrayToArray(arr) {
    let arr_return = []
    arr.forEach(function (item) {
      arr_return.push(item.url)
    })
    return arr_return
  }

  // 七牛云不同文件存不同bucket对应的域名
  static getBucketDomain(file) {
    let domain = {
      "image": "http://image.cdzyhd.com",
      "video": "http://audio.cdzyhd.com",
      "audio": "http://audio.cdzyhd.com",
      "other": "http://resouce.cdzyhd.com",
    }
    let fileType = this.getFileType(file)[0]
    let returnDomain = "";
    switch (fileType) {
      case "image":
        returnDomain = domain["image"];
        break;
      case "video":
        returnDomain = domain["video"];
        break;
      case "audio":
        returnDomain = domain["audio"];
        break;
      default:
        returnDomain = domain["other"];
        break;
    }
    returnDomain = domain["other"]
    return returnDomain
  }


  // 获取随机文件名
  static getUuid() {
    let s = []
    let hexDigits = '0123456789abcdef'
    for (let i = 0; i < 36; i++) {
      s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
    }
    s[14] = '4'  // bits 12-15 of the time_hi_and_version field to 0010
    s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)  // bits 6-7 of the clock_seq_hi_and_reserved to 01
    s[8] = s[13] = s[18] = s[23] = '-'

    let uuid = s.join('')
    return uuid
  }

  // 各个场景的文件大小限制 单位KB
  static fileSizeLimit = {
    // 图片
    img: 2048,
    // 视频
    video: 10240,
    // 音频
    audio: 10240,
    // 其它
    other: 10240,
  }

  static fileTypeEnum = {
    'image': ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/bmp'],
    'video': ['video/mp4', 'video/avi'],
    'audio': ['audio/ogg', 'audio/mp3']
  }
  static fileSuffixEnum = {
    'image': ['jpg', 'png', 'jpeg', 'bmp', 'gif'],
    'video': ['mp4', 'ogg', 'avi', 'mov', 'rm', 'rmvb', '3gp', 'flv', 'mkv', 'm4v', 'mpg', 'mpeg', 'wmv', 'asf', 'asx'],
    'audio': ['ogg', 'mp3']
  }

  // 获取文件对应的文件类型
  static getFileType(file) {
    let fileType = file.type
    //console.log(fileType);
    let fileTypeMain = fileType.split('/')[0]
    let fileTypeSub = fileType.split('/')[1]
    return [fileTypeMain, fileTypeSub]
  }

  // 检测文件大小限制
  static checkFileSize(sizeLimit, file) {
    let size = file.size
    let sizeInput = parseInt(size / 1024)
    if (sizeLimit < sizeInput) {
      msg_err('文件必须小于' + sizeLimit + 'KB，当前文件大小是' + sizeInput + 'KB')
      return false
    } else {
      return true
    }
  }

  // 限制文件类型
  static fileTypeLimit(file, type) {
    let [fileType] = this.getFileType(file)
    return fileType === type;
  }

  // 获取文件后缀名
  static getSuffix(v) {
    console.log(v)
    let nameArr = v.split('.')
    let fileSuffix = nameArr[nameArr.length - 1]
    return fileSuffix
  }

  // element upload的上传方法
  static async uploadRequest(upload) {
    let file = upload.file
    return await new Promise((resolve, reject) => {
      BaseUploadModel.qiNiuUpload(file, {
        next: (result) => {
        },
        error: (errResult) => {
          console.log(errResult)
          msg_err('上传失败')
        },
        complete: (result) => {
          let domain = BaseUploadModel.getBucketDomain(file)
          let url = domain + '/' + result.key + ''
          resolve({data: url})
        }
      })
    })
  }

  // 判断是否是要导入的excel文件
  static isImportExcelFile(file) {
    console.log(file.type)
    if (file.type !== "application/vnd.ms-excel" && file.type !== "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") {
      msg_err("请选择后缀名为xls的Excel导入文件")
      return false
    } else {
      return true
    }
  }
}

export {BaseUploadModel}
