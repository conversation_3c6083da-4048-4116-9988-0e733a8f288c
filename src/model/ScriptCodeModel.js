import {useCode} from "@/api/ScriptCodeApi";
import {ToolsModel} from "./ToolsModel";
import {ScriptRecordModel} from "./ScriptRecordModel";

class ScriptCodeModel {
    // 使用体验码
    static async useCode(code) {
        // 时间误差检测
        if (!(await ToolsModel.frontServerDateCompare())) {
            return
        }
        let [res] = await useCode({
            code
        });
        if (res.code === "000000") {
            // 获取任务记录等信息
            let recordInfo = res.data["recordInfo"]
            // 初始化任务记录信息
            await ScriptRecordModel.initRecord(recordInfo)
            return true
        } else {
            return false
        }
    }
}

export {ScriptCodeModel}
