import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList, getOne, deleteOne, addMultipleCode,getCodeInfo} from "@/api/ScriptCodeApi";

class ScriptCodeModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      scriptId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取信息通过code
  static async getCodeInfo(code) {
    let [res] = await getCodeInfo(code)
    if (res.code === "000000"&&res.hasOwnProperty("data")) {
      return res.data
    } else {
      return false
    }
  }


  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 批量创建体验码
  static async addMultipleCode(codeNumber, scriptId, remarks, otherInfo) {
    let [data] = await addMultipleCode({
      codeNumber,
      scriptId,
      remarks,
      otherInfo
    });
    if (data.code === "000000") {
      return data.data
    } else {
      return false
    }
  }
}

export {ScriptCodeModel}
