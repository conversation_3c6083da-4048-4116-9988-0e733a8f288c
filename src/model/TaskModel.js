import router from "../router/index"
import taskConfig from "../scripts/fdldq/taskConfig";
import {find_obj_from_arr_by_id} from "../utils/common";
import {initOneTask, completeOneTask, completeAllTask} from "../api/ScriptRecordApi";
import {TaskStore} from "../store/TaskStore";
import {ScriptRecordModel} from "./ScriptRecordModel";

class TaskModel {
    // 进入某一关的页面
    static async goToOneTaskPage(nowTaskId) {
        let nowTask = this.getOneTaskConfig(nowTaskId);
        // 标记任务进行中
        await TaskModel.markTaskDoing(nowTaskId);
        // 跳转到任务页
        // todo 直接push 和window.location刷新是否有性能差别，location会释放内存？
        // 22-12-2 还是闪退 微信和浏览器都闪退
        router.push({
            path: nowTask["pageUrl"]
        })
    }

    // 初始化某一关
    static async initOneTask(taskId) {
        // 找到当前关配置
        let nowTask = this.getOneTaskConfig(taskId);
        // 设置关卡必要信息
        let taskInfo = { // todo 思考每个任务要保存什么信息 安全隐患不能让前端初始化，改为后端
            taskId: taskId,
            status: "",// 任务状态
            clueList: [],// 线索记录 [选择题序号-线索序号]
            selectQuestionAnswerList: [],// 选择题记录 [[],[]]
            extraInfo: {},// 其他额外需要记录的信息
        }
        let param = {
            recordId: ScriptRecordModel.getRecordIdFromCookie(),
            taskInfo: taskInfo
        }
        let [res] = await initOneTask(param)
        if (res.code === "000000") {
            let savedRecordInfo = res.data
            // 保存到store
            let taskStore = TaskStore()
            taskStore.setRecordInfo(savedRecordInfo)
            taskStore.$patch({
                nowTaskId: taskId,
                nextTaskId: nowTask["nextTaskId"],// todo 改为后端判断，增加安全性
            })
            return true
        } else {
            return false
        }
    }

    // 找到某个任务配置-配置是指配置文件
    static getOneTaskConfig(taskId) {
        let taskList = taskConfig.taskList;
        // 找到当前关配置
        let [, task] = find_obj_from_arr_by_id("taskId", taskId, taskList);
        return task
    }

    // 找到某个任务记录-记录是指pinia记载的
    static getOneTaskRecord(taskId) {
        let taskStore = TaskStore()
        let recordInfo = taskStore.recordInfo
        let taskRecord = recordInfo[taskId]
        return taskRecord
    }

    // 完成某一关
    static async completeOneTask(nowTaskId) {
        // 找到当前关配置
        let nowTask = this.getOneTaskConfig(nowTaskId);
        let param = {
            recordId: ScriptRecordModel.getRecordIdFromCookie(),
            taskId: nowTaskId,
            nextTaskId: nowTask["nextTaskId"],// todo 改为后端判断，增加安全性
        }
        let [res] = await completeOneTask(param)
        if (res.code === "000000") {
            let savedRecordInfo = res.data
            // 保存到store
            let taskStore = TaskStore()
            taskStore.setRecordInfo(savedRecordInfo)
            // 初始化下一关
            if (nowTask["nextTaskId"] !== "none") { // 判断是否是最终任务，不是才初始化下一关
                let nextTask = this.getOneTaskConfig(nowTask["nextTaskId"]);
                await this.initOneTask(nextTask["taskId"])
            }
            return true
        } else {
            return false
        }
    }

    // 判断某个任务是否已完成,如果是就跳转回剧本主页
    static judgeOneTaskIsCompletedOrJumpBack(taskId) {
        return new Promise(async (resolve, reject) => {
            let taskStore = TaskStore()
            let recordInfo = taskStore.recordInfo
            // 如果不再剧本中就跳转
            if (!ScriptRecordModel.ifNotScriptToJump()) {
                return
            }
            // 如果任务已完成就返回主界面
            if (recordInfo[taskId]["completed"] === true) {
                await router.push({
                    name: "AR_Main_View",
                })
            }
            resolve(true)
        })
    }

    // 到达某一关地理位置
    static async arriveOneTaskGpsAddress(taskId) {
        let res = await ScriptRecordModel.updateRecordInfo("updateTaskStatus", {
            taskId,
            status: "arriveGpsLocation"
        })
        if (res) {
            return true
        } else {
            return false
        }
    }

    // 扫描某一关入口定位二维码
    static async scanEdQrCode(taskId) {
        let res = await ScriptRecordModel.updateRecordInfo("updateTaskStatus", {
            taskId,
            status: "scanEdQrCode"
        })
        if (res) {
            return true
        } else {
            return false
        }
    }

    // 扫描某一关任务开始AR物体
    static async scanEdArCode(taskId) {
        let res = await ScriptRecordModel.updateRecordInfo("updateTaskStatus", {
            taskId,
            status: "scanEdArCode"
        })
        if (res) {
            return true
        } else {
            return false
        }
    }

    // 标记任务进行中
    static async markTaskDoing(taskId) {
        let res = await ScriptRecordModel.updateRecordInfo("updateTaskStatus", {
            taskId,
            status: "doing"
        })
        if (res) {
            return true
        } else {
            return false
        }
    }

    // 标记完成某个大任务中的小任务
    static async markTaskDoing_completeOneChildTask(taskId, childTaskIndex) {
        let res = await ScriptRecordModel.updateRecordInfo("updateTaskStatus", {
            taskId,
            status: "doing_completeOneChildTask",
            childTaskIndex: childTaskIndex,
        })
        if (res) {
            return true
        } else {
            return false
        }
    }

    // 选择某个选择题,做出决策
    static async completeOneSelect(taskId, answer, selectIndex) {
        let res = await ScriptRecordModel.updateRecordInfo("completeOneSelect", {
            answer: answer,
            taskId,
            selectIndex: selectIndex,
        })
        if (res) {
            return true
        } else {
            return false
        }
    }

    // 获得某个线索
    static async getOneClue(taskId, clue, clueIndex, selectIndex) {
        // 判断是否已获取过此线索
        let taskRecord = this.getOneTaskRecord(taskId)
        let clueField = `${selectIndex}-${clueIndex}`
        if (taskRecord["clueList"].indexOf(clueField) !== -1) {
            return true // todo 判断已获取
        }
        let res = await ScriptRecordModel.updateRecordInfo("getOneClue", {
            clue: clue,// 暂时未用 todo 要不要把线索文字内容保存起来
            clueIndex: clueIndex,
            taskId,
            selectIndex: selectIndex,
        })
        if (res) {
            return true
        } else {
            return false
        }
    }

    // todo 完成整个任务
    static async completeAllTask() {
        let [res] = await completeAllTask({
            recordId: ScriptRecordModel.getRecordIdFromCookie()
        })
        if (res.code === "000000") {
            let recordEntity = res.data
            // 保存到store
            let taskStore = TaskStore()
            taskStore.$patch({
                complete: recordEntity.completed,
                startTime: recordEntity.startTime,
                endTime: recordEntity.endTime,
                nowTaskId: recordEntity.nowTaskId,
                nextTaskId: recordEntity.nextTaskId,
                score: recordEntity.score,
                scoreInfo: recordEntity.scoreInfo
            })
            return recordEntity
        } else {
            return false
        }
    }

    // 保存某个任务内的额外信息
    static async saveOneTaskExtraInfo(taskId, extraInfo) {
        let res = await ScriptRecordModel.updateRecordInfo("saveOneTaskExtraInfo", {
            taskId,
            extraInfo: extraInfo,
        })
        if (res) {
            return true
        } else {
            return false
        }
    }

    // 获取某个任务内的额外信息
    static async getOneTaskExtraInfo(taskId) {
        let res = await ScriptRecordModel.getRecordInfo("getOneTaskExtraInfo", {
            taskId
        })
        if (res) {
            return true
        } else {
            return false
        }
    }
}

export {TaskModel}
