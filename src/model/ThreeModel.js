import * as THREE from "three";
import {GLTFLoader} from 'three/examples/jsm/loaders/GLTFLoader'
import {DRACOLoader} from 'three/examples/jsm/loaders/DRACOLoader'

//
let publicPath = import.meta.env.BASE_URL === "/" ? "/" : import.meta.env.BASE_URL
let dracoLoaderPath = publicPath + "js/"

class ThreeModel {
    // 加载Draco压缩的Glft
    static loadGltfDraco(modelPath) {
        return new Promise((resolve, reject) => {
            let dracoLoader = new DRACOLoader()
            dracoLoader.setDecoderPath(dracoLoaderPath)
            let gltfLoader = new GLTFLoader()
            gltfLoader.setDRACOLoader(dracoLoader)
            gltfLoader.load(modelPath, (gltf) => {
                resolve(gltf)
            });
        })
    }

    // 加载纹理 todo 似乎本身就是个同步方法？
    static loadTexture(texturePath) {
        return new Promise((resolve, reject) => {
            let texture = new THREE.TextureLoader().load(texturePath)
            resolve(texture)
        })
    }

    /**
     * GUI添加modelFolder
     * @param gui
     * @param model
     * @param folderName
     * @param positionRangeArr position开始和结束数值范围 如[-1,1]
     */
    static guiAddModelFolder(gui, model, folderName, positionRangeArr) {
        let modelFolder = gui.addFolder(folderName)
        modelFolder.add(model.position, 'x', positionRangeArr[0], positionRangeArr[1]).onChange(val => {
            model.position.x = val
        }).name("p.x")
        modelFolder.add(model.position, 'y', positionRangeArr[0], positionRangeArr[1]).onChange(val => {
            model.position.y = val
        }).name("p.y")
        modelFolder.add(model.position, 'z', positionRangeArr[0], positionRangeArr[1]).onChange(val => {
            model.position.z = val
        }).name("p.z")
        modelFolder.add(model.rotation, 'x', -Math.PI, Math.PI).onChange(val => {
            model.rotation.x = val
        }).name("r.x")
        modelFolder.add(model.rotation, 'y', -Math.PI, Math.PI).onChange(val => {
            model.rotation.y = val
        }).name("r.y")
        modelFolder.add(model.rotation, 'z', -Math.PI, Math.PI).onChange(val => {
            model.rotation.z = val
        }).name("r.z")
    }

    /**
     * GUI添加cameraFolder
     * @param gui
     * @param camera
     * @param folderName
     * @param positionRangeArr position开始和结束数值范围 如[-1,1]
     */
    static guiAddCameraFolder(gui, camera, folderName, positionRangeArr) {
        let cameraFolder = gui.addFolder(folderName)
        cameraFolder.add(camera.position, 'x', positionRangeArr[0], positionRangeArr[1]).onChange(val => {
            camera.position.x = val
        }).name("p.x")
        cameraFolder.add(camera.position, 'y', positionRangeArr[0], positionRangeArr[1]).onChange(val => {
            camera.position.y = val
        }).name("p.y")
        cameraFolder.add(camera.position, 'z', positionRangeArr[0], positionRangeArr[1]).onChange(val => {
            camera.position.z = val
        }).name("p.z")
        cameraFolder.add(camera.rotation, 'x', -Math.PI, Math.PI).onChange(val => {
            camera.rotation.x = val
        }).name("r.x")
        cameraFolder.add(camera.rotation, 'y', -Math.PI, Math.PI).onChange(val => {
            camera.rotation.y = val
        }).name("r.y")
        cameraFolder.add(camera.rotation, 'z', -Math.PI, Math.PI).onChange(val => {
            camera.rotation.z = val
        }).name("r.z")
    }

    // 寻找某个属性的对象
    static findObjectFromArrByNameAndVal(arr, name, val) {
        let obj = undefined;
        let k = -1;
        for (k in arr) {
            if (arr.hasOwnProperty(k)) {
                if (arr[k][name] === val) {
                    obj = arr[k];
                    break;
                }
            }
        }
        return [k, obj];
    }

    // 寻找某个属性的对象集合
    static findObjectsFromArrByNameAndVal(arr, name, val) {
        let obj = undefined;
        let k = -1;
        let objects = []
        console.log(arr)
        for (k in arr) {
            if (arr.hasOwnProperty(k)) {
                if (arr[k][name] === val) {
                    obj = arr[k];

                    objects.push(obj)
                }
            }
        }
        return objects;
    }
}

export {ThreeModel}
