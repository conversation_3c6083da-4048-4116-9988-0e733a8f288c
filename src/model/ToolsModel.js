import {decodeQrCode, getServerNowDate} from "../api/ToolsApi";
import {toast_err} from "../utils/nut_component";
import {TaskStore} from "../store/TaskStore";
import screenfull from "screenfull";

class ToolsModel {
    // 上传单个文件
    static async decodeQrCode(file) {
        let formData = new FormData()
        formData.append("file", file)
        let [data] = await decodeQrCode(formData)
        if (data.code === "000000") {
            return data.data
        } else {
            return false
        }
    }

    // 获取服务器当前时间
    static async getServerNowDate() {
        let [data] = await getServerNowDate()
        if (data.code === "000000") {
            return data.data
        } else {
            return false
        }
    }

    // 判断用户前台时间是否和服务器时间一致
    static async frontServerDateCompare() {
        const dateDisparitySecond = 20000; // 可以容忍的时间差距 单位秒
        let frontDate = new Date().getTime()
        let serverDate = await this.getServerNowDate()
        if (Math.abs(serverDate - frontDate) >= dateDisparitySecond) {
            toast_err("您当前系统时间和北京时间有误差，请调整后再进行操作！")
            return false
        } else {
            return true
        }
    }

    // 添加全屏监听
    static addFullscreenListened() {
        if (screenfull.isEnabled) {
            let taskStore = TaskStore()
            taskStore.$patch({
                screenFull: screenfull.isFullscreen,
            })
            screenfull.on('change', async () => {
                taskStore.$patch({
                    screenFull: screenfull.isFullscreen,
                })
            });
        }
        if (screenfull.isEnabled) {
            screenfull.on('error', event => {
                console.error('Failed to enable fullscreen', event);
            });
        }
    }

    // 添加横竖屏监听
    static addOrientationListen() {
        let direction = window.neworientation.current
        let taskStore = TaskStore()
        taskStore.$patch({
            screenDirection: direction,
        })
        window.addEventListener('orientationchange', function () {
            let direction = window.neworientation.current
            taskStore.$patch({
                screenDirection: direction,
            })
        });
    }

    // 删除横竖屏监听
    static removeOrientationListen() {
        window.removeEventListener('orientationchange', function () {
        })
    }

    // 运行环境判断
    static getEnvironment() {
        let taskStore = TaskStore()
        // 判断手机类型
        let u = navigator.userAgent
        console.log(u)
        let isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //android系统
        let isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //ios系统
        if (isAndroid) {
            taskStore.$patch({
                mobileType: "Android",
            })
        }
        if (isiOS) {
            taskStore.$patch({
                mobileType: "iPhone",
            })
        }
        // 判断是微信还是浏览器
        if (u.toLowerCase().indexOf('micromessenger') !== -1) {
            taskStore.$patch({
                browserType: "weixin",
            })
        } else {
            taskStore.$patch({
                browserType: "browser",
            })
        }
    }

    // 重置metaViewport
    static resetMetaViewport() {
        // 重置meta viewport
        let metaViewport = document.getElementById("viewport")
        if (metaViewport) {
            // metaViewport.parentNode.removeChild(metaViewport)
            metaViewport.setAttribute('content', 'width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no, shrink-to-fit=yes');
        }
    }
}

export {ToolsModel}
