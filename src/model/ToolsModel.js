import {getIpInfoOnline1} from "@/api/ToolsApi"

class ToolsModel {
  // 获取ip信息
  static async getIpInfoOnline1(ip,date) {
    if (!window.ipCache) {
      window.ipCache = {}
    }
    if (window.ipCache.hasOwnProperty(ip)) {
      return window.ipCache[ip]
    }
    let [data] = await getIpInfoOnline1({
      ip
    })
    let result = data.data
    let resultStr = ""
    if (result["code"] === 200) {
      if (result.province === "") {
        resultStr = result["country"]
        window.ipCache[ip] = resultStr
      } else {
        resultStr = result["province"] + result["city"] + result["area"] + result["isp"]
        window.ipCache[ip] = resultStr
      }
      return resultStr
    } else {
      return "获取失败"
    }
  }
}

export {ToolsModel}
