import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 上传一个文件
export async function uploadOne(data) {
  return request_async(API_URL + "/v1/file/uploadOne", "post_form", data);
}

// 删除一个文件
export async function deleteOne(data) {
  return request_async(API_URL + "/v1/file/fileOne", "delete", data);
}

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL + "/v1/file/", "post_body", data);
}
