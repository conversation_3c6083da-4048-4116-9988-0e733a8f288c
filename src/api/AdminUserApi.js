import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 获取用户信息
export async function getMineInfo(data) {
  return request_async(API_URL+"/v1/adminUser/user", "get", data);
}

// 用户进行登录
export async function login(data) {
  return request_async(API_URL+"/v1/adminUser/token", "get", data);
}

// 用户退出登录
export async function loginOut(data) {
  return request_async(API_URL+"/v1/adminUser/token", "delete", data);
}

// 获取本系统用户列表-不分页
export async function getUserList(data) {
  return request_async(API_URL + `/v1/adminUser/user/list`, "post_body", data);
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL + "/v1/adminUser/user", "delete", data);
}

// 新增
export async function add(data) {
  return request_async(API_URL + "/v1/adminUser/user", "post_body", data);
}

// 修改
export async function edit(data) {
  return request_async(API_URL + "/v1/adminUser/user", "put_body", data);
}

// 修改密码
export async function changePassword(data) {
  return request_async(API_URL + "/v1/adminUser/changePassword", "post_body", data);
}
