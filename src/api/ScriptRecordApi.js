import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 获取我的任务列表
export async function getUserTaskList(data) {
    return request_async(API_URL + `/v1/scriptRecord/userTaskList`, "get", data);
}

// 获取某个任务记录信息
export async function getUserOneRecordInfo(data) {
    return request_async(API_URL + `/v1/scriptRecord/userOneRecordInfo`, "get", data);
}

// 初始化某一关
export async function initOneTask(data) {
    return request_async(API_URL + `/v1/scriptRecord/initOneTask`, "post_body", data);
}

// 完成某一关
export async function completeOneTask(data) {
    return request_async(API_URL + `/v1/scriptRecord/completeOneTask`, "post_body", data);
}

// 更新记录信息
export async function updateRecordInfo(recordId, method, data) {
    return request_async(API_URL + `/v1/scriptRecord/updateRecordInfo?recordId=${recordId}&method=${method}`, "post_body", data);
}

// 获取任务记录信息
export async function getRecordInfo(recordId, method, data) {
    return request_async(API_URL + `/v1/scriptRecord/getRecordInfo?recordId=${recordId}&method=${method}`, "post_body", data);
}

// 完成了所有关
export async function completeAllTask(data) {
    return request_async(API_URL + `/v1/scriptRecord/completeAllTask`, "post_body", data);
}

// 重新开始剧本
export async function restartScript(data) {
    return request_async(API_URL + `/v1/scriptRecord/restartScript`, "post_body", data);
}

// 开始一个新剧本
export async function startNewScript(data) {
    return request_async(API_URL + `/v1/scriptRecord/startNewScript`, "post_body", data);
}
