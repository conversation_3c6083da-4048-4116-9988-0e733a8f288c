import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 获取一个
export async function getOne(data) {
  return request_async(API_URL + "/v1/scriptCode/", "get", data);
}

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL + "/v1/scriptCode/", "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/scriptCode/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL + `/v1/scriptCode/list`, "post_body", data);
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL + "/v1/scriptCode/", "delete", data);
}

// 批量生成实验码
export async function addMultipleCode(data) {
  return request_async(API_URL + "/v1/scriptCode/addMultipleCode", "post_body", data);
}

// 通过code获取信息
export async function getCodeInfo(code) {
  return request_async(API_URL + "/v1/scriptCode/codeInfo", "get", {code: code});
}
