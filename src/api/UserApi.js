// 发送账号注册邮箱验证码
import {request_async} from "../utils/requestAsync";
import {API_URL} from "../config/main";

// 登录平台
export async function loginInPlatform(data) {
    return request_async(API_URL + "/v1/user/loginInPlatform", "get", data);
}

// 发送注册邮件
export async function sendRegEmail(data) {
    return request_async(API_URL + "/v1/user/sendRegEmail", "get", data);
}

// 发送忘记密码邮件
export async function sendForgetEmail(data) {
    return request_async(API_URL + "/v1/user/sendForgetEmail", "get", data);
}



// 注册账号
export async function regAccount(data) {
    return request_async(API_URL + "/v1/user/regAccount", "post_json", data);
}

// 通过token获取用户信息
export async function getInfoByToken(data) {
    return request_async(API_URL + "/v1/user/infoByToken", "get", data);
}

// 修改密码
export async function changePassword(data) {
    return request_async(API_URL + "/v1/user/changePassword", "post_json", data);
}

// 获取个人中心信息
export async function getUserIndexInfo(data) {
    return request_async(API_URL + "/v1/user/userIndexInfo", "get", data);
}

