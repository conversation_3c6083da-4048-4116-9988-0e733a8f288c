import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 获取一个
export async function getOne(data) {
  return request_async(API_URL + "/v1/log/", "get", data);
}


// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/log/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL + `/v1/log/list`, "post_body", data);
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL + "/v1/log/", "delete", data);
}

// 获取列表-分页
export async function getLogList(page, size, sort, data) {
  return request_async(API_URL + `/v1/log/logList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}
