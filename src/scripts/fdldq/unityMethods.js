// unity 调用方法

// todo 目前发现unity只能调用window下面的全局方法
import {TaskModel} from "../../model/TaskModel";


// 切换场景
/**
 * 0,0 选角色 0,1-A1
 * 1,0 B1
 * 2,0 C1
 * 3,0 D1
 */
export function unity_fdldq_setScene(vue, taskIdString) {
    window.vueNow = vue
    window.js_fdldq_setScene = function () {
        window.vueNow.onSetScene(taskIdString)
        return taskIdString
    }
}

// 完成场景加载
export function unity_fdldq_loadedScene(vue, taskId) {
    window.vueNow = vue
    window.js_fdldq_loadedScene = function () {
        window.vueNow.onLoadedScene(taskId)
        return taskId
    }
}

// 选择角色
export function unity_fdldq_chooseRole(vue) {
    window.vueNow = vue
    window.js_fdldq_chooseRole = function (roleId) {
        window.vueNow.onChooseRole(roleId)
    }
}

// 完成某个任务
export function unity_fdldq_completeOneTask(vue) {
    window.vueNow = vue
    window.js_fdldq_completeOneTask = function (taskId) {
        window.vueNow.onCompleteTask(taskId)
    }
}

// 完成某个选择题或决策
export function unity_fdldq_completeOneSelect(vue) {
    window.vueNow = vue
    window.js_fdldq_completeOneSelect = function (answer, selectIndex, taskId) {
        window.vueNow.onCompleteOneSelect(answer, selectIndex, taskId)
    }
}

// 获取某个线索
export function unity_fdldq_getOneClue(vue) {
    window.vueNow = vue
    window.js_fdldq_getOneClue = function (clue, clueIndex, selectIndex, taskId) {
        window.vueNow.onGetOneClue(clue, clueIndex, selectIndex, taskId)
    }
}


// 保存某个任务内的额外信息
export function unity_fdldq_saveOneTaskExtraInfo() {
    window.vueNow = vue
    window.js_fdldq_saveOneTaskExtraInfo = async function (taskId, extraInfo) {
        await TaskModel.saveOneTaskExtraInfo(taskId, extraInfo)
        return true
    }
}

// 获取某个任务内的额外信息
export function unity_fdldq_getOneTaskExtraInfo() {
    window.vueNow = vue
    window.js_fdldq_getOneTaskExtraInfo = async function (taskId) {
        await TaskModel.getOneTaskExtraInfo(taskId)
        return true
    }
}

// 播放视频前调用
export function unity_fdldq_beforePlayVideo(vue) {
    window.vueNow = vue
    window.js_fdldq_beforePlayVideo = function (videoName,videoIndex) {
        console.log(videoName,videoIndex)
        window.vueNow.onBeforePlayVideo(videoName,videoIndex)
    }
}

// 播放视频后调用
export function unity_fdldq_afterPlayVideo(vue) {
    window.vueNow = vue
    window.js_fdldq_afterPlayVideo = function (taskId) {
        window.vueNow.onAfterPlayVideo(taskId)
    }
}
