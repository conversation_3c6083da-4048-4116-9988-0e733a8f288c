/*初始化和通用css*/

    * {
        /*所有元素采用盒模型*/
        box-sizing: border-box;
    }

img {
    width: 100%;
    vertical-align: middle;
    /*使用了viewport-units-buggyfill 安卓能正常显示图片 ios不能*/
    content: normal !important;
}

input {
    border: 0;
    outline: none;
    background-color: rgba(0, 0, 0, 0);
    -webkit-appearance: none;
}

textarea {
    outline: none;
    -webkit-appearance: none;
}

/*清除浮动*/
.clearFix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
}

/*清除浮动*/
.clearfix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
}

.fixed {
    position: fixed;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

/*flex*/
.flex {
    display: flex;
}

.flex-dr {
    flex-direction: column;
}

.flex-center {
    justify-content: center;
    align-items: center;
}

.flex-around {
    justify-content: space-around;
    align-items: center;
}

.flex-between {
    justify-content: space-between;
    align-items: center;
}

.flex-start {
    justify-content: flex-start;
    align-items: center;
}

.flex-end {
    justify-content: flex-end;
    align-items: center;
}

.flex-wrap {
    flex-wrap: wrap;
}

/*省略号  */
.ellipsis {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

/*英文数字等自动化换行 还需要设置width*/
.word-hr {
    word-wrap: break-word;
    white-space: pre-wrap;
    word-break: normal;
}

.html-view {

}

.html-view .img {
    max-width: 100% !important;
}

/* 方向提醒 */
.direction-tip {
    line-height: 100vh;
    text-align: center;
    font-size: 25px;
    color: #333;
}

/*平台通用块级按钮*/
.common-block-btn {
    width: 315px;
    height: 40px;
    line-height: 40px;
    border-radius: 20px;
    text-align: center;
    font-size: 15px;
    color: #fff;
    background-image: url(../assets/ui/platform/common/long-round-btn-bg.png);
    background-position: 50% 50%;
}

.common-short-btn {
    width: 105px;
    height: 30px;
    line-height: 30px;
    border-radius: 15px;
    text-align: center;
    font-size: 15px;
    color: #fff;
    background-image: url(../assets/ui/platform/common/short-round-btn-bg.png);
    background-position: 50% 50%;
}

/*横屏-全屏按钮*/
.full-btn-landscape {
    background-image: url(../assets/ui/fdldq/common/full-btn-bg.png);
    background-position: 50% 50%;
    width: 33px;
    height: 13px;
    border-radius: 3px;
    text-align: center;
    padding-top: 1px;
    position: fixed;
    left: 3px;
    bottom: 2px;
    z-index: 100;
}

.full-btn-landscape img {
    width: 7px;
    height: 7px;
    display: inline-block;
    margin-top: -2px;
    margin-right: 2px;
}

.full-btn-landscape span {
    font-size: 7px;
    color: #fff;
    line-height: 13px;
}
/* ar视频背景 */
#arVideoBackground{
    position: fixed;
    left: 0px;
    top:0px;
    right: 0px;
    bottom: 0px;
    z-index: -1;
}
