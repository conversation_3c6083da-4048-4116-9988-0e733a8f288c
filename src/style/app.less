// 通用变量

// 主色调
@main-color: rgb(249, 103, 111);
// 备注色
@remark-color: rgb(170, 170, 170);
// 按钮颜色
@button-color: #fff;
.text-color {
  color: #f9676f;
}

// 渐变色
.common-bg-jb {
  background: linear-gradient(180deg, #f9676f, #fda8ac);
}

// 通用渐变按钮
.common-button {
  .common-bg-jb();
  display: inline-block;
  text-align: center;
  color: @button-color;
  font-weight: bold;
  border: none;
  text-decoration: none;
}

.common-dis-button {
  background: linear-gradient(180deg, #aaaaaa, #aaaaaa);
  display: inline-block;
  text-align: center;
  color: @button-color;
  font-weight: bold;
  border: none;
  text-decoration: none;
}

// 通用输入框
input[type='text'] {
  color: rgb(71, 71, 71);
  font-size: 28px;
  border-radius: 0;

  &::placeholder {
    color: #c5c5c5;
  }
}


