<template>
  <div class="ar-main-container" v-show="screenDirection==='landscape'">
    <info-frame></info-frame>
  </div>
  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
</template>

<script>
import {toast_err} from "../utils/nut_component";
import {mapState} from "pinia";
import {TaskStore} from "../store/TaskStore";
import infoFrame from "@/views/components/infoFrame.vue";
import {ToolsModel} from "../model/ToolsModel";

export default {
  name: "AR_Main_View",
  components: {infoFrame},
  computed: {
    ...mapState(TaskStore, {
      nowTaskId: store => store.nowTaskId,
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  data() {
    return {
      directionRight: true,
    }
  },
  mounted() {
    let $this = this;

  },
  methods: {}
}
</script>

<style scoped lang="less">
.ar-main-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
}
</style>
