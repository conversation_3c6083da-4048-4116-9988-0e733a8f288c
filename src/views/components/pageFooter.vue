<template>
  <nut-tabbar :bottom="true" :safeAreaInsetBottom="true" v-bind="activeNumber">
    <nut-tabbar-item to="/hall" tab-title="任务大厅" icon="home"></nut-tabbar-item>
    <nut-tabbar-item to="/myTask" tab-title="任务" icon="category"></nut-tabbar-item>
    <nut-tabbar-item to="/user/index" tab-title="我的" icon="my"></nut-tabbar-item>
  </nut-tabbar>
</template>

<script>

export default {
  name: "pageFooter",
  props: {
    activeNumber: Number
  }
}
</script>
<style>
.nut-tabbar-item_icon-box_nav-word {
  margin-top: 5px;
}
</style>
<style scoped>


</style>
