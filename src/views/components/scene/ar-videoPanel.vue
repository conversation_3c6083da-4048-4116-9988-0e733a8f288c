<template>
  <div class="video-container">
    <div id="mui-player"></div>
  </div>
</template>

<script>
import 'mui-player/dist/mui-player.min.css'
import MuiPlayer from 'mui-player'

export default {
  name: "arVideoPanel",
  props: {
    videoInfo: {
      type: Object,
      default: () => {
        return {url: ""}
      }
    }
  },
  components: {},
  data() {
    return {}
  },
  mounted() {
    let $this = this;
  },
  methods: {
    initVideo() {
      let $this = this
      let mp = new MuiPlayer({
        container: '#mui-player',
        // height: window.screen.height,// todo iphone适配
        height: window.innerHeight,
        autoFit: false,
        autoplay: true,
        videoAttribute: [
          {attrKey: 'webkit-playsinline', attrValue: 'webkit-playsinline'},
          {attrKey: 'playsinline', attrValue: 'playsinline'},
          {attrKey: 'x5-video-player-type', attrValue: 'h5-page'},
        ],
        src: this.videoInfo.url,
      })
      mp.video().onended = () => {
        $this.$emit("onPlayEnd")
      };
      mp.openFullScreen()
    }
  }
}
</script>

<style scoped lang="less">
.full-btn {
  position: fixed;
  left: 20px;
  bottom: 0px;
}

.video-container {

  #mui-player {
    width: 100%;
    height: 100vh;
  }
}

.button-box {
  margin-top: 10px;
  text-align: center;

  .van-button {
    margin-right: 30px;
  }
}
</style>
