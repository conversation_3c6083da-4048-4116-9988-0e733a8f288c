<template>
  <div class="text-container">
    <div class="text-content" v-for="item in textInfo.content.split('\n')">{{ item }}</div>
    <div class="button-box">
      <van-button type="success" size="normal" @click="clickSureBtn">确&nbsp;&nbsp;认</van-button>
    </div>
  </div>

</template>

<script>

export default {
  name: "arTextPanel",
  components: {},
  props: {
    textInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  mounted() {
    let $this = this;
  },
  methods: {
    clickSureBtn() {
      this.$emit("onClickSureBtn")
    }
  }
}
</script>

<style scoped lang="less">
.full-btn {
  position: fixed;
  left: 20px;
  bottom: 0px;
}

.text-container {
  padding: 10px;

  .text-content {
    color: #fff;
    font-size: 8px;
    text-indent: 2em;
    line-height: 10px;
  }
}

.button-box {
  margin-top: 10px;
  text-align: center;

  .van-button {
    margin-right: 30px;
  }
}
</style>
