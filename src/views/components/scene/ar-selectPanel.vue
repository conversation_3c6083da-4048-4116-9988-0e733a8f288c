<template>
  <div class="select-container">
    <div class="title">{{ questionInfo.title }}{{ questionInfo.maxSelectNumber > 1 ? '（多选）' : '（单选）' }}
    </div>
    <div class="options">
      <div class="option-li flex flex-start" v-for="(item,index) in questionInfo.options"
           @click="selectOption(index)">
        <van-icon size="1.5rem" class="square" name="passed" v-show="questionInfo.userAnswer.indexOf(index)!==-1"
        />
        <van-icon size="1.5rem" class="square" name="circle"
                  v-show="questionInfo.userAnswer.indexOf(index)===-1"/>
        <div>{{ item.content }}</div>
      </div>
    </div>
    <div class="button-box">
      <van-button type="primary" size="normal" @click="clickShowClueBtn">
        查看线索（{{ this.questionInfo.clueList.length }}条）
      </van-button>
      <van-button type="success" size="normal" @click="clickSureChooseBtn">确认选择</van-button>
    </div>
    <van-popup v-model:show="clueShow" position="right" :style="{ width: '60%',height:'100%' }">
      <van-collapse v-model="clueCollapse">
        <van-collapse-item v-for="(item,index) in questionInfo.clueList" :title="'线索'+(index+1)" :name="index"
                           @click="clickClueItem(item,index)">
          {{ item.content }}
        </van-collapse-item>
      </van-collapse>
    </van-popup>
  </div>
</template>

<script>
import {msg_err, msg_err_vant, msg_success_vant, toast_err} from "@/utils/nut_component";
import {Button} from 'vant';
import {ref, onBeforeUpdate} from 'vue';
import screenfull from 'screenfull'

export default {
  name: "arSelectPanel",
  components: {},
  props: {
    // 选择题问题信息
    questionInfo: {
      type: Object,
      default: () => {
        return {
          clueList: [],
          userClueList: []
        }
      }
    },
    // 任务信息
    taskInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      firstAnswer: [],// 首次选择结果
      clueShow: false,
      clueCollapse: [],
    }
  },
  mounted() {
    let $this = this;
  },
  methods: {
    // 选择某个选项
    selectOption(index) {
      let findIndex = this.questionInfo.userAnswer.indexOf(index);
      if (findIndex === -1) {
        if (this.questionInfo.maxSelectNumber === 1) {// 单选
          this.questionInfo.userAnswer = []
          this.questionInfo.userAnswer.push(index)
        } else {
          this.questionInfo.userAnswer.push(index)
        }

      } else {
        this.questionInfo.userAnswer.splice(findIndex, 1)
      }
    },
    // 点击查看线索按钮
    clickShowClueBtn() {
      this.clueShow = true;
    },
    // 点击某个线索
    clickClueItem(clue, clueIndex) {
      if (this.questionInfo.userClueList.indexOf(clueIndex) === -1) {
        this.questionInfo.userClueList.push(clueIndex)
        this.$emit("onGetOneClue", clue, clueIndex,this.questionInfo.index); // 回传，选择的决策
      }
    },
    // 点击确认选择按钮
    clickSureChooseBtn() {
      if (this.questionInfo.userAnswer.length > 0) {
        // 答案判断逻辑
        let result = true
        this.questionInfo.answer.forEach(needAnswer => {
          if (this.questionInfo.userAnswer.indexOf(needAnswer) === -1) {
            result = false
          }
        })
        if(this.firstAnswer.length===0){ // 如果没有选择过
          this.firstAnswer = JSON.parse(JSON.stringify(this.questionInfo.userAnswer)) // 保存首次答案，用于计分
        }
        if (result) {
          msg_success_vant("答案正确")
          this.$emit("onCompleteOneSelect", this.firstAnswer, this.questionInfo.index); // 回传，已完成选择题
        } else {
          msg_err_vant("答案错误")
        }
      } else {
        msg_err_vant("请先选择您的答案")
        return
      }
    },
  }
}
</script>

<style scoped lang="less">
.full-btn {
  position: fixed;
  left: 20px;
  bottom: 0px;
}

.select-container {
  min-height: 100vh;
  padding: 10px;

  .title {
    color: #fff;
    font-size: 8px;
    line-height: 10px;
    text-indent: 2em;
    font-weight: bold;
  }

  .options {
    margin-top: 5px;
    padding: 0px 5px;
    color: #fff;

    .option-li {
      margin-bottom: 6px;
      font-size: 7.5px;

      .square {
        margin-right: 5px;
      }
    }
  }
}

.button-box {
  margin-top: 10px;
  text-align: center;

  .van-button {
    margin-right: 30px;
  }
}
</style>
