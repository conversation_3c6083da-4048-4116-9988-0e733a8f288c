<template>
  <nut-navbar @on-click-back="clickBackBtn" @on-click-title="clickTitle" :title="titleName" :left-show="showLeft">
    <template v-if="showLeft" #left>
      <div v-if="showLeft">返回</div>
    </template>

    <template #right v-if="showRight">
      <nut-icon class="right" name="share-n"></nut-icon>
    </template>
  </nut-navbar>
</template>

<script>
export default {
  name: "pageHeader",
  props: {
    titleName: String,
    showLeft: {
      type: <PERSON>olean,
      default() {
        return false
      }
    },
    showRight: {
      type: <PERSON>olean,
      default() {
        return false
      }
    },
  },
  methods: {
    // 点击返回按钮
    clickBackBtn() {
      this.$router.back(-1)
    },
    // 点击标题
    clickTitle() {

    },
  }
}
</script>
<style>
.nut-tabbar-item_icon-box_nav-word {
  margin-top: 5px;
}
.nut-navbar .nut-navbar__title .title {
  color: #fff;
}
</style>
<style scoped lang="less">
.nut-navbar {
  background: linear-gradient(to bottom, #a3352e, #af4333);
  color: #fff!important;
  margin-bottom: 0px;

}

</style>
