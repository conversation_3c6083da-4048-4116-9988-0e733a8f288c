<!--qr识别-横屏-->
<template>
  <div id="qr-H">
    <div id="scanLine" class="none">
      <img src="@/assets/images/scan-line.png"/>
    </div>
    <div class="footer">
<!--      <van-button type="success" size="small" id="captureVideoBtn" class="none" @click="clickCaptureBtn">截图</van-button>-->
      <van-button type="success" size="small" id="startQrScanBtn" class="none" @click="clickStartBtn">开始识别</van-button>
      <van-button type="danger" size="small" id="stopScanBtn" class="none" @click="clickStopBtn">退出识别</van-button>
    </div>
    <div class="img">
      <img src="" alt="" id="canvas-img" class="none">
    </div>
  </div>


</template>

<script>
import $ from 'jquery'
import {EasyARModel} from "../../model/easyAR/EasyARModel";
import {playSound} from "../../utils/common";
import enums from "@/enums";

export default {
  name: "qrScanH",
  data() {
    return {
      successAudio: enums.audioList[0],
      webAR: undefined,
      needCaptureImg: true,
    }
  },
  mounted() {
  },
  methods: {
    // 显示某个元素
    showElement(id) {
      $(`#qr-H #${id}`).show()
    },
    // 隐藏某个元素
    hideElement(id) {
      $(`#qr-H #${id}`).hide()
    },
    // 初始化
    async init() {
      this.webAR = await EasyARModel.getQRInstance("qr-H");
      let resultCamera = await this.openCamera();
      console.log(resultCamera)
      if (resultCamera === "camera_open_success"
      ) {
        return "init_success"
      } else {
        return "init_fail"
      }
    },
    // 点击开始识别按钮
    clickStartBtn() {
      this.showElement('scanLine');
      this.hideElement("startQrScanBtn");
      this.webAR.startRecognizeQrCode(
          (successMsg) => { // 识别成功回调
            this.showElement("startQrScanBtn");
            playSound(this.successAudio)
            this.$emit("onRecSuccess", successMsg); // 向父组件传递成功的消息
            this.clickStopBtn();
          },
          (errMsg) => { // 识别失败回调
            if (errMsg === "notMatch") {
              this.$emit("onRecError", "notMatch");
            } else {
              this.$emit("onRecError", errMsg);
            }
          }
      );
    },
    // 点击停止识别按钮
    clickStopBtn() {
      this.hideElement('scanLine');
      this.hideElement("startQrScanBtn");
      this.webAR.stopRecognize();
      this.hideElement("stopScanBtn");
      // document.getElementById("scanVideo").setAttribute("style","display:none")
      this.hideElement("scanVideo")
      this.hideElement("captureVideoBtn")
      this.webAR.closeCamera();
    },
    // 点击截图按钮
    clickCaptureBtn() {
      let data = "data:image/jpg;base64," + this.webAR.captureVideo()
      document.querySelector("#qr-H #canvas-img").setAttribute("src", data)
      this.showElement("canvas-img")
    },
    /**
     * 尝试打开摄像头
     */
    async openCamera() {
      let option = {audio: false, video: {facingMode: {exact: 'environment'}}}
      return new Promise((resolve, reject) => {
        this.webAR.openCamera(option).then(() => {
          this.showElement("startQrScanBtn")
          if (this.needCaptureImg) {
            this.showElement("captureVideoBtn")
          }
          this.showElement("stopScanBtn");
          resolve("camera_open_success")
        }).catch(err => {
          console.error(err);
          if (err.message === "Requested device not found") {
            alert("未找到摄像头!");
          } else {
            alert(`摄像头打开失败\n${err}`);
          }
          this.clickStopBtn();
          resolve("camera_open_fail")
          // todo 打开失败的提示
        });
      })
    }
  }
}
</script>

<style lang="less">
#qr-H {
  .none {
    display: none;
  }

  .footer {
    position: fixed;
    bottom: 45px;
    z-index: 100;

    width: 100%;
    text-align: center;

    .van-button {
      margin-right: 5px;
    }
  }

  #scanVideo {

  }


  #scanLine {
    position: absolute;
    width: 100%;
    z-index: 999;
    margin-top: 20vh;
    text-align: center;
    animation: scan 3s infinite linear;
    -webkit-animation: scan 3s infinite linear;
  }

  #scanLine > img {
    width: 80%;
  }

  @keyframes scan {
    0% {
      margin-top: 20vh;
    }
    100% {
      margin-top: 80vh;
    }
  }
}
</style>
