<template>
  <div id="info-frame">
    <div :class="rightTip.status==='open'?'right-info-container open':'right-info-container close'">
      <div :class="rightTip.status==='open'?'bg open':'bg close'"></div>
      <div class="container flex flex-dr flex-center" v-if="rightTip.status==='open'">
        <div class="task-name">
          {{ nowTaskId }} &nbsp;{{ nowTaskConfig.name }}
        </div>
        <div class="flex flex-between">
          <div class="role-box flex flex-dr flex-center">
            <img :src="fdldq_taskConfig.roleObject[roleId]['avatar']" alt="">
            <div class="text">
              <div class="name">{{ fdldq_taskConfig.roleObject[roleId]["name"] }}</div>
              <div class="des">{{ fdldq_taskConfig.roleObject[roleId]["des"] }}</div>
            </div>
          </div>
          <div class="right-box">
            <div class="task-box number-box">
              任务（{{ gotTaskNumber }} / {{ fdldq_taskConfig.calObject['taskNumber'] }}）
            </div>
            <div class="clue-box number-box">
              线索（{{ gotClueNumber }} / {{ fdldq_taskConfig.calObject['clueNumber'] }}）
            </div>
            <div class="decision-box number-box">
              决策（{{ gotDecisionNumber }} / {{ fdldq_taskConfig.calObject['decisionNumber'] }}）
            </div>
            <div class="time-box number-box">
              {{ timeFilter(recordUsedSecond) }}
            </div>
          </div>
        </div>
      </div>
      <div class="close-container" v-if="rightTip.status==='close'" @click="clickRightTipStatusBtn('open')">任务信息</div>
      <img src="@/assets/ui/fdldq/common/double-right-arrow.png" alt="" class="close"
           @click="clickRightTipStatusBtn('close')" v-if="rightTip.status==='open'">
      <img src="@/assets/ui/fdldq/common/double-left-arrow.png" alt="" class="open"
           @click="clickRightTipStatusBtn('open')" v-if="rightTip.status==='close'">
    </div>
    <div class="bottom-info-container">
      <div class="bg"></div>
      <div class="container flex flex-around">
        <div class="li-box flex flex-dr flex-center" @click="clickQRBtn">
          <img src="@/assets/ui/fdldq/common/bottom-qr-icon.png" alt="" class="qr">
          <span>扫一扫</span>
        </div>
        <div class="li-box flex flex-dr flex-center" @click="clickARBtn">
          <img src="@/assets/ui/fdldq/common/bottom-ar-icon.png" alt="" class="ar">
          <span>A R</span>
        </div>
        <div class="li-box flex flex-dr flex-center" @click="clickMapBtn">
          <img src="@/assets/ui/fdldq/common/bottom-map-icon.png" alt="" class="map">
          <span>地  图</span>
        </div>
      </div>
    </div>
    <div :class="'tips-container flex flex-center flex-dr '+tipConfig.type" v-show="+tipConfig.show">
      <div class="text-box">
        <div class="title">{{ tipConfig.title }}</div>
        <div class="text">{{ tipConfig.text }}</div>
      </div>
      <div class="arrow-box">
        <van-icon name="down" size="2.6rem" color="#f1dbb4"/>
      </div>
    </div>
    <div class="ar-scan-container">
      <ar-scan-h ref="arScanH" @onRecSuccess="onRecArSuccess" @onRecError="onRecArError"></ar-scan-h>
    </div>
    <div class="qr-scan-container">
      <qr-scan-h ref="qrScanH" @onRecSuccess="onRecQrSuccess" @onRecError="onRecQrError"></qr-scan-h>
    </div>
  </div>
  <div class="full-btn full-btn-landscape" @click="screenfull.toggle()" v-if="mobileType!=='iPhone'">
    <img src="@/assets/ui/fdldq/common/full-btn-icon.png" alt="">
    <span>全屏</span>
  </div>
</template>

<script>
import screenfull from 'screenfull'
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";
import {timeFilter} from "@/filters";
import arScanH from "@/views/components/arScanH.vue"
import qrScanH from "@/views/components/qrScanH.vue"
import $ from "jquery"
import fdldq_taskConfig from "../../scripts/fdldq/taskConfig";
import {TaskModel} from "../../model/TaskModel";
import {ScriptRecordModel} from "../../model/ScriptRecordModel";
import {
  msg_alert_vant,
  msg_confirm_vant,
  msg_confirm_vant_config,
  msg_err_vant,
  msg_success_vant
} from "../../utils/nut_component";
import {UserModel} from "../../model/UserModel";
import fdldqTaskConfig from "@/scripts/fdldq/taskConfig";

export default {
  name: "infoFrame",
  components: {arScanH, qrScanH},
  computed: {
    ...mapState(TaskStore, {
      roleId: store => store.roleId,
      recordStartTime: store => store.startTime,
      gotTaskNumber: store => store.gotTaskNumber,
      gotClueNumber: store => store.gotClueNumber,
      gotDecisionNumber: store => store.gotDecisionNumber,
      nowTaskId: store => store.nowTaskId,
      recordInfo: store => store.recordInfo,
      mobileType: store => store.mobileType,
    }),
  },
  data() {
    return {
      rightTip: {
        status: "close"
      },
      fdldq_taskConfig: fdldq_taskConfig,
      nowTask: {},
      nowTaskConfig: {},
      timeFilter: timeFilter,
      directionRight: true,
      screenfull: screenfull,
      recordUsedTimeInterval: "",
      recordUsedSecond: 0,
      tipConfig: { // 底部提示配置
        type: "",
        show: false,
        title: "",
        text: ""
      }
    }
  },
  beforeCreate() {
    UserModel.loginCheck()
  },
  beforeUnmount() {
    clearInterval(this.recordUsedTimeInterval)
  },
  mounted() {
    let $this = this;
    this.taskInit();
    this.calRecordUsedTime();
    ScriptRecordModel.calStatisticNumber()
  },
  methods: {
    // 点击右侧信息框收起展开按钮
    clickRightTipStatusBtn(button) {
      if (button === "close") {
        this.rightTip.status = "close"
      }
      if (button === "open") {
        this.rightTip.status = "open"
      }
    },
    // 任务初始化
    async taskInit() {
      // 获取任务名称
      let nowTaskConfig = TaskModel.getOneTaskConfig(this.nowTaskId)
      this.nowTaskConfig = nowTaskConfig;
      let nowTask = this.recordInfo[this.nowTaskId]
      nowTask["name"] = nowTaskConfig["name"]

      document.title = this.nowTaskId + "-" + this.nowTaskConfig["name"]

      this.nowTask = nowTask
      // 任务状态判断
      if (nowTask.status === "open") { // 任务开启
        if (this.nowTaskConfig["locationNeeded"] === false) {// 如果不需要定位，就直接到达
          await TaskModel.arriveOneTaskGpsAddress(this.nowTaskId) // 标记达到定位位置
          await TaskModel.scanEdQrCode(this.nowTaskId)// 标记扫描了定位二维码
        }
        if (this.nowTaskConfig["locationNeeded"] === true && fdldqTaskConfig.configObject.mapType === 'sketch') {// 如果需要定位，但定位模式是示意图模式
          await TaskModel.arriveOneTaskGpsAddress(this.nowTaskId) // 标记达到定位位置
        }
        if (this.$route.name === "Map_Html_view") { // 在地图页
          let testChoose = await msg_confirm_vant_config("是否跳过定位判断", "", "跳过", "不跳过")
          // let testChoose=false
          if (testChoose) {
            // todo 测试阶段直接到达
            await TaskModel.arriveOneTaskGpsAddress(this.nowTaskId)
          }
        }
        if (this.$route.name === "AR_Main_View") {// 在ar主界面页

        }
      }
      if (nowTask.status === "doing") {// 任务正在进行中
        TaskModel.goToOneTaskPage(this.nowTaskId)
      }
      this.showBottomTip()
    },
    // 显示提示信息
    showBottomTip() {
      let nowTask = this.recordInfo[this.nowTaskId]
      this.nowTask = nowTask
      if (nowTask.status === "open") { // 任务开启
        if (this.$route.name === "AR_Main_View") {// 在ar主界面页
          this.tipConfig = {
            type: "map",
            show: true,
            title: "请查看地图",
            text: "前往任务开始地点"
          }
          // 弹窗提示
          if (this.nowTaskConfig.gpsTipText) {
            msg_alert_vant("任务提示", this.nowTaskConfig.gpsTipText);
          }
        }
      }
      if (nowTask.status === "arriveGpsLocation") {// 已到达任务区域内
        let titleText = "已到达任务区域"
        if (!this.nowTaskConfig.locationNeeded) {
          titleText = "扫描二维码"
        }
        this.tipConfig = {
          type: "qr",
          show: true,
          title: titleText,
          text: "请扫描入口的定位二维码"
        }
        // 弹窗提示
        if (this.nowTaskConfig.qrTipText) {
          msg_alert_vant("任务提示", this.nowTaskConfig.qrTipText);
        }
      }
      if (nowTask.status === "scanEdQrCode") {// 已扫描任务定位二维码
        this.tipConfig = {
          type: "ar",
          show: true,
          title: "任务已激活",
          text: "请扫描AR物体开始任务"
        }
        // 弹窗提示
        if (this.nowTaskConfig.arTipText) {
          msg_alert_vant("任务提示", this.nowTaskConfig.arTipText);
        }
      }
    },
    // 计算当前记录用时
    calRecordUsedTime() {
      let $this = this;
      this.recordUsedTimeInterval = setInterval(() => {
        $this.recordUsedSecond = Math.floor((new Date().getTime() - $this.recordStartTime) / 1000)
      }, 1000)
    },
    // 点击地图按钮
    clickMapBtn() {
      this.$router.push({
        name: "Map_Html_view"
      })
    },
    // 点击AR按钮
    async clickARBtn() {
      // todo 测试用
      let testChoose = await msg_confirm_vant_config("是否跳过识别", "", "跳过", "不跳过")
      if (testChoose) {
        this.onRecArSuccess("god")
        return
      }
      let nowTaskConfig = TaskModel.getOneTaskConfig(this.nowTaskId)
      let nowTask = this.nowTask
      if (nowTask.status === "scanEdQrCode") {// 已扫描定位二维码
        $(".ar-scan-container").show()
        $(".qr-scan-container").hide()
        if ((await this.$refs["arScanH"].init()) === "init_success") {
          this.tipConfig = {show: false}
        } else {
          $(".ar-scan-container").hide()
        }
      } else {
        msg_err_vant("不满足扫描AR条件！")
      }

    },
    // 当AR识别成功
    async onRecArSuccess(regResult) {
      console.log(regResult)
      let nowTaskConfig = TaskModel.getOneTaskConfig(this.nowTaskId)
      let nowTask = this.nowTask
      if (nowTask.status === "scanEdQrCode") {// 已到达任务区域内 才可以扫描定位二维码
        if (regResult === "god" || regResult.target.targetId === nowTaskConfig["arId"]) {
          msg_success_vant("识别成功！")
          $(".ar-scan-container").hide()
          // 保存任务状态
          if (await TaskModel.scanEdArCode(this.nowTaskId)) {
            TaskModel.goToOneTaskPage(this.nowTaskId)
          } else {
            alert("返回失败")
          }
        } else {
          msg_err_vant("扫描错误，请扫描本任务的AR物体！")
        }
      } else {
        msg_err_vant("不满足扫描AR条件！")
      }
    },
    // 当AR识别失败
    onRecArError(regResult) {
      console.log(regResult)
      msg_err_vant("AR识别失败，请重试！")
      this.$refs["arScanH"].clickStopBtn()
      this.showBottomTip()
    },
    // 点击QR按钮
    async clickQRBtn() {
      // todo 测试用
      let testChoose = await msg_confirm_vant_config("是否跳过识别", "", "跳过", "不跳过")
      if (testChoose) {
        this.onRecQrSuccess("god")
        return
      }
      // 判断开启条件
      let nowTaskConfig = TaskModel.getOneTaskConfig(this.nowTaskId)
      let nowTask = this.nowTask
      if (nowTask.status === "arriveGpsLocation") {// 已到达任务区域内
        $(".qr-scan-container").show()
        $(".ar-scan-container").hide()
        if (await this.$refs["qrScanH"].init() === "init_success") {
          this.tipConfig = {show: false}
        } else {
          $(".qr-scan-container").hide()
        }
      } else {
        msg_err_vant("不满足扫描定位二维码条件！")
      }
    },
    // 当QR识别成功
    async onRecQrSuccess(regResult) {
      console.log(regResult)
      let nowTaskConfig = TaskModel.getOneTaskConfig(this.nowTaskId)
      let nowTask = this.nowTask
      if (nowTask.status === "arriveGpsLocation") {// 已到达任务区域内 才可以扫描定位二维码
        if (regResult === "god" || regResult === nowTaskConfig["qrId"]) {
          msg_success_vant("扫描成功！")
          $(".qr-scan-container").hide()
          // 保存任务状态
          if (await TaskModel.scanEdQrCode(this.nowTaskId)) {
            this.showBottomTip()
          }
        } else {
          msg_err_vant("扫描错误，请扫描本任务的定位二维码！")
          this.showBottomTip()
        }
      }
    },
    // 当QR识别失败
    onRecQrError(regResult) {
      console.log(regResult)
    }
  }
}
</script>

<style scoped lang="less">
.right-info-container {
  position: fixed;
  right: 0px;
  top: 0px;
  width: 90px;
  z-index: 3;
  box-shadow: 1px 1px 3px 1px rgba(0, 0, 0, 0.2);

  &.open {

  }

  &.close {
    width: 30px;
  }

  .bg {
    width: 90px;
    height: 74px;
    background-color: #f3d8b3;
    opacity: 0.85;
    position: absolute;
    left: 0px;
    top: 0px;
    z-index: 1;

    &.open {

    }

    &.close {
      width: 30px;
    }
  }

  .container {
    width: 90px;
    position: absolute;
    left: 0px;
    top: 0px;
    z-index: 2;

    .task-name {
      color: #f09748;
      font-weight: bold;
      font-size: 8px;
      margin-top: 2px;
      margin-bottom: 2px;
      text-shadow: 0.5px 0.5px 0.5px #000;
    }

    .role-box {
      margin-right: 4px;

      img {
        width: 20px;
        height: 20px;
        object-fit: cover;
        margin-right: 2px;
        box-shadow: 1px 1px 3px 1px rgba(0, 0, 0, 0.2);
      }

      .text {
        padding: 0px 1px;
        text-align: center;
        margin-top: 2px;
        margin-bottom: 2px;
        color: #555;

        .name {
          font-size: 6.5px;
          font-weight: bold;
        }

        .des {
          font-size: 5px;
        }
      }
    }

    .number-box {
      width: 53px;
      height: 13px;
      line-height: 13px;
      color: #555;
      font-size: 5.5px;
      text-align: center;
      background-color: #f5ead5;
      border-bottom-left-radius: 2px;
      border-bottom-right-radius: 2px;
      margin-bottom: 2px;
      box-shadow: 1px 1px 3px 1px rgba(0, 0, 0, 0.2);
      font-weight: bold;

      &:last-child {
        margin-bottom: 0px;
      }
    }

    .time-box {
      color: #d5553b;
    }
  }

  .close-container {
    width: 30px;
    position: absolute;
    left: 0px;
    top: 0px;
    text-align: center;
    padding: 10px 5px;
    color: #f09748;
    font-weight: bold;
    font-size: 11px;
    margin-top: 2px;
    margin-bottom: 2px;
    text-shadow: 0.5px 0.5px 0.5px #000;
    z-index: 2;
  }

  img.close, img.open {
    position: absolute;
    left: -10px;
    top: 30px;
    z-index: 1;
    width: 7px;
    height: 8px;
  }
}


.ar-scan-container {
  position: absolute;
  display: none;
  width: 100%;
  min-height: 100vh;
  z-index: 5;
  top: 0px;
  left: 0px;
}

.qr-scan-container {
  display: none;
  position: absolute;
  width: 100%;
  min-height: 100vh;
  z-index: 5;
  top: 0px;
  left: 0px;
}

.bottom-info-container {
  position: fixed;
  bottom: 2px;
  left: 122.5px;
  width: 130px;
  height: 25px;
  padding: 1.5px 0px;
  z-index: 3;

  .bg {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 130px;
    height: 25px;
    opacity: 0.85;
    background-color: #fff;
    z-index: 1;
  }

  .container {
    position: absolute;
    left: 0px;
    top: 0px;
    width: 130px;
    height: 25px;
    z-index: 2;
    padding-top: 2px;

    .li-box {
      img {
        width: 10px;
        height: 10px;
        display: inline-block;
      }

      img.ar {
        width: 8px;
        height: 10px;
      }

      span {
        margin-top: 1px;
        color: #555;
        height: 10px;
        font-size: 7px;
        line-height: 10px;
      }
    }
  }
}

.tips-container {
  position: fixed;
  width: 80px;
  height: 80px;
  padding: 1.5px 0px;
  z-index: 2;

  &.map {
    left: 188px;
    bottom: 30px;
  }

  &.ar {
    left: 147px;
    bottom: 30px;
  }

  &.qr {
    left: 104px;
    bottom: 30px;
  }


  .text-box {
    background-color: #f3d8b3;
    opacity: 0.8;
    border-radius: 5px;
    margin-top: 6px;
    width: 80px;
    height: 25px;
    padding: 2px 5px 0px 5px;
    line-height: 11px;
    text-align: center;
    color: #b05f22;

    .title {
      font-weight: bold;
      font-size: 7px;
      border-bottom: 1px dashed #b05f22;
    }

    .text {
      font-size: 6px;
    }
  }

  .arrow-box {
    position: absolute;
    bottom: 3px;
    right: 28px;

    .van-icon {
      animation: down_arrow 0.8s 0.3s linear both infinite;
      -webkit-animation: down_arrow 0.8s 0.3s linear both infinite;
      @keyframes down_arrow {
        0% {
          bottom: 0px;
        }
        50% {
          bottom: -5px;
        }
        100% {
          bottom: 0px;
        }
      }
    }
  }
}
</style>
