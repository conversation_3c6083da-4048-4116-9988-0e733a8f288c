<template>
  <div class="tips-container" v-show="directionRight">
    <div class="title">实验提示</div>
    <div class="text-container">
      <p>实验目标：完成实验</p>
      <p>考核要求：获取线索，完成决策和任务</p>
      <p>如果不小心退出，可以从中断的地方继续开始！</p>
    </div>
    <div class="button-container">
      <van-button type="success">进入任务</van-button>
    </div>
  </div>
</template>

<script>
import {msg_err, toast_err} from "../utils/nut_component";
import { Button } from 'vant';

export default {
  name: "hTestView",
  components: {
    [Button.name]: Button,
  },
  data() {
    return {
      directionRight: true
    }
  },
  mounted() {
    let $this = this;
    window.addEventListener('orientationchange', function () {
      let direction = window.neworientation.current
      if (direction === "landscape") {
        $this.directionRight = true
      } else {
        $this.directionRight = false;
        toast_err("请切换到横屏使用本程序!")
      }
    }, false);
  }
}
</script>

<style scoped lang="less">
.tips-container {
  background-color: rgba(0, 0, 0, 0.6);
  min-height: 100vh;
  padding: 20px 20px;
}

.title {
  font-size: 10px;
  color: #fff;
  text-align: center;
}

.text-container {
  width: 70%;
  margin: 0 auto;

  p {

    color: #fff;
    font-size: 8px;
  }
}

.button-container {
  width: 70%;
  margin: 0 auto;
  text-align: center;
  margin-top: 20px;
}
</style>
