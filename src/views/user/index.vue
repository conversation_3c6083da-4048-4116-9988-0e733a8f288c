<template>
  <div v-show="screenDirection==='portrait'" class="user-index-container">
    <pageFooter :active-number="2"></pageFooter>
    <div>
      <div class="header-box flex flex-center flex-dr">
        <div class="headBg">
          <span>个人中心</span>
        </div>
        <nut-avatar class="avatar"
                    size="100"
                    icon="https://img12.360buyimg.com/imagetools/jfs/t1/143702/31/16654/116794/5fc6f541Edebf8a57/****************.png"
        ></nut-avatar>
        <div class="user-name">{{ userInfo.email ? userInfo.email : userInfo.account }}</div>
        <div class="number-box flex flex-around">
          <div class="flex flex-dr flex-center number-li">
            <span class="text">{{ userCenterInfo.scriptTaskNumber }}</span>
            <div class="flex flex-start name-box">
              <img src="@/assets/ui/platform/user/number-task.png" alt="" class="icon">
              <span class="name">参与任务</span>
            </div>

          </div>
          <div class="flex flex-dr flex-center number-li">
            <span class="text">{{ userCenterInfo.scriptClueNumber }}</span>
            <div class="flex flex-start name-box">
              <img src="@/assets/ui/platform/user/number-clue.png" alt="" class="icon">
              <span class="name">获得线索</span>
            </div>

          </div>
          <div class="flex flex-dr flex-center number-li">
            <span class="text">{{ userCenterInfo.scriptDecisionNumber }}</span>
            <div class="flex flex-start name-box">
              <img src="@/assets/ui/platform/user/number-decision.png" alt="" class="icon">
              <span class="name">参与决策</span>
            </div>

          </div>
        </div>
      </div>
      <div class="enter-box">
        <nut-cell-group>
          <nut-cell title="修改密码" is-link to="/user/changePassword">
            <template v-slot:icon>
              <img
                  class="icon password"
                  src="@/assets/ui/platform/user/li-password.png"
              />
            </template>
          </nut-cell>
          <nut-cell title="帮助中心" is-link to="/helpCenter">
            <template v-slot:icon>
              <img
                  class="icon help"
                  src="@/assets/ui/platform/user/li-help.png"
              />
            </template>
          </nut-cell>
          <nut-cell title="更多实验" is-link url="http://h5.cdzyhd.com">
            <template v-slot:icon>
              <img
                  class="icon more"
                  src="@/assets/ui/platform/user/li-more.png"
              />
            </template>
          </nut-cell>
          <nut-cell title="退出登录" @click="clickLoginOutBtn">
            <template v-slot:icon>
              <img
                  class="icon more"
                  src="@/assets/ui/platform/user/li-logout.png"
              />
            </template>
          </nut-cell>
        </nut-cell-group>
      </div>
    </div>
  </div>
  <div class="direction-tip" v-show="screenDirection==='landscape'">请切换到竖屏使用</div>
</template>

<script>
import pageFooter from "@/views/components/pageFooter.vue";
import pageHeader from "@/views/components/pageHeader.vue";
import {UserModel} from "../../model/UserModel";
import {MainStore} from "../../store/MainStore";
import {mapState} from 'pinia'
import {msg_confirm} from "../../utils/nut_component";
import {TaskStore} from "@/store/TaskStore";

export default {
  name: "userIndex",
  components: {pageFooter, pageHeader},
  computed: {
    ...mapState(MainStore, {
      userInfo: store => store.userInfo
    }),
    ...mapState(TaskStore, {
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  setup() {

  },
  beforeCreate() {
    UserModel.loginCheck()
  },
  data() {
    return {
      userCenterInfo: {}
    }
  },
  created() {
    document.title = "AR红色数字剧坊-个人中心"
  },
  async mounted() {
    this.userCenterInfo = await UserModel.getUserIndexInfo()
  },
  methods: {
    async clickLoginOutBtn() {
      if (await msg_confirm("退出登录", "确认要退出登录吗？")) {
        UserModel.loginOut()
      }
    }
  }
}
</script>
<style>
.user-index-container .enter-box .nut-cell-group__warp{
  border-radius: 0;
}
</style>
<style scoped lang="less">
.user-index-container {
  background-color: #efefef;
  min-height: 100vh;

  .header-box {
    position: relative;
    background-color: #fff;
    box-shadow: 1px 1px 2px 1px rgba(0, 0, 0, 0.1);

    .headBg {
      position: absolute;
      top: -43px;
      left: -38px;
      width: 510px;
      height: 263px;
      display: block;
      background-image: url(../../assets/ui/platform/user/headBg.png);
      background-size: cover;
      margin-bottom: 100px;
      span{
        position: absolute;
        color: #fff;
        text-align: center;
        top:60px;
        left: 185px;
        font-size: 19px;
        font-weight: bold;
      }
    }

    .avatar {
      margin-top: 83px;
      border: 3px solid #d77141;
    }

    .user-name {
      margin-top: 8px;
      color: #555;
      font-size: 17px;
      margin-bottom: 15px;
      font-weight: bold;
    }

    .number-box {
      width: 100%;
      border-top: 1px solid #f5f5f5;
      border-bottom: 1px solid #f6f6f6;
      padding: 5px 20px;

      .number-li {
        width: 33.3%;
        border-right: 1px solid #ddd;

        &:first-child{

        }
        &:last-child {
          border-right: none;
          padding-right: 0px;
        }
      }

      .name-box{
        margin-top: 10px;
        margin-bottom: 3px;
      }

      .name {
        color: #656565;
        font-size: 13px;
      }

      .icon {
        margin-right: 3px;
        height: 16px;
        width: 16px;
      }

      .text {
        font-size: 20px;
        color: #444;
        margin-bottom: -5px;
      }
    }
  }

  .enter-box{
    box-shadow: 1px 1px 2px 1px rgba(0, 0, 0, 0.1);
    .icon{
      margin-right: 10px;
      width: 12px;
      height: 15px;
      margin-top: 2px;
    }
    .icon.more{
      height: 13px;
      width: 13px;
      margin-top: 4px;
    }
  }
}

.direction-tip {
  line-height: 100vh;
  text-align: center;
  font-size: 25px;
  color: #333;
}
</style>
