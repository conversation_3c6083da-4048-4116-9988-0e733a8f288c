<template>
  <div v-show="screenDirection==='portrait'" class="forget-container">
    <pageHeader title-name="修改密码" :showLeft="false"></pageHeader>
    <div>
      <div class="input-form flex flex-dr flex-center">
        <input type="password" placeholder="请输入原密码" v-model="passwordData.password0">
        <input type="password" placeholder="请输入新密码" v-model="passwordData.password1">
        <input type="password" placeholder="请再次输入新密码" v-model="passwordData.password2">
        <div class="sure-btn common-block-btn" @click="clickSureBtn">确认修改</div>
      </div>

    </div>
  </div>
  <div class="direction-tip" v-show="screenDirection==='landscape'">请切换到竖屏使用</div>
</template>
<script>
import {ref} from 'vue';
import pageHeader from "@/views/components/pageHeader.vue";
import {msg_err, msg_success, toast_err} from "@/utils/nut_component";
import {UserModel} from "@/model/UserModel";
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";
// todo 访问setup里面的ref
let formRef = undefined;
export default {
  name: "userChangePassword",
  components: {pageHeader},
  computed: {
    ...mapState(TaskStore, {
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  data() {
    return {
      passwordData: {
        password0: "",
        password1: "",
        password2: "",
      },
    }
  },
  async mounted() {
  },
  methods: {
    // 点击确认修改按钮
    async clickSureBtn() {
      if (!this.passwordData.password0) {
        toast_err("请输入原密码！")
        return
      }

      let pPattern = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[a-zA-Z0-9]{6,18}$/;
      if (!pPattern.test(this.passwordData.password1)) {
        toast_err("新密码要求6-18位，且要有大写字母、小写字母、数字！")
        return
      }
      if (this.passwordData.password1 !== this.passwordData.password2) {
        toast_err("两次新密码输入不一致！")
        return
      }
      let regResult = await UserModel.changePassword({
        oldPassword: this.passwordData.password0,
        newPassword: this.passwordData.password1,
      })
      if (regResult) {
        msg_success("密码修改成功,请重新登录！")
        setTimeout(() => {
          window.location.href = "/login"
        }, 1000)
      }
    },

  },
}
</script>
<style scoped lang="less">
.forget-container {
  background-color: #efefef;
  min-height: 100vh;
}

.input-form {
  width: 300px;
  margin: 0 auto;
  padding-top: 50px;

  input {
    border: 1px solid #b8b8b8;
    width: 315px;
    padding: 10px 20px;
    margin-bottom: 23px;
    border-radius: 20px;
    color: #333;
  }

  input::-webkit-input-placeholder {
    color: #8e8e8e;
  }

  .sure-btn {
    margin-top: 30px;

  }

}
</style>
