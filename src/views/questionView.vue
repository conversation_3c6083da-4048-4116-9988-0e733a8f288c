<template>
  <div v-show="screenDirection==='portrait'" class="article-page-container">
    <pageHeader title-name="常见问题" :showLeft="false"></pageHeader>
    <div class="question-container">
      <div class="name">问题：{{ question.name }}</div>
      <div class="html-view" v-html="question.content"></div>
    </div>
  </div>
  <div class="direction-tip" v-show="screenDirection==='landscape'">请切换到竖屏使用</div>
</template>

<script>
import pageFooter from "@/views/components/pageFooter.vue";
import pageHeader from "@/views/components/pageHeader.vue";
import {HelpQuestionModel} from "../model/HelpQuestionModel";
import {getQuery} from "../utils/common";
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";

export default {
  name: "articleView",
  computed: {
    ...mapState(TaskStore, {
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  components: {pageFooter, pageHeader},
  data() {
    return {
      question: {}
    }
  },
  async mounted() {
    let questionId = getQuery("id")
    this.question = await HelpQuestionModel.getOne(questionId)
  }
}
</script>

<style scoped lang="less">
.article-page-container {
  background-color: #efefef;
  min-height: 100vh;
}

.question-container {
  padding: 30px 20px;
  .name {
    margin-bottom: 30px;
    font-size: 15px;
    color: #333;
  }

  .html-view {
    line-height: 15px;
  }
}
</style>
