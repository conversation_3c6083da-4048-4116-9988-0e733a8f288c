<template>
  <div v-show="screenDirection==='portrait'" class="help-center-container">
    <pageHeader title-name="帮助中心" :showLeft="false"></pageHeader>
    <pageFooter :active-number="-1"></pageFooter>
    <div>
      <div class="question-list">
        <nut-tabs v-model="typeIndex" title-scroll title-gutter="10" @click="getQuestionList">
          <nut-tabpane v-for="type in helpTypeEnum" :title="type.label" :paneKey="type.label">
            <nut-cell :title="question.name" is-link v-for="question in list"
                      :to="'/questionView?id='+question.helpQuestionId"></nut-cell>
          </nut-tabpane>
        </nut-tabs>
      </div>
    </div>
  </div>
  <div class="direction-tip" v-show="screenDirection==='landscape'">请切换到竖屏使用</div>
</template>

<script>
import pageFooter from "@/views/components/pageFooter.vue";
import pageHeader from "@/views/components/pageHeader.vue";
import enums from "@/enums/index"
import {objectToLVArr} from "../utils/common";
import {HelpQuestionModel} from "../model/HelpQuestionModel";
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";

export default {
  name: "helpCenter",
  computed: {
    ...mapState(TaskStore, {
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  components: {pageFooter, pageHeader},
  beforeCreate() {

  },
  data() {
    return {
      typeIndex: "常见问题",
      helpTypeEnum: objectToLVArr(enums.helpQuestionType),
      questionList: {},
      list: []
    }
  },
  mounted() {
    document.title = "AR红色数字剧坊-帮助中心"
    this.getQuestionList({title: "常见问题"})
  },
  methods: {
    // 获取某个类型的问题列表
    async getQuestionList(v) {
      let type = v.title
      if (!this.questionList.hasOwnProperty("type")) {
        let list = await HelpQuestionModel.getList({type: type})
        this.questionList[type] = list
        this.list = list
      }

    }
  }

}
</script>
<style>
.help-center-container .nut-tabs__titles {
  background-color: #efefef;
}

.help-center-container .nut-tabpane {
  background: none;
}
</style>
<style scoped lang="less">
.help-center-container {
  background-color: #efefef;
  min-height: 100vh;
}

.question-list {
  padding-top: 50px;
}
</style>
