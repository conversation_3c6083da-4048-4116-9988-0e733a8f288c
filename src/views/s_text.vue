<template>
  <div class="text-container" v-show="directionRight">
    <div class="text-content" v-for="item in textInfo.content.split('\n')">{{ item }}</div>
    <div class="button-box">
      <van-button type="success" size="normal" @click="clickSureChooseBtn">确&nbsp;&nbsp;认</van-button>
    </div>
  </div>

  <van-button type="danger" size="small" class="full-btn" @click="screenfull.toggle()">全屏</van-button>
</template>

<script>
import {msg_err, msg_err_vant, msg_success_vant, toast_err} from "../utils/nut_component";
import {Button} from 'vant';
import {ref, onBeforeUpdate} from 'vue';
import screenfull from 'screenfull'

export default {
  name: "S_Text_View",
  components: {},
  data() {
    return {
      screenfull: screenfull,
      directionRight: true,
      textInfo: {
        content: "1934年10月，第五次反“围剿”失败后，中央主力红军为摆脱国民党军队的围追堵截，被迫实行战略转移，进行长征。\n" +
            "红军虽渡过湘江，但损失惨重，蒋介石调整部署，企图围歼中央红军于湘西。在此危急关头，毛泽东力主放弃原定计划，改向国民党统治力量薄弱的贵州前进。此后，中央红军先后占领贵州省黎平、遵义等地。"
      }
    }
  },
  mounted() {
    let $this = this;
    window.addEventListener('orientationchange', function () {
      let direction = window.neworientation.current
      if (direction === "landscape") {
        $this.directionRight = true
      } else {
        $this.directionRight = false;
        toast_err("请切换到横屏使用本程序!")
      }
    }, false);
  },
  methods: {}
}
</script>

<style scoped lang="less">
.full-btn {
  position: fixed;
  left: 20px;
  bottom: 0px;
}

.text-container {
  background-color: rgba(0, 0, 0, 0.6);
  min-height: 100vh;
  padding: 10px;

  .text-content {
    color: #fff;
    font-size: 8px;
    text-indent: 2em;
    line-height: 10px;
  }
}

.button-box {
  margin-top: 10px;
  text-align: center;

  .van-button {
    margin-right: 30px;
  }
}
</style>
