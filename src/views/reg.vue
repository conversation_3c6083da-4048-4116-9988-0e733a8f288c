<template>
  <div class="reg-container" v-show="screenDirection==='portrait'">
    <div class="title-box">
      <img class="title_img" src="@/assets/ui/platform/login/title.png" alt="">
      <div class="title">注册</div>
    </div>
    <div class="reg-form">
      <div class="input-box mail-box">
        <img src="@/assets/ui/platform/reg/mail.png" alt="" class="icon">
        <input type="text" placeholder="请输入邮箱，用于找回密码" v-model="regData.email">
      </div>
      <div class="input-box code-box">
        <img src="@/assets/ui/platform/reg/code.png" alt="" class="icon code">
        <input type="text" placeholder="请输入验证码" v-model="regData.emailCode" :disabled="!regData.codeSend">
        <div class="code-btn common-short-btn" @click="clickGetCodeBtn" :disabled="regData.codeSend">
          {{ regInfo.codeSend ? "重新获取(" + regInfo.sendMailSecond + "S)" : regInfo.sendMailText }}
        </div>
      </div>
      <div class="input-box password1-box">
        <img src="@/assets/ui/platform/reg/password.png" alt="" class="icon password">
        <input type="password" placeholder="请输入密码" v-model="regData.password1">
      </div>
      <div class="input-box password2-box">
        <img src="@/assets/ui/platform/reg/password.png" alt="" class="icon password">
        <input type="password" placeholder="请再次输入密码" v-model="regData.password2">
      </div>
      <div class="input-box sex-box flex flex-between">
        <span class="title">性别</span>
        <nut-radiogroup v-model="regData.sex" direction="horizontal">
          <nut-radio label="男">男</nut-radio>
          <nut-radio label="女">女</nut-radio>
        </nut-radiogroup>
      </div>
      <div class="reg-btn common-block-btn" @click="clickRegBtn">提交注册</div>
      <div class="login-btn" @click="clickLoginBtn">已有账号点此登录</div>
    </div>
  </div>

  <div class="direction-tip" v-show="screenDirection==='landscape'">请切换到竖屏使用</div>
</template>
<script>
import {ref} from 'vue';
import {msg_err, msg_success, toast_err} from "../utils/nut_component";
import {regCheck} from "../utils/common";
import {UserModel} from "../model/UserModel";
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";

export default {
  computed: {
    ...mapState(TaskStore, {
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  data() {
    return {
      regData: {
        password1: "",
        password2: "",
        email: "",
        sex: "男"
      },
      regInfo: {
        codeSend: false,
        sendMailText: "获取验证码",
        sendMailSecond: 60,
        timeId: false
      }
    }
  },
  watch: {},
  async mounted() {
    document.title = "注册账号"
  },
  beforeUnmount() {
    if (this.regInfo.timeId) {
      clearInterval(this.regInfo.timeId)
    }
  },
  methods: {
    // 点击登录按钮
    clickLoginBtn() {
      this.$router.push("/login")
    },
    // 邮箱发送倒计时
    mailSecondCount() {
      this.regInfo.timeId = setInterval(() => {
        if (this.regInfo.sendMailSecond > 1) {
          this.regInfo.sendMailSecond -= 1
        } else {
          this.regInfo.sendMailSecond = 60
          this.regInfo.codeSend = false
          clearInterval(this.regInfo.timeId)
        }
      }, 1000)
    },
    // 点击获取验证码按钮
    async clickGetCodeBtn() {
      let email = this.regData.email;
      if (!email) {
        msg_err("请先输入您的邮箱地址")
        return
      }
      if (!regCheck("email", email)) {
        msg_err("邮箱格式错误")
        return;
      }
      this.regInfo.codeSend = true
      this.regInfo.sendMailSecond = 60
      if (await UserModel.sendRegEmail(this.regData.email).catch(res => {
        this.regInfo.codeSend = false
        return
      })) {
        this.regInfo.sendMailSecond = 60
        this.mailSecondCount()
        this.regInfo.codeSend = true
      }
    },
    // 点击注册按钮
    async clickRegBtn() {
      if (!regCheck("email", this.regData.email)) {
        msg_err("邮箱格式错误")
        return;
      }
      let pPattern = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[a-zA-Z0-9]{6,18}$/;
      if (!pPattern.test(this.regData.password1)) {
        msg_err("密码6-18位，且需要有大写字母、小写字母、数字")
        return
      }
      if (!this.regInfo.codeSend) {
        msg_err("请先获取邮箱验证码后再试！")
        return
      }
      let re = /\d{6}$/;
      if (!re.test(this.regData.emailCode)) {
        msg_err("邮箱验证码格式不正确！")
        return
      }
      if (this.regData.password1 !== this.regData.password2) {
        msg_err("两次新密码输入不一致！")
        return
      }
      let regResult = await UserModel.regAccount({
        password: this.regData.password2,
        sex: this.regData.sex,
        email: this.regData.email,
        emailCode: this.regData.emailCode,
      })
      if (regResult) {
        msg_success("注册成功,请登录！")
        setTimeout(() => {
          window.location.href = "/login"
        }, 1000)
      }
    },

  },
}
</script>
<style>
.reg-container .nut-radio__label {
  color: #fff;
}
</style>
<style scoped lang="less">
.reg-container {
  background-color: #cecece;
  min-height: 100vh;
  background-image: url(../assets/ui/platform/login/bg.jpg);
  background-size: cover;
  background-position: 50% 50%;
}

.title-box {
  text-align: center;
  padding-top: 55px;
  margin-bottom: 30px;

  .title_img {
    width: 214px;
    height: 116px;
  }

  .title {
    margin-top: 20px;
    font-size: 26px;
    color: #cd3020;
    text-align: center;
    font-weight: bold;
  }
}

.reg-form {
  width: 310px;
  margin: 0 auto;

  .input-box {
    margin-bottom: 22px;
    position: relative;

    .icon {
      position: absolute;
      display: inline-block;
      width: 21px;
      height: 16px;
      top: 11px;
      left: 18px;

      &.code {
        width: 19px;
        height: 23px;
        top: 8px;
      }

      &.password {
        width: 18px;
        height: 22px;
        top: 8px;
      }
    }

    input {
      border: 1px solid #fff;
      width: 315px;
      padding: 10px 50px;
      border-radius: 20px;
      color: #fff;
    }

    input::-webkit-input-placeholder {
      color: #dedede;
    }
  }

  .code-box {
    .code-btn {
      position: absolute;
      right: 0px;
      top: 5px;
    }
  }

  .sex-box {
    width: 100%;
    padding: 0px 30px 0px 20px;

    .title {
      color: #fff;
      font-size: 16px;
      margin-top: -5px;
    }
  }


  .reg-btn {
    font-weight: bold;
    margin-bottom: 20px;
  }

  .login-btn {
    width: 120px;
    margin: 0 auto;
    color: #eec6bb;
    font-size: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eec6bb;
  }
}
</style>
