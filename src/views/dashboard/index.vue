<template>
  <div class="dashboard-container">
    <component :is="currentRole"/>
  </div>
</template>

<script>
import {mapGetters} from 'vuex'
import dashboard from "@/views/dashboard/dashboard";
export default {
  name: 'Dashboard',
  components: {dashboard},
  data() {
    return {
      currentRole: 'dashboard'
    }
  },
  computed: {
    ...mapGetters([
      'roles'
    ])
  },
  created() {
    if (this.roles.includes('picoDealer')) {
      this.currentRole = 'dashboard'
    }
  }
}
</script>
