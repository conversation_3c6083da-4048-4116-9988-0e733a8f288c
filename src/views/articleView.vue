<template>
  <div v-show="screenDirection==='portrait'">
    <pageHeader title-name="文章详情" :showLeft="false"></pageHeader>
    <pageFooter :active-number="-1"></pageFooter>
    <div class="article-page-container">
      <div class="html-view" v-html="htmlText"></div>
    </div>
  </div>

  <div class="direction-tip" v-show="screenDirection==='landscape'">请切换到竖屏使用</div>
</template>

<script>
import pageFooter from "@/views/components/pageFooter.vue";
import pageHeader from "@/views/components/pageHeader.vue";
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";

export default {
  name: "articleView",
  computed: {
    ...mapState(TaskStore, {
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  components: {pageFooter, pageHeader},
  data() {
    return {
      htmlText: "<p>123</p><p>456</p>"
    }
  }
}
</script>

<style scoped lang="less">

</style>
