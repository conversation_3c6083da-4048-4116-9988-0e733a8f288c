<template>
  <div>
    <div style="text-align: center;margin-bottom: 30px;margin-top: 30px;font-size: 23px;">实时定位信息-坐标采集</div>
    <div style="margin-left: 30px;">
      <div class="flex flex-start li">
        <span>纬度：</span>
        <span>{{ gpsInfo.coords.latitude }}</span>
      </div>
      <div class="flex flex-start li">
        <span>经度：</span>
        <span>{{ gpsInfo.coords.longitude }}</span>
      </div>
      <div class="flex flex-start li">
        <span>时间：</span>
        <span>{{ dateFormat(gpsInfo.timestamp, "yyyy-MM-dd HH:mm:ss") }}</span>
      </div>
      <div class="flex flex-start li">
        <span>高度：</span>
        <span>{{ gpsInfo.coords.altitude }} 米</span>
      </div>
      <div class="flex flex-start li">
        <span>精度：</span>
        <span>{{ gpsInfo.coords.accuracy }} 米</span>
      </div>
      <div class="flex flex-start li">
        <span>移动方向：</span>
        <span>{{ gpsInfo.coords.heading }} 正北偏移</span>
      </div>
      <div class="flex flex-start li">
        <span>速度：</span>
        <span>{{ gpsInfo.coords.speed }} 米/秒</span>
      </div>
      <div class="flex flex-around li" style="margin-top: 30px;text-align: center;margin-bottom: 30px">
        <nut-button type="default" @click="clickRecordOnceBtn" style="margin-right: 20px">单次记录</nut-button>
        <nut-button type="default" @click="clickRecordMultiBtn">多次记录</nut-button>
      </div>
      <div class="flex flex-start li" v-for="item in list">
        <span>{{ item[0] }} , {{ item[1] }}</span>
      </div>
      <div class="flex flex-start li">
        <span>坐标平均值：</span>
        <span>{{ longAv }} , {{ latAv }}</span>
      </div>
    </div>
  </div>
</template>

<script>/**
 * coords.latitude  十进制数的纬度
 coords.longitude  十进制数的经度
 coords.accuracy  位置精度
 coords.altitude  海拔，海平面以上以米计
 coords.altitudeAccuracy  位置的海拔精度
 coords.heading  方向，从正北开始以度计
 coords.speed  速度，以米/每秒计
 timestamp  响应的日期/时间
 */
import {dateFormat} from "../../filters";
import {GpsModel} from "../../model/GpsModel";
import {msg_confirm} from "../../utils/nut_component";

export default {
  name: "gpsNowView",
  data() {
    return {
      dateFormat: dateFormat,
      gpsInfo: {
        timeStamp: {},
        coords: {},
      },
      list: [],
      longAv: "",
      latAv: ""
    }
  },
  mounted() {
    this.getGPSInfo();
    // alert(GpsModel.wgs84PointsDistance(104.020646441, 30.628016885, 104.020697567, 30.627981778))
  },
  methods: {
    // 获取GPS信息
    getGPSInfo() {
      let $this = this;
      if (navigator.geolocation) {
        // getCurrentPosition支持三个参数
        // getSuccess是执行成功的回调函数
        // getError是失败的回调函数
        // getOptions是一个对象，用于设置getCurrentPosition的参数
        // 后两个不是必要参数
        var getOptions = {
          //是否使用高精度设备，如GPS。默认是true
          enableHighAccuracy: true,
          //超时时间，单位毫秒，默认为0
          timeout: 5000,
          //使用设置时间内的缓存数据，单位毫秒
          //默认为0，即始终请求新数据
          //如设为Infinity，则始终使用缓存数据
          maximumAge: 0
        };

        //navigator.geolocation.getCurrentPosition(getSuccess, getError, getOptions);

        //成功回调
        function getSuccess(position) {
          $this.gpsInfo = position
          // getCurrentPosition执行成功后，会把getSuccess传一个position对象
          // position有两个属性，coords和timeStamp
          // timeStamp表示地理数据创建的时间？？？？？？
          // coords是一个对象，包含了地理位置数据
          // console.log(position.timeStamp);

          // 估算的纬度
          // console.log(position.coords.latitude);
          // 估算的经度
          // console.log(position.coords.longitude);
          // 估算的高度 (以米为单位的海拔值)
          // console.log(position.coords.altitude);
          // 所得经度和纬度的估算精度，以米为单位
          // console.log(position.coords.accuracy);
          // 所得高度的估算精度，以米为单位
          // console.log(position.coords.altitudeAccuracy);
          // 宿主设备的当前移动方向，以度为单位，相对于正北方向顺时针方向计算
          // console.log(position.coords.heading);
          // 设备的当前对地速度，以米/秒为单位
          // console.log(position.coords.speed);
          // 除上述结果外，Firefox还提供了另外一个属性address
          // if (position.address) {
          //通过address，可以获得国家、省份、城市
          // console.log(position.address.country);
          // console.log(position.address.province);
          // console.log(position.address.city);
        }

        //失败回调
        function getError(error) {
          // 执行失败的回调函数，会接受一个error对象作为参数
          // error拥有一个code属性和三个常量属性TIMEOUT、PERMISSION_DENIED、POSITION_UNAVAILABLE
          // 执行失败时，code属性会指向三个常量中的一个，从而指明错误原因
          switch (error.code) {
            case error.TIMEOUT:
              console.log('超时');
              break;
            case error.PERMISSION_DENIED:
              console.log('用户拒绝提供地理位置');
              break;
            case error.POSITION_UNAVAILABLE:
              console.log('地理位置不可用');
              break;
            default:
              break;
          }
        }

        // watchPosition方法一样可以设置三个参数
        // 使用方法和getCurrentPosition方法一致，只是执行效果不同。
        // getCurrentPosition只执行一次
        // watchPosition只要设备位置发生变化，就会执行
        $this.watcher_id = navigator.geolocation.watchPosition(getSuccess, getError, getOptions);
        //clearwatch用于终止watchPosition方法
        //clearWatch(watcher_id);

      }
    },
    // 10秒延时
    await5Second() {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          resolve(true)
        }, 5000)
      })
    },
    // 点击单次记录按钮
    clickRecordOnceBtn() {
      if (this.gpsInfo) {
        this.list.push(
            [this.gpsInfo.coords.longitude, this.gpsInfo.coords.latitude]
        )
      }
    },
    // 点击多次记录按钮
    async clickRecordMultiBtn() {
      if (await msg_confirm("开始采集", "将清空位置记录，重新采集。请将手机放置在无遮挡位置，耐心等待60秒完成采集！")) {
        if (this.gpsInfo) {
          this.list=[]
          this.longAv =0
          this.latAv = 0
          await this.await5Second()
          this.list.push(
              [this.gpsInfo.coords.longitude, this.gpsInfo.coords.latitude]
          )
          await this.await5Second()
          this.list.push(
              [this.gpsInfo.coords.longitude, this.gpsInfo.coords.latitude]
          )
          await this.await5Second()
          this.list.push(
              [this.gpsInfo.coords.longitude, this.gpsInfo.coords.latitude]
          )
          await this.await5Second()
          this.list.push(
              [this.gpsInfo.coords.longitude, this.gpsInfo.coords.latitude]
          )
          await this.await5Second()
          this.list.push(
              [this.gpsInfo.coords.longitude, this.gpsInfo.coords.latitude]
          )
          await this.await5Second()
          this.list.push(
              [this.gpsInfo.coords.longitude, this.gpsInfo.coords.latitude]
          )
          await this.await5Second()
          this.list.push(
              [this.gpsInfo.coords.longitude, this.gpsInfo.coords.latitude]
          )
          await this.await5Second()
          this.list.push(
              [this.gpsInfo.coords.longitude, this.gpsInfo.coords.latitude]
          )
          await this.await5Second()
          this.list.push(
              [this.gpsInfo.coords.longitude, this.gpsInfo.coords.latitude]
          )
          await this.await5Second()
          this.list.push(
              [this.gpsInfo.coords.longitude, this.gpsInfo.coords.latitude]
          )
          await this.await5Second()
          this.list.push(
              [this.gpsInfo.coords.longitude, this.gpsInfo.coords.latitude]
          )
          await this.await5Second()
          this.list.push(
              [this.gpsInfo.coords.longitude, this.gpsInfo.coords.latitude]
          )
          this.clickAvBtn()
        }
      }
    },
    // 点击求平均值按钮
    clickAvBtn() {
      if (this.list.length >= 1) {
        let longTotal = 0;
        let latTotal = 0;
        for (let i = 0; i < this.list.length; i++) {
          longTotal += this.list[i][0]
          latTotal += this.list[i][1]
        }
        this.longAv = longTotal / this.list.length
        this.latAv = latTotal / this.list.length
      }
    }
  }
}
</script>

<style scoped lang="less">
.li {
  margin-bottom: 10px;

  span:first-child {
    color: #333;
    font-size: 25px;
  }

  span:last-child {
    color: #888;
    font-size: 25px;
  }
}
</style>
