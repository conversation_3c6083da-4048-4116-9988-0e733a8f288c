<template>
  <div class="page-container">
    <div class="map-container">
      <img src="@/assets/imgs/map.png" alt="">
      <i id="dot"></i>
    </div>
    <div class="info-container">
      <div class="li">x:{{ dotX }}</div>
      <div class="li">y:{{ dotY }}</div>
      <div class="li">lon:{{ position.coords.longitude }}</div>
      <div class="li">lat:{{ position.coords.latitude }}</div>
      <div class="li" style="text-align: center"><button class="btn-start" @click="clickSetStartBtn">设为起点</button></div>
    </div>
  </div>
</template>

<script>
import {dateFormat} from "../../filters";
import {GpsModel} from "../../model/GpsModel";
import fdldq_taskConfig from "../../scripts/fdldq/taskConfig";

export default {
  name: "mapTestView",
  filters: {dateFormat},
  data() {
    return {
      // gps watcherId
      watcherId: "",
      // gps配置
      gpsOptions: {
        //是否使用高精度设备，如GPS。默认是true
        enableHighAccuracy: true,
        //超时时间，单位毫秒，默认为0
        timeout: 5000,
        //使用设置时间内的缓存数据，单位毫秒
        //默认为0，即始终请求新数据，其它时间则按时间刷新
        //如设为Infinity，则始终使用缓存数据
        maximumAge: 0
      },
      position: {
        coords: {},
      },
      gpsModel: "",
      dot: {},
      dotX: "",
      dotY: "",
      dotTimeId: ""
    }
  },
  mounted() {
    console.log(JSON.stringify(fdldq_taskConfig))
    alert(GpsModel.wgs84PointsDistance(104.020129537,30.628389594,104.020448,30.627968))
    document.title = "美术地图测试"
    document.getElementsByClassName("map-container")[0].style.width = "1000px"
    document.getElementsByClassName("map-container")[0].style.height = "1000px"
    this.dot = document.getElementById("dot")
    this.dot.style.left = "500px"
    this.dot.style.bottom = "500px"
    this.watchGps();
  },
  beforeDestroy() {
    clearInterval(this.dotTimeId)
  },
  methods: {
    // 监视gps位置变化
    watchGps() {
      this.watcherId = navigator.geolocation.watchPosition((position) => {
        this.position = position
        if (!this.gpsModel) {// 初始化
          this.gpsModel = new GpsModel(this.dot)
          this.watchDot();
        } else {// 位置变动
          this.gpsModel.onChange(position)
        }
      }, (error) => {
        switch (error.code) {
          case error.TIMEOUT:
            //alert('超时');
            break;
          case error.PERMISSION_DENIED:
            alert('用户拒绝提供地理位置');
            break;
          case error.POSITION_UNAVAILABLE:
            alert('地理位置不可用');
            break;
          default:
            break;
        }
      }, this.gpsOptions);
    },
    // 点击设为起点按钮
    clickSetStartBtn() {
      this.gpsModel.setStartPoint()
    },
    // 监视dot变化
    watchDot() {
      this.dotTimeId = setInterval(() => {
        this.dotX = parseFloat(this.dot.style.left.replace("px")) - 500;
        this.dotY = parseFloat(this.dot.style.bottom.replace("px")) - 500;
      }, 1000)
    }
  }
}
</script>

<style scoped lang="less">
.map-container {
  position: relative;

  img {
    position: absolute;
    left: 0px;
    top: 0px;
    z-index: 1;
  }

  i#dot {
    display: block;
    z-index: 9999;
    position: absolute;
    background-color: red;
    width: 50px;
    height: 50px;
    border-radius: 25px;
  }

  button.btn-start {
    position: absolute;
    z-index: 9999;
    right: 0px;
    bottom: 0px;
  }
}

.info-container {
  position: fixed;
  width: 300px;
  top: 0px;
  right: 0px;
  background: rgba(0, 0, 0, 0.3);
  z-index: 9999;
  padding: 30px;

  .li {
    font-size: 22px;
    margin-top: 5px;
  }
}
</style>
