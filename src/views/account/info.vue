<template>
  <div class="app-container">
    <div class="info-container">
      <el-form>
        <el-form-item label="账号：">
          {{ user.userName }}
        </el-form-item>
        <el-form-item label="姓名：">
          {{ user.name }}
        </el-form-item>
        <el-form-item label="角色：">
          {{ user.roles | rolesNameFilter }}
        </el-form-item>
        <el-form-item>
          <el-button size="small" @click="password.dialog=true">修改密码</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!--修改密码弹窗-->
    <el-dialog
      title="修改密码"
      :visible.sync="password.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="600px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="140px" ref="passwordForm" :model="password.edit" :rules="password.formRules">
          <el-form-item label="请输入新密码:" prop="password1">
            <el-input v-model.trim="password.edit.password1" type="password">
            </el-input>
          </el-form-item>
          <el-form-item label="请再次输入新密码:" prop="password2">
            <el-input v-model="password.edit.password2" type="password"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="password.dialog=false">取 消</el-button>
        <el-button type="success" @click="clickChangePasswordBtn()">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {mapGetters, mapState} from 'vuex'
import enums from '@/enums/index'
import {dateFormat, rolesNameFilter} from "@/filters";
import {validateMaxLength} from "@/utils/validate";
import {msg_err, msg_success} from "@/utils/ele_component";
import {AdminUserModel} from "@/model/AdminUserModel";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";

export default {
  name: 'info',
  computed: {
    ...mapGetters([
      'user'
    ]),
    ...mapState({
      adminUserId: state => state.user.id,
    })
  },
  directives: {
    elDragDialog, permission
  },
  filters: {dateFormat, rolesNameFilter},
  data() {
    return {
      enums: enums,
      password: {
        dialog: false,
        userInfo: {},
        edit: {
          password1: "",
          password2: ""
        },
        // 输入检测
        formRules: {
          'password1': {required: true, pattern: /^\w{6,}$/, message: '密码至少6位'},
          'password2': {required: true, pattern: /^\w{6,}$/, message: '密码至少6位'},
        },
      }
    }
  },
  mounted() {

  },
  methods: {
    // 点击修改密码弹窗的提交按钮
    clickChangePasswordBtn() {
      this.$refs['passwordForm'].validate(async validate => {
        if (validate) {
          if (this.password.edit.password1 !== this.password.edit.password2) {
            msg_err("两次密码输入不一样！")
            return
          }
          let result = await AdminUserModel.changePassword(this.password.edit.password1, this.user["roles"][0])
          if (result.code === "000000") {
            msg_success("修改密码成功")
            this.password.dialog = false
          }
        }
      });
    }
  }
}
</script>

<style scoped lang="scss">

</style>
