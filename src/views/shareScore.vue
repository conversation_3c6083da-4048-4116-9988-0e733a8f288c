<template>
  <div class="share-score-container flex flex-dr flex-center">
    <div class="title">您的成绩截图如下</div>
    <img :src="imgUrl" alt="成绩截图" v-if="imgUrl">
    <div class="des">可长按图片保存，或使用微信分享功能分享</div>
  </div>
</template>

<script>
import { getQuery } from "@/utils/common";
import { FileModel } from "@/model/FileModel";
import { msg_err } from "@/utils/nut_component";
import { FILE_URL } from "@/config/main";

export default {
  name: "shareScore",
  data() {
    return {
      imgUrl: "",
      baseUrl: FILE_URL
    }
  },
  async mounted() {
    // 从URL获取fileId参数
    const fileId = getQuery("fileId");
    if(!fileId) {
      msg_err("缺少必要的文件ID参数");
      return;
    }
    
    // 通过Model层获取分享成绩信息
    const fileInfo = await FileModel.getShareScoreInfo(fileId);
    if(fileInfo) {
      this.imgUrl = this.baseUrl + fileInfo.location;
    } else {
      msg_err("获取成绩信息失败");
    }
  }
}
</script>

<style scoped lang="less">
.share-score-container {
  min-height: 100vh;
  padding: 20px;
  
  .title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  
  img {
    max-width: 100%;
    height: auto;
  }

  .des {
    font-size: 15px;
    font-weight: bold;
    margin-top: 20px;
  }
}
</style> 