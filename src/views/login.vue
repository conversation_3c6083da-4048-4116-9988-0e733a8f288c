<template>
  <div v-show="screenDirection==='portrait'" class="login-container">
    <div class="title-box">
      <img class="title_img" src="@/assets/ui/platform/login/title.png" alt="">
    </div>
    <div class="login-form">
      <div class="login-type">
        <div :class="loginData.loginType==='account'?'left active':'left'" @click="clickLoginType('account')">账号登录</div>
        <div :class="loginData.loginType==='email'?'right active':'right'" @click="clickLoginType('email')">邮箱登录</div>
      </div>
      <div class="input-box account-box" v-if="loginData.loginType==='account'">
        <input type="text" placeholder="账号" v-model="loginData.account">
        <input type="password" placeholder="密码" v-model="loginData.password">
      </div>
      <div class="input-box email-box" v-if="loginData.loginType==='email'">
        <input type="text" placeholder="邮箱" v-model="loginData.email">
        <input type="password" placeholder="密码" v-model="loginData.password">
      </div>
      <div class="btn-box">
        <div class="login-btn" @click="clickLoginBtn">登录系统</div>
        <img src="@/assets/ui/platform/login/forgetBtn.png" alt="" class="forget-btn" @click="clickForgetPwdBtn">
      </div>
    </div>

  </div>

  <div class="direction-tip" v-show="screenDirection==='landscape'">请切换到竖屏使用</div>
</template>
<script>
import {ref} from 'vue';
import {regCheck} from "../utils/common";
import {msg_err, msg_success} from "../utils/nut_component";
import {UserModel} from "../model/UserModel";
import {MainStore} from "../store/MainStore";
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";
// todo 访问setup里面的ref
let formRef = undefined;
export default {
  name: "loginNew",
  computed: {
    ...mapState(TaskStore, {
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  data() {
    return {
      loginData: {
        loginType: "email"
      }
    }
  },
  mounted() {
    document.title = "登录"
  },
  methods: {
    // 点击切换登录方式按钮
    clickLoginType(type) {
      this.loginData.loginType = type
    },
    // 点击登录按钮
    async clickLoginBtn() {
      let username = ""
      if (this.loginData.loginType === "account") {
        username = this.loginData.account

        if (!username) {
          msg_err("请输入账号!")
          return
        }
      }
      if (!this.loginData.password) {
        msg_err("请输入密码!")
        return
      }
      if (this.loginData.loginType === "email") {
        username = this.loginData.email
        if (!username) {
          msg_err("请输入账号或邮箱!")
          return
        }
        if (!regCheck("email", this.loginData.email)) {
          msg_err("邮箱格式错误")
          return;
        }
      }
      let loginResult = await UserModel.loginInPlatform(username, this.loginData.password)
      if (loginResult) {
        msg_success("登录成功")
        this.$router.push("/hall")
        // 正常登录
      }
    },
    // 点击忘记密码按钮
    async clickForgetPwdBtn() {
      let email = this.loginData.email;
      if (this.loginData.loginType === "email") {
        if (!email) {
          msg_err("请先输入您的邮箱！")
          return
        }
        if (!regCheck("email", email)) {
          msg_err("邮箱格式错误")
          return;
        }
        if (await UserModel.sendForgetEmail(email)) {

        }
      }
      if (this.loginData.loginType === "account") {
        msg_success("账号登录方式，请联系管理人员重置密码！")
      }
    },
    reset() {
      formRef.value.reset();
    }
  },
  setup() {
    let regForm = ref();
    formRef = regForm
    const mainStore = MainStore()
    return {regForm, mainStore};
  }
}
</script>
<style lang="less" scoped>
.login-container {
  background-color: #cecece;
  min-height: 100vh;
  background-image: url(../assets/ui/platform/login/bg.jpg);
  background-size: cover;
  background-position: 50% 50%;
}

.title-box {
  text-align: center;
  padding-top: 55px;
  margin-bottom: 50px;

  .title_img {
    width: 214px;
    height: 116px;
  }
}

.login-form {
  width: 310px;
  margin: 0 auto;

  .login-type {
    background-image: url(../assets/ui/platform/login/loginTypeBg.png);
    background-size: cover;
    width: 315px;
    height: 41px;
    position: relative;

    > div {
      position: absolute;
      top: 2px;

      width: 160px;
      height: 37px;
      line-height: 37px;
      font-size: 15px;
      text-align: center;
      border-radius: 20px;
      color: #fff;
    }

    .left {
      left: 3px;
    }

    .right {
      right: 3px;
    }

    .active {
      background-color: #fae2d6;
      color: #333;
    }
  }

  .input-box {
    margin-top: 34px;

    input {
      border: 1px solid #fff;
      width: 315px;
      padding: 10px 20px;
      margin-bottom: 23px;
      border-radius: 20px;
      color: #fff;
    }

    input::-webkit-input-placeholder {
      color: #dde3e4;
    }
  }

  .btn-box {
    text-align: center;

    .login-btn {
      background-image: url(../assets/ui/platform/login/loginBtnBg.png);
      //background-size: cover;
      border-radius: 20px;
      background-position: 50% 50%;
      width: 315px;
      height: 41px;
      color: #fff;
      text-align: center;
      font-size: 15px;
      line-height: 41px;
    }

    .forget-btn {
      display: inline-block;
      //background-size: cover;
      background-position: 50% 50%;
      width: 83px;
      height: 14px;
      margin: 0 auto;
      margin-top: 15px;
    }
  }
}
</style>
