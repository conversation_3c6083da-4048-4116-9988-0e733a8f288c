<template>
  <div class="login-container">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" autocomplete="off"
             label-position="left"
    >

      <div class="title-container">
        <h3 class="title-img">
<!--          <img src="../../../public/logo.png" alt="">-->
        </h3>
        <h3 class="title">
          江西上饶阳光青少年成长中心
        </h3>
        <h4 class="sub-title">
          AR红色数字剧坊管理后台
        </h4>
      </div>

      <el-form-item prop="account">
        <span class="svg-container">
          <svg-icon icon-class="user"/>
        </span>
        <el-input
          ref="account"
          v-model="loginForm.account"
          :placeholder="$t('login.account')"
          name="account"
          type="text"
          tabindex="1"
          autocomplete="off"
        />
      </el-form-item>

      <el-tooltip v-model="capsTooltip" content="Caps lock is On" placement="right" manual>
        <el-form-item prop="password">
          <span class="svg-container">
            <svg-icon icon-class="password"/>
          </span>
          <el-input
            :key="passwordType"
            ref="password"
            v-model="loginForm.password"
            :type="passwordType"
            placeholder="密码"
            name="password"
            tabindex="2"
            autocomplete="off"
            @keyup.native="checkCapslock"
            @blur="capsTooltip = false"
            @keyup.enter.native="handleLogin"
          />
          <span class="show-pwd" @click="showPwd">
            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'"/>
          </span>
        </el-form-item>
      </el-tooltip>

      <div>
        <el-button round :loading="loading" type="primary"
                   style="display:block;width:35%;margin:0 auto;margin-bottom:30px;font-size: 17px"
                   @click.native.prevent="handleLogin"
        >
          {{ $t('login.logIn') }}
        </el-button>
      </div>
    </el-form>
    <!--    <ul class="cb-slideshow">-->
    <!--      <li></li>-->
    <!--      <li></li>-->
    <!--      <li></li>-->
    <!--      <li></li>-->
    <!--      <li></li>-->
    <!--      <li></li>-->
    <!--    </ul>-->

    <!--修改密码弹窗-->
    <el-dialog
      title="请修改默认密码"
      :visible.sync="password.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      :show-close="false"
      width="600px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="140px" ref="passwordForm" :model="password.edit" :rules="password.formRules">
          <el-form-item label="请输入新密码:" prop="password1">
            <el-input v-model.trim="password.edit.password1" type="password">
            </el-input>
          </el-form-item>
          <el-form-item label="请再次输入新密码:" prop="password2">
            <el-input v-model="password.edit.password2" type="password"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="success" @click="clickChangePasswordBtn()">提 交</el-button>
      </span>
    </el-dialog>

    <video class="video-bg" src="../../../public/bgvideo.mp4" autoplay="autoplay" loop muted></video>

    <div class="bottomTips">
      <div>成都智云鸿道信息技术有限公司提供技术支持</div>
      <div>推荐使用谷歌、火狐浏览器访问本系统</div>
      <div>蜀ICP备15014907号-2</div>
    </div>
  </div>
</template>

<script>
import {validUsername} from '@/utils/validate'
import {AdminUserModel} from '@/model/AdminUserModel'
import {getToken, setToken} from '@/utils/auth'
import {msg_err, msg_success} from "@/utils/ele_component";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";

export default {
  name: 'Login',
  components: {},
  directives: {
    elDragDialog, permission
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      callback()
    }
    const validatePassword = (rule, value, callback) => {
      callback()
    }
    return {
      directToken: this.$route.query['directToken'],
      loginForm: {
        account: '',
        password: ''
      },
      loginRules: {
        account: [{required: true, trigger: 'blur', message: '请输入账号'}],
        password: [{required: true, trigger: 'blur', message: '请输入密码'}]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
      password: {
        dialog: false,
        userInfo: {},
        edit: {
          password1: "",
          password2: ""
        },
        // 输入检测
        formRules: {
          'password1': {required: true, pattern: /^\w{6,}$/, message: '密码至少6位'},
          'password2': {required: true, pattern: /^\w{6,}$/, message: '密码至少6位'},
        },
      }
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {
    // window.addEventListener('storage', this.afterQRScan)
  },
  async mounted() {
    // 检测地址栏是否有token,有就直接登录
    if (this.directToken) {
      this.loginDirectByToken(this.directToken)
    }
    if (this.loginForm.account === '') {
      this.$refs.account.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    }
  },
  destroyed() {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    // 通过token直接登录系统
    loginDirectByToken(token) {
      this.$store.dispatch('user/login', {
        token: token
      }).then(() => {
        this.$router.push({path: '/'})
      })
    },
    checkCapslock(e) {
      const {key} = e
      this.capsTooltip = key && key.length === 1 && (key >= 'A' && key <= 'Z')
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(async valid => {
        if (valid) {
          let result = await AdminUserModel.userLogin(this.loginForm.account, this.loginForm.password)
          if (result) {
            this.$store.dispatch('user/login', result).then(() => {
              this.$router.push({path: '/'})
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
    // 点击修改密码弹窗的提交按钮
    clickChangePasswordBtn() {
      this.$refs['passwordForm'].validate(async validate => {
        if (validate) {
          if (this.password.edit.password1 !== this.password.edit.password2) {
            msg_err("两次密码输入不一样！")
            return
          }
          let result = await AdminUserModel.changePassword(this.password.edit.password1, this.password.userInfo.user.adminUserId)
          if (result.code === "000000") {
            msg_success("修改密码成功")
            // 设置extraInfo已登录
            let user = result.data
            user.extraInfo.hasLogin = true
            await AdminUserModel.edit(user)
            this.password.dialog = false
            this.$router.push({path: '/'})
          }
        }
      });
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #f9f9f9;
$light_gray: #fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: $bg;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 10px 5px 10px 13px;
      color: #333;
      height: 47px;


      &:-webkit-autofill {
        box-shadow: inset 0px 60px 0px $bg;;
        -webkit-text-fill-color: #333 !important;
        color: #222;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: $bg;
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style lang="scss" scoped>
.video-bg {
  position: fixed;
  top: 0;
  right: 0;
  min-width: 100%;
  min-height: 100%;
  height: 0px;
  z-index: -999;
  object-fit: fill;
}

.bottomTips {
  position: fixed;
  right: 20px;
  bottom: 20px;
  color: #fff;
  font-size: 13px;
  text-align: right;
}

$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #fff;

.login-container {
  min-height: 100%;
  width: 100%;

  overflow: hidden;

  .login-form {
    position: relative;
    width: 520px;
    max-width: 100%;
    padding: 160px 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title-img {
      text-align: center;

      img {

      }
    }

    .title {
      font-size: 30px;
      color: $light_gray;
      margin: 0px auto 20px auto;
      text-align: center;
      font-weight: bold;
    }

    .sub-title {
      font-size: 26px;
      color: $light_gray;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
    }

    .set-language {
      color: #fff;
      position: absolute;
      top: 3px;
      font-size: 18px;
      right: 0px;
      cursor: pointer;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }
}

.cb-slideshow {
  margin: 0px;
}

.cb-slideshow,
.cb-slideshow:after {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
  z-index: -1;
}

.cb-slideshow li {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0px;
  left: 0px;
  color: transparent;
  background-size: cover;
  background-position: 50% 50%;
  background-repeat: none;
  opacity: 0;
  z-index: 0;
  -webkit-backface-visibility: hidden;
  -webkit-animation: imageAnimation 72s linear infinite 0s;
  -moz-animation: imageAnimation 72s linear infinite 0s;
  -o-animation: imageAnimation 72s linear infinite 0s;
  -ms-animation: imageAnimation 72s linear infinite 0s;
  animation: imageAnimation 72s linear infinite 0s;
}

.cb-slideshow li:nth-child(1) {
  background-image: url("../../../public/image/background1.jpg")
}

.cb-slideshow li:nth-child(2) {
  background-image: url("../../../public/image/background2.jpg");
  -webkit-animation-delay: 12s;
  -moz-animation-delay: 12s;
  -o-animation-delay: 12s;
  -ms-animation-delay: 12s;
  animation-delay: 12s;
}

.cb-slideshow li:nth-child(3) {
  background-image: url("../../../public/image/background3.jpg");
  -webkit-animation-delay: 24s;
  -moz-animation-delay: 24s;
  -o-animation-delay: 24s;
  -ms-animation-delay: 24s;
  animation-delay: 24s;
}

.cb-slideshow li:nth-child(4) {
  background-image: url("../../../public/image/background1.jpg");
  animation-delay: 36s;
}

.cb-slideshow li:nth-child(5) {
  background-image: url("../../../public/image/background2.jpg");
  animation-delay: 48s;
}

.cb-slideshow li:nth-child(6) {
  background-image: url("../../../public/image/background3.jpg");
  animation-delay: 60s;
}

@keyframes imageAnimation {
  0% {
    opacity: 0.7;
    animation-timing-function: ease-in;
  }

  8% {
    opacity: 1;
    transform: scale(1.15);
    animation-timing-function: ease-out;
    -webkit-transform: scale(1.15);
    -moz-transform: scale(1.15);
    -ms-transform: scale(1.15);
    -o-transform: scale(1.15);
  }

  17% {
    opacity: 1;
    transform: scale(1.20);
  }

  25% {
    opacity: 0;
    transform: scale(1.40);
  }

  100% {
    opacity: 0
  }
}
</style>
