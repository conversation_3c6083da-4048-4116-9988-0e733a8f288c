<template>
  <div v-show="screenDirection==='landscape'" id="map_html">
    <info-frame></info-frame>
    <div class="map-box" id="map-box">
      <div id="build-s" class="build build-s complete" @click="clickTaskArea('s')">
        <div class="build-name">任务起点</div>
        <div class="dot"></div>
        <img src="@/assets/ui/platform/map/fdldq/旗子.png" alt="" class="flag">
        <img src="@/assets/ui/platform/map/fdldq/第一段.png" alt="" class="line">
        <img src="@/assets/ui/platform/map/fdldq/第一段高亮.png" alt="" class="line-on">
      </div>
      <div id="build-a" :class="'build build-a '+taskAreaInfo.aAreaStatus" @click="clickTaskArea('a')">
        <div class="dot"></div>
        <div class="build-name">A-任务区</div>
        <div class="status done">{{ taskAreaInfo.statusText[taskAreaInfo.aAreaStatus] }}</div>
        <img src="@/assets/ui/platform/map/fdldq/旗子.png" alt="" class="flag">
        <img src="@/assets/ui/platform/map/fdldq/第二段.png" alt="" class="line">
        <img src="@/assets/ui/platform/map/fdldq/第二段高亮.png" alt="" class="line-on">
      </div>
      <div id="build-b" :class="'build build-b '+taskAreaInfo.bAreaStatus" @click="clickTaskArea('b')">
        <div class="dot"></div>
        <div class="build-name">B-任务区</div>
        <div class="status done">{{ taskAreaInfo.statusText[taskAreaInfo.bAreaStatus] }}</div>
        <img src="@/assets/ui/platform/map/fdldq/旗子.png" alt="" class="flag">
        <img src="@/assets/ui/platform/map/fdldq/第三段.png" alt="" class="line">
        <img src="@/assets/ui/platform/map/fdldq/第三段高亮.png" alt="" class="line-on">
      </div>
      <div id="build-c" :class="'build build-c '+taskAreaInfo.cAreaStatus" @click="clickTaskArea('c')">
        <div class="dot"></div>
        <div class="build-name">C-任务区</div>
        <div class="status doing">{{ taskAreaInfo.statusText[taskAreaInfo.cAreaStatus] }}</div>
        <img src="@/assets/ui/platform/map/fdldq/旗子.png" alt="" class="flag">
        <img src="@/assets/ui/platform/map/fdldq/第四段.png" alt="" class="line">
        <img src="@/assets/ui/platform/map/fdldq/第四段高亮.png" alt="" class="line-on">
      </div>
      <div id="build-d" :class="'build build-d '+taskAreaInfo.dAreaStatus" @click="clickTaskArea('d')">
        <div class="dot"></div>
        <div class="build-name">D-任务区</div>
        <div class="status undo">{{ taskAreaInfo.statusText[taskAreaInfo.dAreaStatus] }}</div>
        <img src="@/assets/ui/platform/map/fdldq/旗子.png" alt="" class="flag">
      </div>
      <!--位置dot-->
      <div class="location-now-dot" id="location-now-dot">
        <img src="@/assets/ui/platform/map/位置点.png" alt="" id="location-pointer">
      </div>
    </div>
    <div class="map-info-container">
      <!--      <div class="li">x:{{ dotX }}</div>-->
      <!--      <div class="li">y:{{ dotY }}</div>-->
      <!--      <div class="li">lon:{{ position.coords.longitude }}</div>-->
      <!--      <div class="li">lat:{{ position.coords.latitude }}</div>-->
      <div class="li">距离目的地{{ this.distanceSubMeter }}米</div>
    </div>
    <div class="map-container">
<!--      <van-popup v-model:show="clueShow" position="right" :style="{ width: '60%',height:'100%' }">-->
<!--        <van-steps direction="vertical" :active="-1" v-show="taskShowIndex==='a'" class="taskStepsBox">-->
<!--          <van-step>-->
<!--            <div class="childTaskName">-->
<!--              A1-长征困局（{{ recordInfo.hasOwnProperty("A1") ? taskStatusEnums[recordInfo["A1"]["status"]] : "" }}）-->
<!--            </div>-->
<!--            <div class="date">2022-09-24 15:00:30</div>-->
<!--          </van-step>-->
<!--          <van-step>-->
<!--            <div class="childTaskName">-->
<!--              A2-行军决策（{{ recordInfo.hasOwnProperty("A2") ? taskStatusEnums[recordInfo["A2"]["status"]] : "" }}）-->
<!--            </div>-->
<!--            <div class="date">2022-09-24 15:35:42</div>-->
<!--          </van-step>-->
<!--        </van-steps>-->
<!--        <van-steps direction="vertical" :active="-1" v-show="taskShowIndex==='b'" class="taskStepsBox">-->
<!--          <van-step>-->
<!--            <div class="childTaskName">-->
<!--              B1-夺取安顺场（{{ recordInfo.hasOwnProperty("B1") ? taskStatusEnums[recordInfo["B1"]["status"]] : "" }}）-->
<!--            </div>-->
<!--            <div class="date">2022-09-24 15:00:30</div>-->
<!--          </van-step>-->
<!--          <van-step>-->
<!--            <div class="childTaskName">-->
<!--              B2-强渡大渡河（{{ recordInfo.hasOwnProperty("B2") ? taskStatusEnums[recordInfo["B2"]["status"]] : "" }}）-->
<!--            </div>-->
<!--            <div class="date">2022-09-24 15:35:42</div>-->
<!--          </van-step>-->
<!--          <van-step>-->
<!--            <div class="childTaskName">-->
<!--              B3-渡河难题（{{ recordInfo.hasOwnProperty("B3") ? taskStatusEnums[recordInfo["B3"]["status"]] : "" }}）-->
<!--            </div>-->
<!--            <div class="date">2022-09-24 15:35:42</div>-->
<!--          </van-step>-->
<!--        </van-steps>-->
<!--        <van-steps direction="vertical" :active="0" v-show="taskShowIndex==='c'" class="taskStepsBox">-->
<!--          <van-step>-->
<!--            <div class="childTaskName">-->
<!--              C1-限时奔袭（{{ recordInfo.hasOwnProperty("C1") ? taskStatusEnums[recordInfo["C1"]["status"]] : "" }}）-->
<!--            </div>-->
<!--            <div class="date">2022-09-24 15:00:30</div>-->
<!--          </van-step>-->
<!--          <van-step>-->
<!--            <div class="childTaskName">-->
<!--              C2-隔河竞走（{{ recordInfo.hasOwnProperty("C2") ? taskStatusEnums[recordInfo["C2"]["status"]] : "" }}）-->
<!--            </div>-->
<!--            <div class="date">2022-09-24 15:35:42</div>-->
<!--          </van-step>-->
<!--          <van-step>-->
<!--            <div class="childTaskName">-->
<!--              C3-奔袭泸定桥（{{ recordInfo.hasOwnProperty("C3") ? taskStatusEnums[recordInfo["C3"]["status"]] : "" }}）-->
<!--            </div>-->
<!--            <div class="date">2022-09-24 15:35:42</div>-->
<!--          </van-step>-->
<!--        </van-steps>-->
<!--        <van-steps direction="vertical" :active="-1" v-show="taskShowIndex==='d'" class="taskStepsBox">-->
<!--          <van-step>-->
<!--            <div class="childTaskName">-->
<!--              D1-排兵布阵（{{ recordInfo.hasOwnProperty("D1") ? taskStatusEnums[recordInfo["D1"]["status"]] : "" }}）-->
<!--            </div>-->
<!--            <div class="date">2022-09-24 15:00:30</div>-->
<!--          </van-step>-->
<!--          <van-step>-->
<!--            <div class="childTaskName">-->
<!--              D2-选择勇士（{{ recordInfo.hasOwnProperty("D2") ? taskStatusEnums[recordInfo["D2"]["status"]] : "" }}）-->
<!--            </div>-->
<!--            <div class="date">2022-09-24 15:35:42</div>-->
<!--          </van-step>-->
<!--          <van-step>-->
<!--            <div class="childTaskName">-->
<!--              D3-选择武器（{{ recordInfo.hasOwnProperty("D3") ? taskStatusEnums[recordInfo["D3"]["status"]] : "" }}）-->
<!--            </div>-->
<!--            <div class="date">2022-09-24 15:35:42</div>-->
<!--          </van-step>-->
<!--          <van-step>-->
<!--            <div class="childTaskName">-->
<!--              D4-即将夺桥（{{ recordInfo.hasOwnProperty("D4") ? taskStatusEnums[recordInfo["D4"]["status"]] : "" }}）-->
<!--            </div>-->
<!--            <div class="date">2022-09-24 15:35:42</div>-->
<!--          </van-step>-->
<!--          <van-step>-->
<!--            <div class="childTaskName">-->
<!--              D5-铁索冲锋（{{ recordInfo.hasOwnProperty("D5") ? taskStatusEnums[recordInfo["D5"]["status"]] : "" }}）-->
<!--            </div>-->
<!--            <div class="date">2022-09-24 15:35:42</div>-->
<!--          </van-step>-->
<!--        </van-steps>-->
<!--      </van-popup>-->
      <div class="exit-btn-landscape full-btn-landscape" @click="clickExitBtn">
        <img src="@/assets/ui/fdldq/common/exit-btn-icon.png" alt="">
        <span>退出</span>
      </div>
      <van-button type="danger" size="small" class="exit-btn export-btn" @click="MapMethods().clickExportMapBtn()"
                  v-if="false">
        导出地图
      </van-button>
    </div>
  </div>


  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
</template>

<script>
import {toast_text_vant, msg_success_vant} from "../utils/nut_component";
import {mapState} from "pinia";
import {TaskStore} from "../store/TaskStore";
import infoFrame from "@/views/components/infoFrame.vue";
import {GpsModel} from "../model/GpsModel";
import enums from "@/enums/index"
import {TaskModel} from "../model/TaskModel";
import $ from "jquery";
import {downloadFile, playSound} from "../utils/common";
import {showDialog} from "vant";
import fdldqTaskConfig from "@/scripts/fdldq/taskConfig";
import html2canvas from "html2canvas"

window.$ = $

export default {
  name: "Map_Html_view",
  components: {infoFrame},
  computed: {
    ...mapState(TaskStore, {
      roleId: store => store.roleId,
      recordStartTime: store => store.startTime,
      recordInfo: store => store.recordInfo,
      nowTaskId: store => store.nowTaskId,
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  data() {
    return {
      taskStatusEnums: enums.taskStatus,
      directionRight: true,
      clueShow: false,
      taskShowIndex: "",
      nowTaskConfig: {}, // 任务配置信息


      // 任务区域状态信息
      taskAreaInfo: {
        aAreaStatus: "undo",
        bAreaStatus: "undo",
        cAreaStatus: "undo",
        dAreaStatus: "undo",
        statusText: {
          "undo": "未开始",
          "doing": "进行中",
          "complete": "已完成"
        }
      },


      // 地图
      // gps watcherId
      watcherId: "",
      // gps配置
      gpsOptions: {
        //是否使用高精度设备，如GPS。默认是true
        enableHighAccuracy: true,
        //超时时间，单位毫秒，默认为0
        timeout: 5000,
        //使用设置时间内的缓存数据，单位毫秒
        //默认为0，即始终请求新数据，其它时间则按时间刷新
        //如设为Infinity，则始终使用缓存数据
        maximumAge: 0
      },
      position: {
        coords: {},
      },
      // 地图X分辨率 px
      mapXPix: 1000,
      // 地图Y分辨率 px
      mapYPix: 1000,
      // 起始点x坐标 px
      mapXStartPx: 450,
      // 起始点y坐标 px
      mapYStartPx: 850,
      // 地图左上角经纬度
      mapTopLeftCoordinate: [],
      // 地图右上角经纬度
      mapTopRightCoordinate: [],
      // 地图右下角经纬度
      mapBottomRightCoordinate: [],
      // 地图左下角经纬度 坐标系起点
      mapBottomLeftCoordinate: [],
      // 地图坐标原点坐标
      mapOriginCoordinate: [],
      // 缩放比例 宽度/经度差
      zoomScale: 770000,
      // 当前位置
      positionNow: {},
      // 当前经度
      lonNow: 0,
      // 当前纬度
      latNow: 0,
      // 当前位置点标记element
      dot: undefined,
      gpsInit: "",
      dotX: "",
      dotY: "",
      dotTimeId: "",
      // 任务坐标要求坐标
      taskCoordinate: [],
      // 距离判断误差 米
      distanceError: 5,
      // 到达计数
      arrivedTime: 0,
      // 是否已到达过
      arrived: false,
      // 是否需要判断位置是否达到
      needArrive: false,
      // 和任务相差的距离
      distanceSubMeter: 0,
    }
  },
  mounted() {
    let $this = this;

    // 设置地图信息
    document.getElementById("map-box").style.width = this.mapXPix + "px"
    document.getElementById("map-box").style.height = this.mapYPix + "px"
    this.dot = document.getElementById("location-now-dot")


    // 获取任务配置信息
    let nowTaskConfig = TaskModel.getOneTaskConfig(this.nowTaskId)
    this.nowTaskConfig = nowTaskConfig;
    let nowTask = this.recordInfo[this.nowTaskId]
    nowTask["name"] = nowTaskConfig["name"]
    this.nowTask = nowTask

    // 地图模式重置本任务定位
    fdldqTaskConfig.configObject.mapType === 'gps' && this.nowTaskConfig["locationNeeded"] === true ?
        this.nowTaskConfig["locationNeeded"] = true : this.nowTaskConfig["locationNeeded"] = false


    // todo 示意图模式-显示地图
    this.MapMethods().drawInitMap()

    // 任务状态判断
    if (nowTask.status === "open") { // 任务开启
      if (this.nowTaskConfig["locationNeeded"] === true) {// 如果需要判断定位
        this.watchGps();
      }
    }

    // 任务区域状态判断
    if (["A1", "A2"].indexOf(this.nowTaskId) > -1) {// 正在进行A任务
      this.taskAreaInfo.aAreaStatus = "doing"
      $("#map-box .build-s .line-on").show()
      $("#map-box .build-s .line").hide()
    }
    if (["B1", "B2", "B3"].indexOf(this.nowTaskId) > -1) {// 正在进行B任务
      this.taskAreaInfo.aAreaStatus = "complete"
      this.taskAreaInfo.bAreaStatus = "doing"
      $("#map-box .build-a .line-on").show()
      $("#map-box .build-a .line").hide()
    }
    if (["C1", "C2", "C3"].indexOf(this.nowTaskId) > -1) {// 正在进行C任务
      this.taskAreaInfo.aAreaStatus = "complete"
      this.taskAreaInfo.bAreaStatus = "complete"
      this.taskAreaInfo.cAreaStatus = "doing"
      $("#map-box .build-b .line-on").show()
      $("#map-box .build-b .line").hide()
    }
    if (["D1", "D2", "D3", "D4", "D5"].indexOf(this.nowTaskId) > -1) {// 正在进行D任务
      this.taskAreaInfo.aAreaStatus = "complete"
      this.taskAreaInfo.bAreaStatus = "complete"
      this.taskAreaInfo.cAreaStatus = "complete"
      this.taskAreaInfo.dAreaStatus = "doing"
      $("#map-box .build-c .line-on").show()
      $("#map-box .build-c .line").hide()
    }


  },
  beforeUnmount() {
    clearInterval(this.dotTimeId)
  },
  methods: {
    // 提示框
    msg_alert_vant1(title, des) {
      return new Promise(function (resolve, reject) {
        showDialog({
          title: title,
          message: des,
          theme: 'round-button',
          lockScroll: false,
        })
            .then(() => {
              resolve(true)
            })
            .catch(() => {
              resolve(false)
            });
      });
    },
    // 监视gps位置变化
    watchGps() {
      this.watcherId = navigator.geolocation.watchPosition((position) => {
        this.position = position
        if (!this.gpsModelgpsInit) {// 初始化
          this.dot = document.getElementById("location-now-dot")
          this.taskCoordinate[0] = this.nowTaskConfig["locationCoordinate"][0]
          this.taskCoordinate[1] = this.nowTaskConfig["locationCoordinate"][1]
          this.distanceError = this.nowTaskConfig["locationAllowableError"]
          this.needArrive = this.nowTaskConfig["locationNeeded"]
          this.gpsModelgpsInit = true
        } else {// 位置变动
          this.onChange(position)
        }
      }, (error) => {
        switch (error.code) {
          case error.TIMEOUT:
            //alert('超时');
            break;
          case error.PERMISSION_DENIED:
            alert('用户拒绝提供地理位置');
            break;
          case error.POSITION_UNAVAILABLE:
            alert('地理位置不可用');
            break;
          default:
            break;
        }
      }, this.gpsOptions);
    },
    // 当gps位置改变
    onChange(position) {
      let lonNow = position.coords.longitude
      let latNow = position.coords.latitude
      this.lonNow = lonNow
      this.latNow = latNow
      // 定位点显示修改
      this.MapMethods().dotWatch(position)
      // 判断是否达到任务区域
      if (this.needArrive && !this.arrived) { // 需要定位，且未达到定位 todo 未确认离开了
        this.judgeArriveTaskLocation()
      }
    },

    // 当gps位置改变-矩形四个点
    onChangeRectangle(position) {
      let lonNow = this.lonNow
      let latNow = this.latNow
      let dot = this.dot
      // 判断是否在限制经纬度内
      if (this.mapBottomLeftCoordinate[0] > lonNow || this.mapBottomRightCoordinate[0] < lonNow) {// 经度没在范围内
        alert("超出地图范围-经度")
      }
      if (this.mapBottomLeftCoordinate[1] > latNow || this.mapTopLeftCoordinate[1] < latNow) { // 纬度没在范围内
        alert("超出地图范围-纬度")
      }
      // x轴坐标偏移
      let xC = lonNow - this.mapBottomLeftCoordinate[0]
      xC = xC * this.zoomScale
      dot.style.left = xC + this.mapXStartPx + "px";
      // y轴坐标偏移
      let yC = latNow - this.mapBottomLeftCoordinate[1]
      yC = yC * this.zoomScale
      dot.style.bottom = yC + this.mapYStartPx + "px"

    },
    // 判断是否达到某一任务区域
    async judgeArriveTaskLocation() {
      // 任务坐标
      let taskLon = this.taskCoordinate[0]
      let taskLat = this.taskCoordinate[1]
      // 当前坐标
      let lonNow = this.lonNow
      let latNow = this.latNow
      // 误差判断-方法1-计算两点之间距离，判断是否在误差范围内
      let distance = GpsModel.wgs84PointsDistance(lonNow, latNow, taskLon, taskLat)
      if (distance < this.distanceError) {
        if (this.arrivedTime < 3) {// todo 同一时间内3次达到表示真的达到了，避免误差
          this.arrivedTime++
        }
      } else {
        this.arrivedTime = 0
        this.distanceSubMeter = Math.round(distance)
        $("div.van-overlay").hide()
        $(`div[aria-labelledby='到达任务区域']`).hide()
      }
      if (this.arrivedTime >= 3) {
        if (!this.arrived) {
          // todo 自写弹窗界面控制显示和隐藏，避免和其他弹窗冲突
          $("div.van-overlay").show()
          $(`div[aria-labelledby='到达任务区域']`).show()
          playSound(enums.audioList[0]) // 播放成功提示音
          this.arrived = true
          if (await this.msg_alert_vant1("到达任务区域", "您已达到任务区域，点击确定按钮继续任务！")) {
            if (await TaskModel.arriveOneTaskGpsAddress(this.nowTaskId)) {// 标记到达地点
              this.$router.push({
                name: "AR_Main_View",
                query: {}
              })
            }

          }
        }
      }
      return distance <= this.distanceError;
    },
    // 点击设为起点按钮
    clickSetStartBtn() {
      this.setStartPoint()
    },
    clickExitBtn() {
      this.$router.push({
        name: "AR_Main_View"
      })
    },
    clickTaskTipBtn() {
      toast_text_vant("请前往A任务区进行任务")
    },
    clickTaskArea(taskName) {
      this.clueShow = false
      this.taskShowIndex = taskName
    },
    // 地图相关方法集
    MapMethods() {
      let $this = this;
      return {
        /**
         * 绘制初始化地图-自动化地图
         * 以S1为坐标圆点 一个平面坐标系。地图1000px*1000px。坐标原点位于中间500，500处
         */
        drawInitMap() {
          // 获取任务区域位置和定位点元素
          let taskSEle = document.getElementById("build-s")
          let taskAEle = document.getElementById("build-a")
          let taskBEle = document.getElementById("build-b")
          let taskCEle = document.getElementById("build-c")
          let taskDEle = document.getElementById("build-d")
          let dotEle = document.getElementById("location-now-dot")
          let pointerEle = document.getElementById("location-pointer")

          // todo 示意图模式地图显示
          if ($this.nowTaskConfig["locationNeeded"] === false) {
            dotEle.style.display = "none";
            $(".map-info-container").hide()
          }

          // css设置
          dotEle.style.width = "40px"
          dotEle.style.height = "40px"
          pointerEle.style.width = "28px";
          pointerEle.style.height = "40px";
          pointerEle.style.left = "6px";
          pointerEle.style.bottom = "2.5px";

          taskSEle.style.width = "100px"
          taskSEle.style.height = "100px"
          taskSEle.style.borderRadius = "50px"
          taskAEle.style.width = "100px"
          taskAEle.style.height = "100px"
          taskAEle.style.borderRadius = "50px"
          taskBEle.style.width = "100px"
          taskBEle.style.height = "100px"
          taskBEle.style.borderRadius = "50px"
          taskCEle.style.width = "100px"
          taskCEle.style.height = "100px"
          taskCEle.style.borderRadius = "50px"
          taskDEle.style.width = "100px"
          taskDEle.style.height = "100px"
          taskDEle.style.borderRadius = "50px"
          $("#map-box .build .dot").css("width", "16px")
          $("#map-box .build .dot").css("height", "16px")
          $("#map-box .build .dot").css("left", "42px")
          $("#map-box .build .dot").css("bottom", "42px")
          $("#map-box .build .dot").css("border-radius", "8px")
          $("#map-box .build .build-name").css("left", "0px")
          $("#map-box .build .build-name").css("bottom", "-35px")

          // 获取a-d任务和剧本起点坐标
          let s1Config = TaskModel.getOneTaskConfig("S1")
          $this.mapOriginCoordinate = [s1Config["locationCoordinate"][0], s1Config["locationCoordinate"][1]]
          let s1Coordinate = $this.mapOriginCoordinate
          let a1Config = TaskModel.getOneTaskConfig("A1")
          let a1Coordinate = [a1Config["locationCoordinate"][0], a1Config["locationCoordinate"][1]]
          let b1Config = TaskModel.getOneTaskConfig("B1")
          let b1Coordinate = [b1Config["locationCoordinate"][0], b1Config["locationCoordinate"][1]]
          let c1Config = TaskModel.getOneTaskConfig("C1")
          let c1Coordinate = [c1Config["locationCoordinate"][0], c1Config["locationCoordinate"][1]]
          let d1Config = TaskModel.getOneTaskConfig("D1")
          let d1Coordinate = [d1Config["locationCoordinate"][0], d1Config["locationCoordinate"][1]]
          // 放置任务区域-起点
          taskSEle.style.left = (0 + $this.mapXStartPx).toFixed(2) + "px";
          taskSEle.style.bottom = (0 + $this.mapYStartPx).toFixed(2) + "px"
          // 放置定位点
          dotEle.style.left = $this.mapXStartPx + 30 + "px"
          dotEle.style.bottom = $this.mapYStartPx + 30 + "px"
          // dotEle.style.display="none"
          // 放置任务区域-A
          let taskA_x = (a1Coordinate[0] - s1Coordinate[0]).toFixed(8)
          let taskA_y = (a1Coordinate[1] - s1Coordinate[1]).toFixed(8)
          console.log("taskA距离S1", GpsModel.wgs84PointsDistance(a1Coordinate[0], a1Coordinate[1], s1Coordinate[0], s1Coordinate[1]))
          console.log("taskA", taskA_x, taskA_y)
          console.log("taskA", taskA_x * $this.zoomScale / 1 + $this.mapXStartPx, taskA_y * $this.zoomScale / 1 + $this.mapYStartPx)
          console.log("")
          taskAEle.style.left = (taskA_x * $this.zoomScale / 1 + $this.mapXStartPx).toFixed(2) + "px";
          taskAEle.style.bottom = (taskA_y * $this.zoomScale / 1 + $this.mapYStartPx).toFixed(2) + "px"
          // 放置任务区域-B
          let taskB_x = (b1Coordinate[0] - s1Coordinate[0]).toFixed(8)
          let taskB_y = (b1Coordinate[1] - s1Coordinate[1]).toFixed(8)
          console.log("taskB距离S1", GpsModel.wgs84PointsDistance(b1Coordinate[0], b1Coordinate[1], s1Coordinate[0], s1Coordinate[1]))
          console.log("taskB", taskB_x, taskB_y)
          console.log("taskB", taskB_x * $this.zoomScale / 1 + $this.mapXStartPx, taskB_y * $this.zoomScale / 1 + $this.mapYStartPx)
          console.log("")
          taskBEle.style.left = (taskB_x * $this.zoomScale / 1 + $this.mapXStartPx).toFixed(2) + "px";
          taskBEle.style.bottom = (taskB_y * $this.zoomScale / 1 + $this.mapYStartPx).toFixed(2) + "px"
          // 放置任务区域-C
          let taskC_x = (c1Coordinate[0] - s1Coordinate[0]).toFixed(8)
          let taskC_y = (c1Coordinate[1] - s1Coordinate[1]).toFixed(8)
          console.log("taskC距离S1", GpsModel.wgs84PointsDistance(c1Coordinate[0], c1Coordinate[1], s1Coordinate[0], s1Coordinate[1]))
          console.log("taskC", taskC_x, taskC_y)
          console.log("taskC", taskC_x * $this.zoomScale / 1 + $this.mapXStartPx, taskC_y * $this.zoomScale / 1 + $this.mapYStartPx)
          console.log("")
          taskCEle.style.left = (taskC_x * $this.zoomScale / 1 + $this.mapXStartPx).toFixed(2) + "px";
          taskCEle.style.bottom = (taskC_y * $this.zoomScale / 1 + $this.mapYStartPx).toFixed(2) + "px"
          // 放置任务区域-D
          let taskD_x = (d1Coordinate[0] - s1Coordinate[0]).toFixed(8)
          let taskD_y = (d1Coordinate[1] - s1Coordinate[1]).toFixed(8)
          console.log("taskD距离S1", GpsModel.wgs84PointsDistance(d1Coordinate[0], d1Coordinate[1], s1Coordinate[0], s1Coordinate[1]))
          console.log("taskD", taskD_x, taskD_y)
          console.log("taskD", taskD_x * $this.zoomScale / 1 + $this.mapXStartPx, taskD_y * $this.zoomScale / 1 + $this.mapYStartPx)
          console.log("")
          taskDEle.style.left = (taskD_x * $this.zoomScale / 1 + $this.mapXStartPx).toFixed(2) + "px";
          taskDEle.style.bottom = (taskD_y * $this.zoomScale / 1 + $this.mapYStartPx).toFixed(2) + "px"

          // todo 如何乘，如何转换
          console.log("b1-c1距离", GpsModel.wgs84PointsDistance(b1Coordinate[0], b1Coordinate[1], c1Coordinate[0], c1Coordinate[1]))
        },
        /**
         * 定位移动
         */
        // 当gps位置改变-设置了起点
        dotWatch(position) {
          let lonNow = $this.lonNow
          let latNow = $this.latNow
          let dot = $this.dot
          // x轴坐标偏移
          let xC = lonNow - $this.mapOriginCoordinate[0]
          xC = xC * $this.zoomScale
          // y轴坐标偏移
          let yC = latNow - $this.mapOriginCoordinate[1]
          yC = yC * $this.zoomScale
          // 定位点是否超出-超出就不显示，避免显示问题
          if (Math.abs(xC) > $this.mapXPix || Math.abs(yC) > $this.mapYPix) {
            $this.dot.style.display = "none"
          } else {
            $this.dot.style.display = "block"
          }
          // 设置定位点显示位置
          dot.style.bottom = yC + $this.mapYStartPx + "px"
          dot.style.left = xC + $this.mapXStartPx + "px";
          let pointerEle = document.getElementById("location-pointer")
          let rotateNumber = position.coords.heading

          pointerEle.style.transform = "rotate(" + rotateNumber + "deg)"
          // 朝向指针旋转

          $this.dotX = (xC + $this.mapXStartPx).toFixed(4) - $this.mapXStartPx;
          $this.dotY = (yC + $this.mapYStartPx).toFixed(4) - $this.mapYStartPx;
        },

        // 点击导出地图按钮
        clickExportMapBtn() {
          let element = document.getElementById("map-box");
          console.log(element)
          html2canvas(element, {
            allowTaint: true,
            useCORS: true,
            tainttest: true, // 检测每张图片都已经加载完成
            logging: true,
            scale: 1,
            backgroundColor: `#666`, // 转换图片可能会有白色底色，你可根据你的页面或者PM要求设置一下背景色，不要的话就null
          }).then((canvas) => {
            //  转成图片，生成图片地址
            let imgUrl = canvas.toDataURL("image/png");
            // console.warn(imgUrl);
            downloadFile(imgUrl, "地图")
          });
        },
      }
    },
  }
}
</script>
<style scoped lang="less">
.exit-btn-landscape {
  position: fixed;
  left: 3px;
  top: 2px;
  z-index: 4;
}

.export-btn {
  position: fixed;
  left: 120px;
  top: 0px;
}


#info-frame {
  position: fixed;
  left: 0px;
  top: 0px;
}

.map-box {
  background-image: url("../assets/ui/platform/map/fdldq/底图.png");
  background-size: contain;
  position: absolute;
  top: 0px;
  left: 0px;


  button.btn-start {
    position: absolute;
    z-index: 9999;
    right: 0px;
    bottom: 0px;
  }


  .build {
    //width: 40px;
    //height: 40px;
    position: absolute;
    background-color: #9db0d0;
    // todo 是否隐藏示意图
    //visibility: hidden;

    .dot {
      background-color: #315ea9;
      position: absolute;
    }

    &.build-s {
      //background-color: gray;
      .line, .line-on {
        width: 216/2.3px;
        right: 5.71vw;
        height: 226/2.3px;
        top: 5.536vw;
      }
    }

    &.build-a {
      .line, .line-on {
        width: 83/2.3px;
        right: 5.828vw;
        height: 325/2.3px;
        top: 6.556vw;
      }
    }

    &.build-b {
      .line, .line-on {
        width: 249/2.3px;
        right: -23vw;
        height: 119/2.3px;
        top: 6vw;
      }
    }

    &.build-c {
      .line, .line-on {
        width: 446/2.3px;
        right: -46vw;
        height: 369/2.3px;
        top: -37vw;
      }
    }

    &.complete {
      .build-name {
        background-image: url("../assets/ui/platform/map/fdldq/标签_正常.png");
      }

      .flag {
        display: inline-block;
      }
    }

    &.doing {
      .build-name {
        background-image: url("../assets/ui/platform/map/fdldq/标签_高亮.png");
      }

      .flag {
        display: none;
      }
    }

    &.undo {
      .build-name {
        background-image: url("../assets/ui/platform/map/fdldq/标签_置灰.png");
      }

      .flag {
        display: none;
      }
    }

    .line, .line-on {
      position: absolute;
      z-index: 1;
      display: none;
    }

    .line {
      display: inline-block;
    }

    .flag {
      width: 47/2.2px;
      height: 82/2.2px;
      position: absolute;
      left: 19px;
      top: -14px;
    }

    .build-name {
      position: absolute;
      color: #fff;
      font-weight: bold;
      text-align: center;
      background-image: url("../assets/ui/platform/map/fdldq/标签_正常.png");
      background-size: cover;
      width: 175/4px;
      height: 52/4px;
      line-height: 52/4px;
      z-index: 2;
    }

    .status {
      display: none;
      color: #fff;
      font-size: 4px;
      height: 6px;
      padding: 2px;
      position: absolute;
      top: 0px;
      right: 0px;
    }

  }

  .location-now-dot {
    position: absolute;
    color: #4187f2;
    z-index: 3;
    //background-color: #fff;
    //border-radius: 25px;
    //animation: blink 1s 0.3s linear both infinite;
    //-webkit-animation: blink 1s 0.3s linear both infinite;
    //
    //@keyframes blink {
    //  0% {
    //    background-color: #fff;
    //  }
    //  50% {
    //    background-color: red;
    //  }
    //  100% {
    //    background-color: #fff;
    //  }
    //}


    #location-pointer {
      position: absolute;
    }
  }
}

.map-info-container {
  position: fixed;
  width: 60px;
  bottom: 2px;
  right: 0px;
  background: #f3d8b3;
  z-index: 9999;
  padding: 6px;
  color: #b05f22;

  .li {
    font-size: 5px;
    margin-top: 1px;
  }
}

.taskStepsBox {
  .childTaskName {
    font-size: 7px;
  }

  .taskDes {
    font-size: 7px;
    color: #888;
    margin-top: 3px;
    margin-bottom: 2px;
  }

  .date {
    font-size: 6px;
    color: #888;
  }


}

</style>
