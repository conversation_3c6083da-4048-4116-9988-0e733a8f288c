<template>
  <div>
    <span style="width: 200px"><el-input
      v-model.number="a"
      placeholder="请输入本题得分分值"
      type="number"
      :min="0"
      :max="section['scorePerQuestion']"
    /></span>
    <div>
      <el-input-number v-model="a" :min="1" :max="10" label="描述文字" />
    </div>
  </div>
</template>

<script>
import { API_URL_ERP } from '@/config/main'

export default {
  name: 'TestIndex',
  data() {
    return {
      a: 1,
      section: {
        scorePerQuestion: 6
      }
    }
  },
  mounted() {

  }
}
</script>

<style scoped>

</style>
