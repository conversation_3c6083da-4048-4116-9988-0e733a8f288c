<template>
  <div class="app-container">
    <div class="edit-container">
      <el-form label-width="140px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
        <el-form-item label="剧本名称:" prop="name">
          <el-input v-model.trim="entityInfo.edit.name">
            <span slot="suffix" v-if="entityInfo.edit.name">
              <span class="el-input__count">
                <span class="el-input__count-inner">
                  {{ entityInfo.edit.name.length }} / 40
                </span>
              </span>
            </span>
          </el-input>
        </el-form-item>
<!--        <el-form-item label="是否开放:" prop="opened">-->
<!--          <el-select v-model="entityInfo.edit.opened"-->
<!--                     style="width: 100%">-->
<!--            <el-option :value="true" label="开放" key="开放"></el-option>-->
<!--            <el-option :value="false" label="关闭" key="关闭"></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
        <el-form-item label="剧本头图:" prop="avatarUrl">
          <erp-uploader-one-pic style="float:left;" :img-in="entityInfo.edit.avatarUrl"
                                uploader-id="avatarUrl"
                                uploader-title="" :uploader-size="[230,140]" :pixel-limit="[230,140]"
                                :size-limit="1024"
                                @uploadSuccess="data=>fileUpload(data,entityInfo.edit,'')"
                                @afterDelete="data=>fileDelete(data,entityInfo.edit,'')"></erp-uploader-one-pic>
        </el-form-item>
        <el-form-item label="所属系列:" prop="scriptSeriesId">
          <el-select v-model="entityInfo.edit.scriptSeriesId"
                     style="width: 100%">
            <el-option v-for="item in entityInfo.filter.scriptSeries" :value="item.value" :label="item.label"
                       :key="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="剧本标签:" prop="scriptSeriesId">
          <el-tag
            :key="tag"
            v-for="(tag,tagIndex) in entityInfo.edit.tags"
            closable
            :disable-transitions="false"
            @close="EntityInfoMethods().clickCloseTagBtn(tag,tagIndex)">
            {{ tag }}
          </el-tag>
          <el-input
            class="input-new-tag"
            v-show="entityInfo.tagInputShow"
            v-model="entityInfo.tagInputValue"
            ref="tagInput"
            size="small"
            @keyup.enter.native="EntityInfoMethods().addNewTag()"
            @blur="EntityInfoMethods().addNewTag()"
          >
          </el-input>
          <el-button v-show="!entityInfo.tagInputShow" class="button-new-tag" size="small"
                     @click="EntityInfoMethods().showTagInput()">+ 新标签
          </el-button>
        </el-form-item>
        <el-form-item label="支持地点" prop="addressText">
          <el-input v-model.trim="entityInfo.edit.addressText">
            <span slot="suffix" v-if="entityInfo.edit.addressText">
              <span class="el-input__count">
                <span class="el-input__count-inner">
                  {{ entityInfo.edit.addressText.length }} / 30
                </span>
              </span>
            </span>
          </el-input>
        </el-form-item>
        <el-form-item label="剧本简介" prop="shortDesText">
          <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 5}"
                    v-model="entityInfo.edit.shortDesText"></el-input>
        </el-form-item>
        <el-form-item label="剧本介绍:" prop="desText">
          <tinymce
            ref="tinymce_desText"
            v-model="entityInfo.edit.desText"
            :height="300"
          />
<!--          <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 20}"-->
<!--                    v-model="entityInfo.edit.desText"></el-input>-->
        </el-form-item>
        <el-form-item label="剧本帮助:" prop="helpText">
          <tinymce
            ref="tinymce_helpText"
            v-model="entityInfo.edit.helpText"
            :height="300"
          />
<!--          <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 20}"-->
<!--                    v-model="entityInfo.edit.helpText"></el-input>-->
        </el-form-item>
<!--        <el-form-item label="剧本配置-json:" prop="helpText">-->
<!--          <el-input type="textarea" autosize v-model="entityInfo.edit.configInfoString" :rows="10"></el-input>-->
<!--        </el-form-item>-->
        <el-form-item class="flex flex-around">
          <el-button type="default"
                     @click="EntityInfoMethods().clickCancelBtn()">取 消
          </el-button>
          <el-button type="success" v-if="entityInfo.type==='add'"
                     @click="EntityInfoMethods().clickAddBtn()">新 增
          </el-button>
          <el-button type="success" v-if="entityInfo.type==='edit'"
                     @click="EntityInfoMethods().clickEditBtn()">确认修改
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {isObjArrHasSameIdValue, isObjArrHasSameIdValueAndOtherId, objectToLVArr} from "@/utils/common";
import elDragDialog from "@/directive/el-drag-dialog";
import {validateMaxLength} from "@/utils/validate";
import {ScriptModel} from "@/model/ScriptModel";
import enums from "@/enums";
import {ScriptSeriesModel} from "@/model/ScriptSeriesModel";
import {CommonModel} from "@/model/CommonModel";
import Tinymce from "@/components/Tinymce"
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import {mapState} from "vuex";

export default {
  name: "scriptDetail",
  directives: {
    elDragDialog
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      userInfo: state => state.user,
      permissionArr: state => state.user.permission
    })
  },
  components: {Tinymce, erpUploaderOnePic},
  data() {
    return {
      window: window,
      enums: enums,
      // 详情
      entityInfo: {
        tagInputValue: "",
        tagInputShow: false,
        filter: {
          college: [],
          scriptSeries: []
        },
        $index: 0,
        title: "新增剧本",
        type: "add",
        dialog: false,
        edit: {
          opened: true,
          tags: []
        },
        uploading: false,
        uploadPreviewShow: false,
        // 输入检测
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 40, "剧本名称"), trigger: 'blur'},
          'addressText': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 30, "支持地点"),
            trigger: 'blur'
          },
          'opened': {required: true, message: '请选择是否开放', trigger: 'change'},
          'avatarUrl': {required: true, message: '请上传剧本头图', trigger: 'change'},
          'scriptSeriesId': {required: true, message: '请选择所属系列', trigger: 'change'},
          'shortDesText': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 200, "剧本简介"),
            trigger: 'blur'
          },
          'desText': {required: true, message: '请输入剧本介绍信息', trigger: 'change'},
          'helpText': {required: true, message: '请输入剧本帮助信息', trigger: 'change'},
        },
      },
    }
  },
  async mounted() {
    let type = this.$route.query["type"]
    this.entityInfo.type = type
    if (type === "edit") {// 编辑模式
      let id = this.$route.query["id"]
      let info = await ScriptModel.getOne(id)
      if (info) {
        info.configInfoString=JSON.stringify(info.configInfo)
        this.entityInfo.edit = info
      } else {
        msg_err("未找到该剧本信息！")
      }
    } else {
      //VEAModel.setBreadThirdTitle("新增剧本")
    }
    this.EntityInfoMethods().initFilter()
  },
  methods: {
    // 实体信息Methods
    EntityInfoMethods() {
      let $this = this
      return {
        clickCloseTagBtn(tag, index) {
          $this.entityInfo.edit.tags.splice(index, 1)
        },
        showTagInput() {
          $this.entityInfo.tagInputShow = true
          $this.$nextTick(_ => {
            $this.$refs.tagInput.$refs.input.focus();
          });
        },
        addNewTag() {
          if ($this.entityInfo.tagInputValue) {
            $this.entityInfo.edit.tags.push($this.entityInfo.tagInputValue)
          }
          $this.entityInfo.tagInputShow = false
          $this.entityInfo.tagInputValue = ""
        },
        // 初始化筛选列表
        async initFilter(type) {
          if (!type) {
            // 获取系列列表
            let list = await ScriptSeriesModel.getList({})
            let filterList = CommonModel.generateListFilterOptions("name", "scriptSeriesId", list, false)
            $this.$set($this.entityInfo.filter, "scriptSeries", filterList[0])
          }
        },
        // 点击新增按钮
        clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              if (await msg_confirm('确认要新增该剧本吗？')) {
                $this.entityInfo.edit.configInfo=JSON.parse($this.entityInfo.edit.configInfoString)
                if (await ScriptModel.addOrEdit($this.entityInfo.edit)) {
                  msg_success('新增成功')
                  $this.$router.go(-1);
                  $this.entityInfo.dialog = false
                }
              }
            }
          })
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              if (await msg_confirm('确认要修改该剧本信息吗？')) {
                $this.entityInfo.edit.configInfo=JSON.parse($this.entityInfo.edit.configInfoString)
                // 删除无需修改字段
                if (await ScriptModel.addOrEdit($this.entityInfo.edit)) {
                  msg_success('修改成功')
                  $this.entityInfo.dialog = false
                  $this.$router.go(-1);
                }
              }
            }
          })
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.$router.go(-1);
        }
      }
    },
    // ERP通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // ERP通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.el-tag + .el-tag {
  margin-left: 10px;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
