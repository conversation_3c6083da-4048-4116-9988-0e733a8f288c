<template>
  <div class="app-container flex flex-around">
    <!--生成并导出实验码-->
    <el-card style="width: 500px;" v-permission="['administrator']">
      <div slot="header">
        <span>生成实验码</span>
      </div>
      <el-form>
        <el-form-item label="选择剧本">
          <div>
            <el-select v-model="create.scriptId" style="width: 100%">
              <el-option v-for="item in scriptList" :key="item.scriptId" :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item label="生成个数">
          <el-input v-model="create.codeNumber" type="number"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="create.remarks"></el-input>
        </el-form-item>
        <div style="text-align: center">
          <el-button @click="clickCreateBtn" :loading="create.loading">生成并导出</el-button>
        </div>
      </el-form>
    </el-card>

    <!--查询实验码信息-->
    <el-card style="width: 1100px;" v-permission="['administrator']">
      <div slot="header">
        <span>查询实验码</span>
      </div>
      <el-form>
        <el-form-item label="实验码(支持多个，每行一个)">
          <el-input v-model="info.code" type="textarea"></el-input>
        </el-form-item>
        <div style="text-align: center">
          <el-button @click="clickInfoBtn" :loading="info.loading">查询</el-button>
        </div>
        <el-form-item label="查询结果">
          <el-table :data="info.infos" v-loading="info.loading" element-loading-text="加载中" border fit
                    highlight-current-row
                    style="width: 100%;">
            <el-table-column label="实验码" align="center" width="300px">
              <template slot-scope="scope">
                <span>{{ scope.row.code }}</span>
              </template>
            </el-table-column>
            <el-table-column label="剧本" align="center" width="300px">
              <template slot-scope="scope">
                <span>{{ scope.row.scriptName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.remarks }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否已使用" align="center" width="300px">
              <template slot-scope="scope">
                <span>{{ scope.row.used?'是':'否' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="使用记录" align="center">
              <template slot-scope="scope">
                <div>{{ scope.row.useInfoArrResult }}</div>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import {msg_err} from "@/utils/ele_component";
import {ScriptCodeModel} from "@/model/ScriptCodeModel";
import {date_format, getIpInfo} from "@/utils/common";
import {excel_export_from_json} from "@/utils/excel";
import permission from "@/directive/permission";
import {ToolsModel} from "@/model/ToolsModel";
import {ScriptModel} from "@/model/ScriptModel";
import {CommonModel} from "@/model/CommonModel";

export default {
  name: "scriptCode",
  directives: {
    permission
  },
  data() {
    return {
      scriptList: [],
      create: {
        codeNumber: 1,
        number: 2,
        remarks: "",
        loading: false
      },
      info: {
        code: "",
        infos: [],
        loading: false
      }
    }
  },
  async mounted() {
    // 获取剧本列表
    let scriptList = await ScriptModel.getList({});
    this.scriptList = CommonModel.generateListFilterOptions("name", "scriptId", scriptList)[0];
    this.scriptObject = CommonModel.generateListFilterOptions("name", "scriptId", scriptList)[1];
  },
  methods: {
    // 统计体验使用次数
    async cal() {
      let data = await ScriptCodeModel.getList({})
      let totalUse = 0;
      data.forEach(li => {
        totalUse += li.useInfoArr.length
      })
    },
    // 生成并导出实验码列表
    async clickCreateBtn() {
      // 检测参数
      let reg = /^[0-9]+$/
      if (!reg.test(this.create.number)) {
        msg_err("'只能输入正整数'")
        return
      }
      if (!reg.test(this.create.codeNumber)) {
        msg_err("'只能输入正整数'")
        return
      }
      if (!this.create.scriptId) {
        msg_err("请选择剧本！")
        return
      }
      this.create.loading = true
      let data = await ScriptCodeModel.addMultipleCode(this.create.codeNumber, this.create.scriptId, this.create.remarks, this.create.otherInfo).catch(e => {
        this.create.loading = false
      })
      if (data) {
        let scriptName = this.scriptObject[this.create.scriptId]
        // 导出列表
        // map reduce生成arr
        function formatJson(filterVal, jsonData) {
          return jsonData.map(v => filterVal.map(j => {
            let value = '';
            switch (j) {
              case "scriptName":
                value = scriptName
                break;
              default:
                value = v[j]
            }
            return value
          }))
        }

        const header = ['实验码', '剧本名称', "备注"];
        const filter = ["code", "scriptName", "remarks"];
        // 导出excel
        excel_export_from_json(data, header, filter, formatJson, scriptName + "-实验码列表-" + this.create.number + "个-" + date_format(new Date(), "yyyy-MM-dd HH:mm"))
        this.create.loading = false
      }
    },
    // 点击查询信息按钮
    async clickInfoBtn() {
      if (!this.info.code) {
        msg_err("请先输入实验码")
      }
      this.$set(this.info, "infos", [])
      let codes = this.info.code.split("\n")
      this.info.loading = true
      for (let i = 0; i < codes.length; i++) {
        let code = codes[i]
        let data = await ScriptCodeModel.getCodeInfo(code)
        if (data) {
          let str = ""
          for (let i = 0; i < data.useInfoArr.length; i++) {
            str += await ToolsModel.getIpInfoOnline1(data.useInfoArr[i].ip) + "   " + date_format(data.useInfoArr[i].useDate, "yyyy-MM-dd HH:mm:ss") + "\n"
          }
          data.useInfoArrResult = str
          data["scriptName"]=this.scriptObject[data.scriptId]
          this.info.infos.push(data)
        } else {
          this.info.infos.push({
            code: code,
            scriptName: "未找到",
            useInfoArr: []
          })
        }
      }
      this.info.loading = false
    },

  }
}
</script>

<style scoped lang="scss">

</style>
