<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
            <el-button type="success" @click="ListMethods().clickExportBtn()">导出</el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="剧本" align="center">
        <template slot-scope="scope">
          <span>{{ enums.scriptIdNames[scope.row.scriptId] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户邮箱/账号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userEntity[0]["email"]?scope.row.userEntity[0]["email"]:scope.row.userEntity[0]["account"] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="得分" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.score | scoreFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.startTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.endTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用时" align="center" width="140px">
        <template slot-scope="scope">
          <span v-if="scope.row.usedTime">{{ scope.row.usedTime | scoreUseTimeFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否已完成" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.completed ? "是" : "否" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完成任务数量" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.taskNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完成决策数量" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.decisionNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="获取线索数量" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.clueNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="100"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">

        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--导出记录弹窗-->
    <el-dialog
      title="导出剧本记录"
      :visible.sync="exportRecord.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="600px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="exportRecordForm" :model="exportRecord.edit" :rules="exportRecord.formRules">
          <el-form-item label="总共数量:" prop="totalNum">
            <span>{{ lists.pages.totalElements }}</span>
          </el-form-item>
          <el-form-item label="从第几条开始:" prop="startNum">
            <el-input v-model="exportRecord.edit.startNum" type="number"></el-input>
          </el-form-item>
          <el-form-item label="到第几条结束:" prop="endNum">
            <el-input v-model="exportRecord.edit.endNum" type="number"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="exportRecord.dialog=false">取 消</el-button>
        <el-button type="success" @click="ListMethods().clickExportStartBtn()">开始导出</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat, scoreFormat, scoreUseTimeFilter} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {ScriptRecordModel} from "@/model/ScriptRecordModel";
import {UserModel} from "@/model/UserModel";
import {ScriptSeriesModel} from "@/model/ScriptSeriesModel";
import {CommonModel} from "@/model/CommonModel";
import {ScriptModel} from "@/model/ScriptModel";
import {msg_err} from "@/utils/ele_component";

export default {
  name: "recordList",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat, scoreFormat, scoreUseTimeFilter},
  data() {
    let $this = this;
    return {
      enums: enums,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [

          ],
          filter: [
            {
              type: 'selectRemote',
              label: '所属脚本',
              placeholder: "",
              key: 'scriptId',
              width: "270px",
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              searchFunction: async function (query) {
                query = `{"name":{'$regex': ".*${query}.*"}}`
                let list = await ScriptModel.getList(query)
                let listResult = CommonModel.generateListFilterOptions("name", "scriptSeriesId", list)
                $this.$set($this.lists.searchFilter.filter[0], "data", listResult[0])
                $this.$set($this.lists.searchFilter.filter[0], "dataObject", listResult[1])
              },
              async change(v) {
                let query = `{}`
                let list = await ScriptModel.getList(query)
                let listResult = []
                list.forEach(li => {
                  listResult.push({
                    label: li.name,
                    value: li.scriptSeriesId
                  })
                })
                $this.$set($this.lists.searchFilter.filter[0], "data", listResult)
              }
            },
          ],
        }
      },
      userInfo: {},
      entityInfo: {
        dialog: false,
        title: "新增标签",
        type: "add",
        filter: {},
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {},
      },
      // 导出剧本记录
      exportRecord: {
        doing: false,
        edit: {},
        dialog: false,
      },
    }
  },
  mounted() {
    // 获取列表
    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击批量导出按钮
        async clickExportBtn() {
          $this.exportRecord.dialog = true
        },
        // 点击开始导出成绩按钮
        async clickExportStartBtn() {
          let totalNumber = $this.lists.pages.totalElements
          let startNum = parseInt($this.exportRecord.edit.startNum)
          let endNum = parseInt($this.exportRecord.edit.endNum)
          // 判断输入合法性
          if (startNum > 0 && startNum <= totalNumber && startNum <= endNum) {
            if (endNum > 0 && endNum <= totalNumber) {
              let query = $this.lists.queryLast;
              await ScriptRecordModel.exportRecordList(startNum, endNum, query)
              $this.exportRecord.dialog = false
            } else {
              msg_err("结束数字不合法！")
            }
          } else {
            msg_err("开始数字不合法！")
          }
        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          $this.lists.queryLast = query;
          [list, $this.lists.pages] = await ScriptRecordModel.getPageList(page - 1, size, "", query)
          for (let i = 0; i < list.length; i++) {
            list[i]["userEntity"] = []
            list[i]["userEntity"][0] = await UserModel.getOne(list[i]["userId"])
          }
          // 遍历list获取必要信息
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
      }
    },
    // 实体方法集
    EntityMethods() {
      let $this = this;
    },
  }
}
</script>

<style scoped lang="scss">
.top-tools {
  margin-bottom: 15px;
}
</style>
