<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
<!--          <el-button v-permission="['administrator']" class="el-button" type="success"-->
<!--                     @click="ListMethods().clickAddBtn()"-->
<!--                     style="background-color: #67C23A;border-color:#67C23A">新增剧本-->
<!--          </el-button>-->
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="剧本名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属系列" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.scriptSeriesName }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="开放状态" align="center" width="80px">-->
<!--        <template slot-scope="scope">-->
<!--          <span>{{ scope.row.opened ? '开放' : '关闭' }}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="支持地点" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.addressText }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="350"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickViewBtn(scope.row)">编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {validateMaxLength} from "@/utils/validate";
import {ScriptModel} from "@/model/ScriptModel";
import {ScriptSeriesModel} from "@/model/ScriptSeriesModel";
import {CommonModel} from "@/model/CommonModel";

export default {
  name: "scriptManage",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      userInfo: state => state.user,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    // 校检名称
    const validateName = (rule, value, callback, maxLength, propName) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入' + propName))
        return
      }
      if (value.length > maxLength) {
        callback(new Error('最多输入' + maxLength + '个字，当前已输入' + value.length + "个字"))
      }
      // 特殊字符检测
      let regEn = /[`!#$%^&*()@_+<>?:"{},.\/;'[\]]/im,
        regCn = /[·！#￥（——）：；“”‘、，|《。》？、【】[\]]/im;
      if (regEn.test(value) || regCn.test(value)) {
        callback(new Error('不支持特殊符号'));
      }
      // 仅支持中英文
      regEn = /[a-zA-Z]+/
      regCn = /[\u4e00-\u9fa5]+/g
      let regNumber = /^[0-9]+$/
      if (regEn.test(value) || regCn.test(value) || regNumber.test(value)) {

      } else {
        callback(new Error('仅支持中英文和数字'));
      }
      callback()
    }
    return {
      enums: enums,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '剧本名称',
              key: 'name',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [
            {
              type: 'selectRemote',
              label: '所属系列',
              placeholder: "",
              key: 'scriptSeriesId',
              width: "270px",
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              searchFunction: async function (query) {
                query = `{"name":{'$regex': ".*${query}.*"}}`
                let list = await ScriptSeriesModel.getList(query)
                let listResult = CommonModel.generateListFilterOptions("name", "scriptSeriesId", list)
                $this.$set($this.lists.searchFilter.filter[0], "data", listResult[0])
                $this.$set($this.lists.searchFilter.filter[0], "dataObject", listResult[1])
              },
              async change(v) {
                let query = `{}`
                let list = await ScriptSeriesModel.getList(query)
                let listResult = []
                list.forEach(li => {
                  listResult.push({
                    label: li.name,
                    value: li.scriptSeriesId
                  })
                })
                $this.$set($this.lists.searchFilter.filter[0], "data", listResult)
              }
            },
          ],
        }
      },
    }
  },
  async mounted() {
    // 初始化系列列表
    await this.lists.searchFilter.filter[0].searchFunction("")
    // 获取列表
    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          [list, $this.lists.pages] = await ScriptModel.getPageList(page - 1, size, "", query)
          // 遍历list获取必要信息
          list.forEach(li => {
            // 获取系列名称
            li.scriptSeriesName = $this.lists.searchFilter.filter[0]["dataObject"][li.scriptSeriesId]
          })
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击新增按钮
        clickAddBtn() {
          $this.$router.push({
            name: "scriptDetail",
            query: {
              type: "add"
            }
          })
        },
        // 点击详情按钮
        clickViewBtn(entity, $index) {
          $this.$router.push({
            name: "scriptDetail",
            query: {
              type: "edit",
              id: entity.scriptId
            }
          })
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top-tools {
  margin-bottom: 15px;
}
</style>
