<template>
  <div class="page-container" v-show="screenDirection==='landscape'" id="fdldq-a1">
    <div id="three"></div>
  </div>

  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
  <div class="full-btn full-btn-landscape" @click="screenfull.toggle()"
       v-if="mobileType!=='iPhone'&&screenDirection==='landscape'">
    <img src="@/assets/ui/fdldq/common/full-btn-icon.png" alt="">
    <span>全屏</span>
  </div>
</template>

<script>
import {msg_err_vant,} from "../../utils/nut_component";
import screenfull from 'screenfull'
import {unity_fdldq_chooseRole} from "../../scripts/fdldq/unityMethods";
import {TaskStore} from "../../store/TaskStore";
import {ScriptRecordModel} from "../../model/ScriptRecordModel";
import {TaskModel} from "../../model/TaskModel";
import {mapState} from "pinia";
import * as THREE from 'three'
import {GLTFLoader} from 'three/examples/jsm/loaders/GLTFLoader'
import {DRACOLoader} from 'three/examples/jsm/loaders/DRACOLoader'
import {CSS2DObject, CSS2DRenderer} from 'three/examples/jsm/renderers/CSS2DRenderer';
import {ThreeModel} from "../../model/ThreeModel";
import {EffectComposer} from "three/examples/jsm/postprocessing/EffectComposer.js"
import {RenderPass} from "three/examples/jsm/postprocessing/RenderPass.js"
import {OutlinePass} from "three/examples/jsm/postprocessing/OutlinePass.js"
import {ShaderPass} from "three/examples/jsm/postprocessing/ShaderPass.js"
import {FXAAShader} from "three/examples/jsm/shaders/FXAAShader.js"
import {showLoadingToast} from 'vant';
import {ToolsModel} from "../../model/ToolsModel";
import $ from 'jquery'
import {OrbitControls} from 'three/examples/jsm/controls/OrbitControls'

export default {
  name: "f_a1_View",
  components: {},
  computed: {
    ...mapState(TaskStore, {
      nowTaskId: store => store.nowTaskId,
      recordInfo: store => store.recordInfo,
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull,
      mobileType: store => store.mobileType,
    }),
  },
  watch: {
    // 监听是否全屏
    screenFull(newVal, oldVal) {
      window.renderer.setSize(window.innerWidth, window.innerHeight)
      window.css2DRender.setSize(window.innerWidth, window.innerHeight)
    },
    // 监听横竖屏
    screenDirection(newVal, oldVal) {
      if (newVal === "landscape") {// 如果切换为横屏
        // 如果实验已加载就设置分辨率
        if (!this.three.loaded) {
          setTimeout(() => {
            this.ThreeMethods().load()
          }, 1000) // 切换横竖屏后，要等待一段时间才能获取到innerWidth
        } else {
          if (window.css2DRender) {
            $("#fdldq_a1_css2DRender").show()
            // 切换到横屏要重新设置css2d，不然点击失效
            window.css2DRender.setSize(window.innerWidth, window.innerHeight)
          }
        }
      }
      // 如果切换为竖屏竖屏，恢复metaViewPort
      if (newVal === "portrait") {
        ToolsModel.resetMetaViewport();
        if (window.css2DRender) {
          $("#fdldq_a1_css2DRender").hide()
        }
      }
    }
  },
  data() {
    return {
      taskId: "A1",
      screenfull: screenfull,
      roleId: "r1",
      three: {
        loaded: false,
        INTERSECTED: null, // 暂存射线相交的物体（交互的模型对象）
        mouse: new THREE.Vector2(),
        xuFengObjects: [],
        liShiQiangObjects: []
      }
    }
  },
  async beforeCreate() {

  },
  async mounted() {
    document.title = "A1-长征困局"
    let $this = this;
    // 关卡记录-不允许返回
    //await TaskModel.judgeOneTaskIsCompletedOrJumpBack("A1")

    // 如果是横屏才加载
    if (this.screenDirection === "landscape") {
      await $this.ThreeMethods().load()
    }
  },
  beforeUnmount() {
    this.ThreeMethods().beforeExit()
  },
  beforeRouteLeave() {
    this.ThreeMethods().beforeExit()
  },
  methods: {
    ThreeMethods() {
      let $this = this;
      return {
        // 开始加载
        async load() {
          // 如果是在本页才加载，避免生命周期错误 todo 优化逻辑
          if (window.location.href.indexOf("fdldq/a1") !== -1) {
            showLoadingToast({
              message: '加载中...',
              forbidClick: true,
            });
            $this.three.loaded = true
            await $this.ThreeMethods().init()
          }
        },
        // 初始化
        async init() {
          // WebGL渲染器
          const width = window.innerWidth, height = window.innerHeight;
          const renderer = new THREE.WebGLRenderer()
          renderer.setSize(width, height)
          renderer.setPixelRatio(window.devicePixelRatio)
          document.getElementById('three').appendChild(renderer.domElement)

          // 场景
          const scene = new THREE.Scene()
          $this.three.clock = new THREE.Clock()

          // 光
          const ambient = new THREE.AmbientLight(0xffffff, 0.5),
              light1 = new THREE.PointLight(0xffffff, 0.4),
              light2 = new THREE.PointLight(0xffffff, 0.4)
          const hemiLight = new THREE.HemisphereLight(0xffffff, 0x444444)
          scene.add(ambient)
          light1.position.set(200, 300, 400)
          scene.add(light1)
          light2.position.set(-200, -300, -400)
          scene.add(light2)
          scene.add(hemiLight)

          // 相机
          const camera = new THREE.PerspectiveCamera(60, width / height, 0.3, 1000)
          camera.position.set(-5, 3, -18)
          camera.lookAt(0, 0, 0)
          camera.layers.enableAll();
          camera.layers.toggle(1);

          // 辅助坐标轴
          const axesHelper = new THREE.AxesHelper(100)
          scene.add(axesHelper)


          // 模型加载-draco
          let dracoLoader = new DRACOLoader()
          dracoLoader.setDecoderPath("/models/fdldq/gltf/")
          let gltfLoader = new GLTFLoader()
          gltfLoader.setDRACOLoader(dracoLoader)

          // css2D渲染器
          let css2DRender = new CSS2DRenderer();
          css2DRender.setSize(width, height)
          css2DRender.domElement.style.position = 'absolute';
          // css2DRender.domElement.style.zIndex = -1
          css2DRender.domElement.style.top = '0px';
          css2DRender.domElement.style.touchAction = "none"
          css2DRender.domElement.id = "fdldq_a1_css2DRender"
          document.body.appendChild(css2DRender.domElement);


          // 视角控制器
          const controls = new OrbitControls(camera, css2DRender.domElement)

          // window全局变量-避免渲染错误
          window.css2DRender = css2DRender
          window.renderer = renderer
          window.scene = scene
          window.camera = camera
          window.raycaster = new THREE.Raycaster();


          // 等待一段时间后开始渲染
          setTimeout(() => {
            $this.ThreeMethods().render()
            // 点击事件监听
            $this.ThreeMethods().listenOnMouseTouch()
          }, 500)
        },

        // 渲染器
        render() {
          let delta = $this.three.clock.getDelta()
          window.renderId = requestAnimationFrame($this.ThreeMethods().render)
          window.renderer.render(window.scene, window.camera)
          window.css2DRender.render(window.scene, window.camera);

        },
        // 触摸事件
        onMouseTouch: function (event) {
          event.preventDefault();
          // 将鼠标位置归一化为设备坐标。x 和 y 方向的取值范围是 (-1 to +1)
          // renderer为three的渲染器
          let px = window.renderer.domElement.getBoundingClientRect().left;
          let py = window.renderer.domElement.getBoundingClientRect().top;
          $this.three.mouse.x = ((event.touches[0].clientX - px) / (window.renderer.domElement.offsetWidth)) * 2 - 1;
          $this.three.mouse.y = -((event.touches[0].clientY - py) / (window.renderer.domElement.offsetHeight)) * 2 + 1;
          // 通过摄像机和鼠标位置更新射线
          window.raycaster.setFromCamera($this.three.mouse, window.camera);
          // 计算物体和射线的焦点
          let intersects = window.raycaster.intersectObjects([], true);
          if (intersects.length > 0) {

          } else {
          }
        },
        // 选择角色高亮
        outLineRole(roleId) {

        },
        // 开始监听触摸事件
        listenOnMouseTouch: function () {
          window.touchStartListenFn = (e) => {
            $this.ThreeMethods().onMouseTouch(e);
          }
          window.addEventListener('touchstart', window.touchStartListenFn);
        },
        // 高亮显示模型
        outlineObj(selectedObjects) {
          // 创建一个EffectComposer（效果组合器）对象，然后在该对象上添加后期处理通道。
          let composer = new EffectComposer(window.renderer)
          // 新建一个场景通道  为了覆盖到原理来的场景上
          let renderPass = new RenderPass(window.scene, window.camera)
          composer.addPass(renderPass);
          // 物体边缘发光通道
          let outlinePass = new OutlinePass(new THREE.Vector2(window.innerWidth, window.innerHeight), window.scene, window.camera, selectedObjects)
          outlinePass.selectedObjects = selectedObjects
          outlinePass.edgeStrength = 15.0 // 边框的亮度
          outlinePass.edgeGlow = 2// 光晕[0,1]
          outlinePass.usePatternTexture = false // 是否使用父级的材质
          outlinePass.edgeThickness = 2.0 // 边框宽度
          outlinePass.downSampleRatio = 1 // 边框弯曲度
          outlinePass.visibleEdgeColor.set(parseInt(0xff0000)) // 呼吸显示的颜色
          outlinePass.clear = true
          composer.addPass(outlinePass)
          // 自定义的着色器通道 作为参数
          let effectFXAA = new ShaderPass(FXAAShader)
          effectFXAA.uniforms.resolution.value.set(0.25 / window.innerWidth, 0.25 / window.innerHeight) // 决定选择后屏幕清晰度，1会变模糊
          effectFXAA.renderToScreen = true
          composer.addPass(effectFXAA)
          window.composer = composer
        },
        //退出前清理
        beforeExit() {
          if (!window.scene) {
            return
          }
          cancelAnimationFrame(window.renderId);// Stop the animation
          // 消除threejs和全局变量
          if (document.querySelector("#fdldq_a1_css2DRender")) {
            document.querySelector("#fdldq_a1_css2DRender").remove()
          }
          delete window.scene
          delete window.renderer
          delete window.scene
          delete window.camera
          delete window.raycaster
          delete window.mesh1
          delete window.mesh2
          delete window.composer
          delete window.renderId
          window.removeEventListener('touchstart', window.touchStartListenFn);
          delete window.css2DRender
          delete window.touchStartListenFn
          console.log("清理完成")
        }
      }
    },
  }
}
</script>
<style>
/*动态添加的dom*/
#fdldq_a1_css2DRender .label {
  color: #fff;
  font-size: 7px;
}

#fdldq_a1_css2DRender .label-active {
  color: #ff0000;
  font-size: 10px;
  font-weight: bold;
}

#fdldq_a1_css2DRender #chooseRoleBtn {
  position: fixed;
  margin: auto;
  left: 0;
  right: 0;
  bottom: 10px;
  width: 70px;
  padding: 5px 10px;
  color: #555;
  font-size: 6px;
  border-radius: 10px;
  font-weight: bold;
  border: 0px;
}
</style>
<style scoped lang="less">

.page-container {
  background-color: rgba(0, 0, 0, 0.6);
  min-height: 100vh;
  color: #fff;

  .task-title {
    text-align: center;
    font-size: 10px;
    color: #fff;
  }
}
</style>
