<template>
  <div class="webgl-content" v-show="screenDirection==='landscape'">
    <!--    <div id="unityContainer" style="position: absolute; left: 50%; top: 50%; -webkit-transform: translate(-50%, -50%); transform: translate(-50%, -50%);width: 100%; height: 100%;">-->
    <div id="unityContainer">
      <canvas id="#canvas" tabindex="-1"></canvas>
    </div>
    <div id="unity-progress">
      <div id="scene-text"></div>
      <div id="progress-text"></div>
    </div>
    <van-button type="success" class="full-btn" @click="toggleFull" v-if="webglInfo.loaded">全屏</van-button>
    <van-button type="success" class="complete-btn" @click="clickRefreshBtn" v-if="webglInfo.loaded&&nowTaskId!=='S1'">
      刷新
    </van-button>
  </div>
  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
</template>

<script>
/**
 *
 */


import screenfull from 'screenfull'
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";
import {UnityModel} from "../../model/unity/UnityModel";

export default {
  name: "f_testOther_View",
  components: {},
  computed: {
    ...mapState(TaskStore, {
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  data() {
    return {
      screenfull: screenfull,
      nowTaskId: "Test",
      taskConfig: {
        taskId: "Test",
        name: "其他学校的"
      },
      taskInfo: {},
      webglInfo: {
        screenWidth: false,
        screenHeight: false,
        width: false,
        height: false,
        loaded: false,
      },
    }
  },
  watch: {
    // 监听是否全屏
    screenFull(newVal, oldVal) {

    },
    // 监听横竖屏
    screenDirection(newVal, oldVal) {
      if (newVal === "landscape") {// 如果切换为横屏
        if (!this.webglInfo.width) {// 如果尚未设置宽高
          setTimeout(() => {
            this.saveWidthAndHeight()
            this.loadWebgl()
          }, 1000) // 切换横竖屏后，要等待一段时间才能获取到innerWidth
        }
        // 如果实验已加载就设置分辨率
        if (this.webglInfo.loaded) {

        }
      }
      // 如果切换为竖屏竖屏，恢复metaViewPort
      if (newVal === "portrait") {
        this.resetMetaViewport();
      }
    }
  },
  async mounted() {
    let $this = this;
    document.title = this.taskConfig["taskId"] + "-" + this.taskConfig["name"]

    // 如果加载时是横屏
    if (this.screenDirection === "landscape") {
      this.saveWidthAndHeight()
      // 加载实验
      this.loadWebgl()
    }
  },
  methods: {
    clickRefreshBtn() {
      window.location.reload()
    },
    // 切换全屏
    toggleFull() {
      unityInstance.SetFullscreen(1);
      // if (screenfull.isEnabled) {
      //   screenfull.toggle();
      // 只将canvas全屏，退出实验后会自动退出全屏
      // screenfull.request(document.getElementById("#canvas"), {navigationUI: 'hide'});
      // }
    },
    // 错误提示
    showWebglMsg(msg, type) {
      console.log(msg, type)
    },
    // 加载unity webgl
    loadWebgl() {
      let $this = this;
      // let unityPath = "https://resouce.cdzyhd.com/experiment/webgl/arjbs/fdldq/2022121213/test2022/"
      let unityPath = "https://resouce.cdzyhd.com/experiment/webgl/arjbs/fdldq/2022121213/other/"
      // let unityPath = "/webgl/other/"
      let config = {
        dataUrl: unityPath + "Build/Build_WEBGL_Mobile.data.unityweb",
        frameworkUrl: unityPath + "Build/Build_WEBGL_Mobile.framework.js.unityweb",
        codeUrl: unityPath + "Build/Build_WEBGL_Mobile.wasm.unityweb",
        streamingAssetsUrl: unityPath + "/StreamingAssets",
        companyName: "DefaultCompany",
        productName: "FDLDQ_Webgl",
        productVersion: "0.1",
        showBanner: this.showWebglMsg,
      };
      config.devicePixelRatio = 1.5; // 设置物理分辨率缩放

      let canvas = document.getElementById("#canvas")
      let width = this.webglInfo.width
      let height = this.webglInfo.height
      canvas.width = Math.round(width);
      canvas.height = Math.round(height);
      canvas.style.width = width + "px"
      canvas.style.height = height + "px"
      UnityModel.loadWebGlResource2022(unityPath+"Build/Build_WEBGL_Mobile.loader.js").then(loaded => {
        if (loaded) {
          window.createUnityInstance(canvas, config, (progress) => {
            console.log("速度", progress)
            document.getElementById("scene-text").innerText = $this.taskConfig["taskId"] + "-" + $this.taskConfig["name"]
            document.getElementById("progress-text").innerText = "加载中，请稍后 " + Math.floor(progress * 100) + "%"
            if (progress === 1) {
              document.getElementById("unity-progress").style.display = "none"
            }
          }).then((unityInstance) => {
            window.unityInstance = unityInstance
            $this.webglInfo.loaded = true
          }).catch((message) => {
            alert(message);
          });
        }
      })
    },
    // 保存横屏屏幕和视窗大小
    saveWidthAndHeight() {
      // 保存屏幕和操作区域信息
      console.log("获取宽高")
      this.webglInfo.width = window.innerWidth
      this.webglInfo.height = window.innerHeight
      this.webglInfo.screenWidth = window.screen.width
      this.webglInfo.screenHeight = window.screen.height
    },

    // 重置metaViewport
    resetMetaViewport() {
      // 重置meta viewport
      let metaViewport = document.getElementById("viewport")
      if (metaViewport) {
        // metaViewport.parentNode.removeChild(metaViewport)
        metaViewport.setAttribute('content', 'width=device-width, height=device-height, initial-scale=1.0, user-scalable=no, shrink-to-fit=yes');
      }
    }
  }
}
</script>

<style scoped lang="less">
.webgl-content {
  position: relative;
  background-color: #221f20;
  min-height: 100vh;

  #unity-progress {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    color: #fff;
    width: 150px;
    text-align: center;
    height: 0px;
    margin: auto;
    background-color: #f6f6f6;

    #scene-text {
      margin-top: -20px;
      margin-bottom: 5px;
      font-size: 20px;
    }
  }

}

.full-btn {
  position: absolute;
  top: 0px;
  left: 0px;
}

.complete-btn {
  position: absolute;
  top: 0px;
  right: 0px;
}

.direction-tip {
  line-height: 100vh;
  text-align: center;
  font-size: 25px;
  color: #333;
}
</style>
