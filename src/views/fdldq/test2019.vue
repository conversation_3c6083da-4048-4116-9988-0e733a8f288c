<template>
  <div class="webgl-content" v-show="screenDirection==='landscape'">
    <!--    <div id="unityContainer" style="position: absolute; left: 50%; top: 50%; -webkit-transform: translate(-50%, -50%); transform: translate(-50%, -50%);width: 100%; height: 100%;">-->
    <div id="unityContainer">

    </div>
    <div id="canvas-click"></div>
    <div id="unity-progress">
      <div id="scene-text">123</div>
      <div id="progress-text">50%</div>
    </div>
    <van-button type="success" class="full-btn" @click="toggleFull">全屏</van-button>
    <van-button type="success" class="complete-btn" @click="clickRefreshBtn">刷新</van-button>
  </div>
  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
</template>

<script>
/**
 * 崩溃解决 不要webgl放大，d2还是容易崩
 *
 * 显示清晰解决
 */


import {msg_err_vant, msg_success_vant, toast_text} from "../../utils/nut_component";
import screenfull from 'screenfull'
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";
import {TaskModel} from "../../model/TaskModel";
import {
  unity_fdldq_afterPlayVideo,
  unity_fdldq_beforePlayVideo,
  unity_fdldq_chooseRole,
  unity_fdldq_completeOneSelect,
  unity_fdldq_completeOneTask, unity_fdldq_getOneClue, unity_fdldq_loadedScene, unity_fdldq_setScene
} from "../../scripts/fdldq/unityMethods";
import {ToolsModel} from "../../model/ToolsModel";
import {ScriptRecordModel} from "../../model/ScriptRecordModel";
import {UnityModel} from "../../model/unity/UnityModel";

export default {
  name: "f_test2019_View",
  components: {},
  computed: {
    ...mapState(TaskStore, {
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  data() {
    return {
      screenfull: screenfull,
      nowTaskId: "Test",
      taskConfig: {
        taskId: "Test",
        name: "四渡赤水大"
      },
      taskInfo: {},
      webglInfo: {
        screenWidth: false,
        screenHeight: false,
        width: false,
        height: false,
        loaded: false,
      },
    }
  },
  watch: {
    // 监听是否全屏
    screenFull(newVal, oldVal) {
      this.toggleWebglShow();
    },
    // 监听横竖屏
    screenDirection(newVal, oldVal) {
      if (newVal === "landscape") {// 如果切换为横屏
        if (!this.webglInfo.width) {// 如果尚未设置宽高
          setTimeout(() => {
            this.saveWidthAndHeight()
            this.loadWebgl()
          }, 1000) // 切换横竖屏后，要等待一段时间才能获取到innerWidth
        }
        // 如果实验已加载就设置分辨率
        if (this.webglInfo.loaded) {
          this.toggleWebglShow();
        }
      }
      // 如果切换为竖屏竖屏，恢复metaViewPort
      if (newVal === "portrait") {
        this.resetMetaViewport();
      }
    }
  },
  async mounted() {


    let $this = this;
    document.title = this.taskConfig["taskId"] + "-" + this.taskConfig["name"]

    // 如果加载时是横屏
    if (this.screenDirection === "landscape") {
      this.saveWidthAndHeight()
      // 加载实验
      this.loadWebgl()
    }
  },
  methods: {
    clickRefreshBtn() {
      window.location.reload()
    },
    // 切换全屏
    toggleFull() {
      if (screenfull.isEnabled) {
        screenfull.toggle();
        // 只将canvas全屏，退出实验后会自动退出全屏
        // screenfull.request(document.getElementById("#canvas"), {navigationUI: 'hide'});
      }
    },
    // 切换显示分辨率
    toggleWebglShow() {
      if (this.screenFull) {
        // todo 全屏时webgl显示清晰
        console.log("全屏显示")
        this.unitySizeFixedOnFull()
      } else {
        console.log("非全屏显示")
        this.unitySizeFixed(2000, true)
      }
    },
    // 加载unity webgl
    loadWebgl() {
      let unityPath = "https://resouce.cdzyhd.com/experiment/webgl/arjbs/fdldq/2022121213/test2019/"
      // let unityPath = "/webgl/sdcs/"
      UnityModel.loadWebGlResource2019(unityPath).then(loaded => {
        if (loaded) {
          window.unityInstance = UnityLoader.instantiate("unityContainer", unityPath + "Build/SDCSTest.json", {onProgress: (unityInstance, progress) => UnityProgress(unityInstance, progress, `${this.nowTaskId}-${this.taskConfig.name}`)});
        }
      })
    },
    // 调整unity webgl尺寸，解决模糊和显示问题
    unitySizeFixed(timeOut, plus) {
      let width = this.webglInfo.width
      let height = this.webglInfo.height
      if (plus) {
        width = width * 1.5 // 乘以屏幕缩放率
        height = height * 1.5
      }
      let metaViewport = document.getElementById("viewport")
      if (metaViewport) {
        // metaViewport.parentNode.removeChild(metaViewport)
        metaViewport.setAttribute('content', 'width=' + width + "");// todo 移动端能否正常缩放清晰看这个viewport和webgl的viewport
      }
      let canvas = document.getElementById("#canvas")
      canvas.width = Math.round(width);
      canvas.height = Math.round(height);
      let gl = canvas.getContext("webgl2");
      window.gl = gl
      gl.viewport(0, 0, gl.drawingBufferWidth, gl.drawingBufferHeight); // 这么没什么作用？？
      document.getElementById("#canvas").style.width = width + "px"
      document.getElementById("#canvas").style.height = height + "px"
      setTimeout(() => {

      }, timeOut ? timeOut : 3000)
    },

    // 调整unity webgl尺寸，解决显示问题--全屏时 todo 也显示清晰
    /**
     * 全屏时width设置为0，后面再放大，可以显示未经缩放的webgl图片
     * 好像全屏时直接设置canvas style的width和height就可以了显示一倍不清晰webgl
     * 全屏所有和全屏canvas试下
     */
    unitySizeFixedOnFull() {
      let width = this.webglInfo.screenWidth
      let height = this.webglInfo.screenHeight
      let metaViewport = document.getElementById("viewport")
      if (metaViewport) {
        // metaViewport.parentNode.removeChild(metaViewport)
        metaViewport.setAttribute('content', 'width=' + width);
      }
      let canvas = document.getElementById("#canvas")
      canvas.width = Math.round(width);
      canvas.height = Math.round(height);
      let gl = canvas.getContext("webgl2");
      window.gl = gl
      gl.viewport(0, 0, gl.drawingBufferWidth, gl.drawingBufferHeight);
      document.getElementById("#canvas").style.width = width + "px"
      document.getElementById("#canvas").style.height = height + "px"
      setTimeout(() => {

      }, 3000)
    },

    /**
     * 监听-设置场景
     */
    onSetScene() {

    },


    // 保存横屏屏幕和视窗大小
    saveWidthAndHeight() {
      // 保存屏幕和操作区域信息
      console.log("获取宽高")
      this.webglInfo.width = window.innerWidth
      this.webglInfo.height = window.innerHeight
      this.webglInfo.screenWidth = window.screen.width
      this.webglInfo.screenHeight = window.screen.height
    },

    // 重置metaViewport
    resetMetaViewport() {
      // 重置meta viewport
      let metaViewport = document.getElementById("viewport")
      if (metaViewport) {
        // metaViewport.parentNode.removeChild(metaViewport)
        metaViewport.setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=no');
      }
    }
  }
}
</script>

<style scoped lang="less">
.webgl-content {
  position: relative;

  #canvas-click {
    width: 100%;
    height: 100vh;
    position: fixed;
    z-index: 1000;
  }

  #unity-progress {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    color: #fff;
    width: 150px;
    text-align: center;
    height: 0px;
    margin: auto;

    #scene-text {
      margin-top: -20px;
      margin-bottom: 5px;
      font-size: 20px;
    }
  }

}

.full-btn {
  position: absolute;
  top: 0px;
  left: 0px;
}

.complete-btn {
  position: absolute;
  top: 0px;
  right: 0px;
}

.direction-tip {
  line-height: 100vh;
  text-align: center;
  font-size: 25px;
  color: #333;
}
</style>
