<template>
  <div class="end-container" v-show="screenDirection==='landscape'">
    <div class="info-container flex flex-dr flex-center">
      <div class="script-title">飞夺泸定桥</div>
      <div class="total-score-info flex flex-start">
        <span class="score">{{ scoreFormat(recordEntity.score) }}</span>
        <span class="text">分</span>
      </div>
      <div class="total-time">
        <div class="title flex flex-center">
          <img src="@/assets/ui/fdldq/end/totalTime.png" alt="">
          <span>总用时：</span>
        </div>
        <div class="time">{{ timeFilter(recordEntity.usedTime, "yyyy-MM-dd HH:mm:ss") }}</div>
      </div>
      <div class="score-info flex flex-around">
        <div class="score-li flex flex-dr flex-center">
          <div class="title flex flex-start">
            <img src="@/assets/ui/fdldq/end/timeScore.png" alt="" class="time">
            <span>时间分</span>
          </div>
          <div class="score">{{ scoreFormat(recordEntity.scoreInfo["timeScore"]) }}</div>
        </div>
        <div class="score-li flex flex-dr flex-center">
          <div class="title flex flex-start">
            <img src="@/assets/ui/fdldq/end/decisionScore.png" alt="" class="decision">
            <span>决策分</span>
          </div>
          <div class="score">{{ scoreFormat(recordEntity.scoreInfo["decisionScore"]) }}</div>
        </div>
        <div class="score-li flex flex-dr flex-center">
          <div class="title flex flex-start">
            <img src="@/assets/ui/fdldq/end/clueScore.png" alt="" class="clue">
            <span>线索分</span>
          </div>
          <div class="score">{{ scoreFormat(recordEntity.scoreInfo["clueScore"]) }}</div>
        </div>
      </div>
      <div class="number-info flex flex-around">
        <div class="number-li flex flex-dr flex-center">
          <div class="title">完成任务数量</div>
          <div class="number">{{ recordEntity.taskNumber }}</div>
        </div>
        <div class="number-li flex flex-dr flex-center">
          <div class="title">完成决策数量</div>
          <div class="number">{{ recordEntity.decisionNumber }}</div>
        </div>
        <div class="number-li flex flex-dr flex-center">
          <div class="title">获取线索数量</div>
          <div class="number">{{ recordEntity.clueNumber }}</div>
        </div>
      </div>
      <div class="time-info flex flex-around">
        <div class="time-li flex flex-dr flex-center">
          <div class="title">开始时间</div>
          <div class="time">{{ dateFormat(recordEntity.startTime, "yyyy-MM-dd HH:mm:ss") }}</div>
        </div>
        <div class="time-li flex flex-dr flex-center">
          <div class="title">结束时间</div>
          <div class="time">{{ dateFormat(recordEntity.endTime, "yyyy-MM-dd HH:mm:ss") }}</div>
        </div>
      </div>
    </div>
    <div class="exit-btn common-block-btn" @click="clickEndBtn" v-if="!recordId">结束剧本</div>
    <div class="exit-btn common-block-btn" @click="clickExitBtn" v-if="recordId">退出</div>
  </div>
  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>

    <div class="full-btn full-btn-landscape" @click="screenfull.toggle()" v-if="mobileType!=='iPhone'">
    <img src="@/assets/ui/fdldq/common/full-btn-icon.png" alt="">
    <span>全屏</span>
  </div>
</template>

<script>
import {toast_err} from "../../utils/nut_component";
import screenfull from 'screenfull'
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";
import {TaskModel} from "../../model/TaskModel";
import {dateFormat, scoreFormat, timeFilter} from "../../filters";
import {ScriptRecordModel} from "../../model/ScriptRecordModel";
import {getQuery} from "../../utils/common";
import {ToolsModel} from "../../model/ToolsModel";

export default {
  name: "f_end_View",
  components: {},
  computed: {
    ...mapState(TaskStore, {
      recordInfo: store => store.recordInfo,
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull,
      mobileType: store => store.mobileType,
    }),
  },
  data() {
    return {
      timeFilter: timeFilter,
      dateFormat: dateFormat,
      scoreFormat: scoreFormat,
      screenfull: screenfull,
      directionRight: true,
      recordEntity: {
        scoreInfo: {}
      },
      recordId: "",
    }
  },
  async mounted() {
    if (getQuery("id")) {
      this.recordId = getQuery("id")
      this.recordEntity = await ScriptRecordModel.getUserOneRecordInfo(this.recordId)
    } else {
      if (ScriptRecordModel.ifNotScriptToJump()) {// 如果在任务中
        await this.completeAllTask()
      }
    }

    let $this = this;
    document.title = "学情分析"
  },
  beforeUnmount() {
    // 解除横竖屏切换监听
    ToolsModel.removeOrientationListen()
  },
  methods: {
    // 请求结束所有任务
    async completeAllTask() {
      this.t = await TaskModel.completeAllTask()
    },
    async clickEndBtn() {
      await ScriptRecordModel.endScript()
    },
    clickExitBtn() {
      // this.$router.push("/myTask")
      window.location.href = "/myTask" // todo 直接跳转避免切换横竖屏错误提示
    }
  }
}
</script>

<style scoped lang="less">
.end-container {
  background-color: rgba(0, 0, 0, 0.6);
  min-height: 100vh;
  padding-bottom: 30px;
}

.info-container {
  margin: 0 auto;
  background-image: url(../../assets/ui/fdldq/end/bg.png);
  background-size: cover;
  background-position: 50% 50%;
  width: 220px;
  height: 210px;
  margin-top: -20px;
  margin-bottom: -20px;
}


.script-title {
  color: #d5553b;
  font-size: 10px;
  margin-top: 8px;
  font-weight: bold;
}

.total-score-info {
  margin-top: 3px;

  .score {
    color: #df3d23;
    font-size: 18px;
    font-weight: bold;
  }

  .text {
    font-weight: bold;
    color: #df3d23;
    display: inline-block;
    margin-bottom: -6px;
  }
}

.total-time {
  margin-top: 3px;

  .title {
    text-align: center;

    img {
      display: inline-block;
      width: 4px;
      height: 5px;
      margin-right: 2px;
    }

    span {
      color: #333;
      font-size: 5px;
    }
  }

  .time {
    margin-top: 2px;
    color: #d83b21;
    font-size: 12px;
    font-weight: bold;
  }
}

.score-info {
  width: 60%;
  margin-top: 3px;

  .score-li {
    .title {
      margin-bottom: 2px;

      img {
        display: inline-block;
        width: 6px;
        height: 6px;
        margin-right: 2px;
      }
      img.clue{
        width:7px;
        height: 6px;
      }
      img.decision{
        width: 5px;
        height: 6px;
      }

      span {
        color: #333;
        font-size: 6px;
      }
    }

    .score {
      color: #d77141;
      font-size: 10px;
      font-weight: bold;
    }
  }
}

.number-info {
  width: 60%;
  margin-top: 3px;

  .number-li {
    .title {
      color: #333;
      font-size: 6px;
      margin-bottom: 2px;
    }

    .number {
      color: #d77141;
      font-size: 10px;
      font-weight: bold;
    }
  }
}

.time-info {
  width: 60%;
  margin-top: 2px;

  .time-li {
    .title {
      color: #d77141;
      font-size: 6px;
    }

    .time {
      color: #d77141;
      font-size: 6px;
    }
  }
}

.exit-btn {
  width: 190px;
  height: 20px;
  line-height: 20px;
  margin: 0 auto;
  margin-top: 5px;
  font-size: 9px;
  border-radius: 10px;
}

.direction-tip {
  line-height: 100vh;
  text-align: center;
  font-size: 25px;
  color: #333;
}
</style>
