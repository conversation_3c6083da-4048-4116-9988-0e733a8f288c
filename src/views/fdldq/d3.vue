<template>
  <div class="page-container" v-show="screenDirection==='landscape'" id="fdldq-d3">
    <div id="three"></div>
    <!--  物品信息框  -->
    <van-overlay :show="desBox.show">
      <div id="des-container">
        <div class="name">{{ modelSelectedDesObject["name"] }}</div>
        <div class="des">{{ modelSelectedDesObject["text"] }}</div>
        <div class="buttons flex flex-around">
          <van-button type="primary" size="small" @click="ThreeMethods().clickDesBoxChooseBtn()">&nbsp;&nbsp;&nbsp;选 择&nbsp;&nbsp;&nbsp;</van-button>
          <van-button type="success" size="small" @click="ThreeMethods().clickDesBoxCancelBtn()">&nbsp;&nbsp;&nbsp;放 弃&nbsp;&nbsp;&nbsp;</van-button>
        </div>
      </div>
    </van-overlay>

    <!--  底部信息框  -->
    <div id="info-container" v-show="!desBox.show">
      <div class="text">您认为红军22勇士会选择哪些武器来夺桥呢？</div>
      <!--      <div class="second"></div>-->
      <div class="buttons flex flex-around">
        <van-button type="success" size="small" @click="ThreeMethods().selectGoodsJudge()">确认选择（{{
            leftSecond
          }}）
        </van-button>
      </div>
    </div>
    <!--  帮助按钮  -->
    <img src="@/assets/ui/fdldq/common/帮助.png" alt="" @click="helpShow=true" class="help-btn"
         v-if="errNumber>=3&&!helpShow">
    <!--  物品信息框  -->
    <van-overlay :show="helpShow">
      <div class="help-container">
        <div class="name">应该选择的物品</div>
        <div class="des">马刀、冲锋枪、毛瑟手枪、木柄手榴弹</div>
        <div class="buttons flex flex-around">
          <van-button type="success" size="small" @click="helpShow=false">&nbsp;&nbsp;&nbsp;关 闭&nbsp;&nbsp;&nbsp;
          </van-button>
        </div>
      </div>
    </van-overlay>
  </div>

  <van-button type="success" class="complete-btn" @click="ThreeMethods().onCompleteTask()" :disabled="clickJumped">
    跳过
  </van-button>
  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
  <div class="full-btn full-btn-landscape" @click="screenfull.toggle()"
       v-if="mobileType!=='iPhone'&&screenDirection==='landscape'">
    <img src="@/assets/ui/fdldq/common/full-btn-icon.png" alt="">
    <span>全屏</span>
  </div>
</template>

<script>
import {msg_err_vant,} from "../../utils/nut_component";
import screenfull from 'screenfull'
import {unity_fdldq_chooseRole} from "../../scripts/fdldq/unityMethods";
import {TaskStore} from "../../store/TaskStore";
import {ScriptRecordModel} from "../../model/ScriptRecordModel";
import {TaskModel} from "../../model/TaskModel";
import {mapState} from "pinia";
import * as THREE from 'three'
import {GLTFLoader} from 'three/examples/jsm/loaders/GLTFLoader'
import {DRACOLoader} from 'three/examples/jsm/loaders/DRACOLoader'
import {CSS2DObject, CSS2DRenderer} from 'three/examples/jsm/renderers/CSS2DRenderer';
import {ThreeModel} from "../../model/ThreeModel";
import {EffectComposer} from "three/examples/jsm/postprocessing/EffectComposer.js"
import {RenderPass} from "three/examples/jsm/postprocessing/RenderPass.js"
import {OutlinePass} from "three/examples/jsm/postprocessing/OutlinePass.js"
import {ShaderPass} from "three/examples/jsm/postprocessing/ShaderPass.js"
import {FXAAShader} from "three/examples/jsm/shaders/FXAAShader.js"
import {OrbitControls} from 'three/examples/jsm/controls/OrbitControls'
import {showLoadingToast} from 'vant';
import $ from 'jquery'
import {showDialog, showFailToast} from 'vant';
import {ToolsModel} from "../../model/ToolsModel";

export default {
  name: "f_d3_View",
  components: {},
  computed: {
    ...mapState(TaskStore, {
      nowTaskId: store => store.nowTaskId,
      recordInfo: store => store.recordInfo,
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull,
      mobileType: store => store.mobileType,
    }),
  },
  watch: {
    // 监听是否全屏
    screenFull(newVal, oldVal) {

    },
    // 监听横竖屏
    screenDirection(newVal, oldVal) {
      if (newVal === "landscape") {// 如果切换为横屏
        // 如果实验已加载就设置分辨率
        if (!this.three.loaded) {
          setTimeout(() => {
            this.ThreeMethods().load()
          }, 1000) // 切换横竖屏后，要等待一段时间才能获取到innerWidth
        }
      }
      // 如果切换为竖屏竖屏，恢复metaViewPort
      if (newVal === "portrait") {
        ToolsModel.resetMetaViewport();
      }
    }
  },
  data() {
    return {
      publicPath: import.meta.env.BASE_URL,
      clickJumped:false,
      taskId: "D3",
      screenfull: screenfull,
      three: {
        INTERSECTED: null, // 暂存射线相交的物体（交互的模型对象）
        mouse: new THREE.Vector2(),
        loaded: false,
      },
      // mesh对象集合
      meshArr: [],
      // 模型对象集合
      modelObjects: {
        "jiQiang": [],
        "dao": [],
        "guo": [],
        "chongFengQiang": [],
        "g38Gun": [],
        "maoSeGun": [],
        "shouLiuDan": [],
        "paiJiPao": [],
      },
      // 模型描述文本
      modelDesObject: {
        "jiQiang": {
          name: "ZB-26轻机枪",
          text: "射速达到550发每分钟，可打击1500米射程内的敌人。从反围剿时期开始，到抗日战争，再到解放战争期间，无论是红军、八路军，还是解放军都大量使用ZB-26轻机枪，其中使用最多的当属在抗日战争期间。"
        },
        "dao": {
          name: "马刀",
          text: "马刀属性：铁制冷兵器，长70厘米，宽8厘米，厚7厘米，净重1101克，具有一定威慑力和杀伤力，适合步兵白刃格斗！红军成立之初，缺乏弹药补给，故马刀大量配备。"
        },
        "guo": {
          name: "大铁锅",
          text: "铁锅有生铁锅和熟铁锅之分，生铁锅是选用灰口铁熔化用模型浇铸制成的，熟铁锅是用黑铁皮锻压或手工捶打制成，具有锅坯薄，传热快的性能。红军长征中用来烧水、做菜、煮饭。"
        },
        "chongFengQiang": {
          name: "冲锋枪",
          text: "在中国MP18和MP28冲锋枪不但从外国进口，也在国内多家兵工厂生产，在当时冲锋枪并不是被称之为冲锋枪而是叫做手提机枪，并且拥有一个中国式的俗称:‘手提花机关枪’，源于它可以如机关枪那般连发射击，以及枪管外套筒上很多散热孔。"
        },
        "g38Gun": {
          name: "38步枪",
          text: "发射方式单发，该枪射程2000米。红军手中的38大盖主要来自自身的装备，广州起义的主力部队装备的步枪全部是38大盖。而缴获的38大盖则主要是西路军西征时缴获的。"
        },
        "maoSeGun": {
          name: "毛瑟手枪",
          text: "发射方式单发，有效射程300米。1931年7至9月的第3次反“围剿”期间，共产国际远东情报组负责人、著名红色谍报员佐尔格巧施妙计，将2万支毛瑟M1898步枪和马枪送达红军手中。"
        },
        "shouLiuDan": {
          name: "木柄手榴弹",
          text: "伤害有效范围在10-15米中国工农红军所有的武器大部分来自于缴获，木柄手榴弹也不例外。"
        },
        "paiJiPao": {
          name: "迫击炮",
          text: "射角一般为45°－85°，弹道弯曲，每分钟最大发射次数10发，很难击中移动目标。生产于1931年的国造82迫击炮重69千克，但可拆分为三个部分，结构简单，运输方便，同时造价低廉，故而成为中国军队的首选。"
        },
      },
      // 当前选中模型信息
      modelSelectedDesObject: {
        goodsId: "",
        name: "",
        text: ""
      },
      // 已经选择的物体
      selectedGoodsIds: [],
      // 展示弹窗
      desBox: {
        show: false
      },
      // 选择倒计时
      leftSecondDefault: 60,// 默认倒计时
      leftSecond: 0,
      errNumber: 0,// 累计错误次数
      helpShow: false,
    }
  },
  async beforeCreate() {

  },
  async mounted() {
    document.title = "D3-选择武器"
    let $this = this;
    // 关卡记录-不允许返回
    // await TaskModel.judgeOneTaskIsCompletedOrJumpBack("D3")

    // 如果是横屏才加载
    if (this.screenDirection === "landscape") {
      await $this.ThreeMethods().load()
    }
  },
  beforeUnmount() {
    this.ThreeMethods().beforeExit()
  },
  beforeRouteLeave() {
    this.ThreeMethods().beforeExit()
  },
  methods: {
    ThreeMethods() {
      let $this = this;
      return {
        // 开始加载
        async load() {
          // 如果是在本页才加载，避免生命周期错误 todo 优化逻辑
          if (window.location.href.indexOf("fdldq/d3") !== -1) {
            showLoadingToast({
              message: '加载中...',
              forbidClick: true,
            });
            $this.three.loaded = true
            await $this.ThreeMethods().init()
          }
        },
        // 初始化
        async init() {
          const width = window.innerWidth, height = window.innerHeight;
          // WebGL渲染器
          const renderer = new THREE.WebGLRenderer()
          renderer.setSize(width, height)
          renderer.setPixelRatio(window.devicePixelRatio)
          renderer.domElement.id = "unity-canvas"
          document.getElementById('three').appendChild(renderer.domElement)

          // 场景
          const scene = new THREE.Scene()
          $this.three.clock = new THREE.Clock()

          // 光
          const ambient = new THREE.AmbientLight(0xffffff, 0.5),
              light1 = new THREE.PointLight(0xffffff, 0.4),
              light2 = new THREE.PointLight(0xffffff, 0.4)
          const hemiLight = new THREE.HemisphereLight(0xffffff, 0x444444)
          scene.add(ambient)
          light1.position.set(200, 300, 400)
          scene.add(light1)
          light2.position.set(-200, -300, -400)
          scene.add(light2)
          scene.add(hemiLight)
          const spotLight = new THREE.SpotLight(0xffffff, 2)
          spotLight.position.set(5, 9, 10)
          scene.add(spotLight)

          // 相机
          const camera = new THREE.PerspectiveCamera(60, width / height, 0.3, 1000)
          camera.position.set(5, 9, 10)
          camera.lookAt(0, 0, 0)
          camera.layers.enableAll();
          camera.layers.toggle(1);

          // 辅助坐标轴
          const axesHelper = new THREE.AxesHelper(100)
          //scene.add(axesHelper)


          // 模型加载-draco
          let dracoLoader = new DRACOLoader()
          dracoLoader.setDecoderPath("js")
          let gltfLoader = new GLTFLoader()
          gltfLoader.setDRACOLoader(dracoLoader)


          // 场景背景-天空盒
          let texture = new THREE.TextureLoader().load($this.publicPath + "textures/fdldq/s1/sky.jpg")
          texture.mapping = THREE.EquirectangularReflectionMapping;
          scene.background = texture;

          // css2D渲染器
          // let css2DRender = new CSS2DRenderer();
          // css2DRender.setSize(width, height)
          // css2DRender.domElement.style.position = 'absolute';
          // // css2DRender.domElement.style.zIndex = -1
          // css2DRender.domElement.style.top = '0px';
          // css2DRender.domElement.style.touchAction = "none"
          // css2DRender.domElement.id = "fdldq_d3_css2DRender"
          // document.body.appendChild(css2DRender.domElement);

          // 加载models
          // $this.ThreeMethods().loadModel_build1(scene)
          $this.ThreeMethods().loadModel_desk(scene)
          $this.ThreeMethods().loadModel_jiQiang(scene)
          $this.ThreeMethods().loadModel_dao(scene)
          $this.ThreeMethods().loadModel_guo(scene)
          $this.ThreeMethods().loadModel_chongFengQiang(scene)
          $this.ThreeMethods().loadModel_38Gun(scene)
          $this.ThreeMethods().loadModel_maoSeGun(scene)
          $this.ThreeMethods().loadModel_shouLiuDan(scene)
          $this.ThreeMethods().loadModel_paiJiPao(scene)

          // 视角控制器
          //const controls = new OrbitControls(camera, renderer.domElement)

          // window全局变量-避免渲染错误
          // window.css2DRender = css2DRender
          window.renderer = renderer
          window.scene = scene
          window.camera = camera
          window.raycaster = new THREE.Raycaster();


          setTimeout(() => {
            $this.ThreeMethods().render()
            // 点击事件监听
            $this.ThreeMethods().listenOnScreenClick()
            // 屏幕尺寸改变监听
            window.addEventListener('resize', $this.ThreeMethods().onWindowResize);
            // 开始倒计时
            $this.leftSecond = $this.leftSecondDefault
            $this.leftSecondInterval = setInterval($this.ThreeMethods().secondCount, 1000);
          }, 500)
        },
        // 加载模型-桥头建筑1
        loadModel_build1(scene) {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/d3/建筑1.glb").then(gltf => {
            let model = gltf.scene
            model.traverse(async function (object) {
              if (object.isMesh) {
                object.material = new THREE.MeshStandardMaterial({
                  map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/建筑1.png")
                })
              }
            })
            model.scale.set(1500, 1500, 1500)
            model.position.set(-5, -5, -20)
            model.rotation.set(Math.PI / 30, Math.PI, 0)
            scene.add(model);
          })
        },
        // 加载模型-桌子
        loadModel_desk(scene) {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/d3/桌子.glb").then(gltf => {
            let model = gltf.scene
            model.scale.set(100, 100, 100)
            model.rotation.set(0, Math.PI / 1.55, Math.PI / 20)
            model.position.set(1.2, 0, 2)
            scene.add(model);
          })
        },
        // 加载模型-机枪
        loadModel_jiQiang(scene) {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/d3/机枪.glb").then(gltf => {
            let model = gltf.scene
            model.traverse(async function (object) {
              if (object.isMesh) {
                object.goodsId = "jiQiang"
                $this.modelObjects["jiQiang"].push(object)
                // console.log(object)
                if (object.name === "zb_26_1") {// todo 修改金属颜色
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/机枪_金属.png")
                  })
                  object.material.metalness = 0.6
                  object.material.roughness = 1
                }
                if (object.name === "zb_26_2") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/机枪_木头.png")
                  })
                }
              }
            })
            model.goodsId = "jiQiang"
            model.scale.set(1, 1, 1)
            model.rotation.set(0, Math.PI * 2 - 0.5, 0)
            model.position.set(-12.5, 3, 4.5)
            scene.add(model);
            $this.meshArr.push(model)
          })
        },
        // 加载模型-刀
        loadModel_dao(scene) {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/d3/刀.glb").then(gltf => {
            let model = gltf.scene
            model.traverse(async function (object) {
              if (object.isMesh) {
                object.goodsId = "dao"
                $this.modelObjects["dao"].push(object)
                object.material = new THREE.MeshStandardMaterial({
                  map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/刀.png")
                })
              }
            })
            model.goodsId = "dao"
            model.scale.set(2.2, 2.2, 2.2)
            model.rotation.set(0.1, Math.PI + 0.5, -Math.PI / 2 + 0.2)
            model.position.set(-4, 5, 4.5)
            scene.add(model);
            $this.meshArr.push(model)
          })
        },
        // 加载模型-锅和锅盖
        loadModel_guo(scene) {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/d3/锅.glb").then(gltf => {
            let model = gltf.scene
            model.traverse(async function (object) {
              if (object.isMesh) {
                object.goodsId = "guo"
                $this.modelObjects["guo"].push(object)
                if (object.name === "tieguo1") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/锅.png")
                  })
                }
                if (object.name === "guogai") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/锅盖.jpg")
                  })
                  object.position.set(-0.2, 0.1, -0.2)
                  object.rotation.set(-0.3, 0, 0)
                }
              }
            })
            model.goodsId = "guo"
            model.scale.set(5, 5, 5)
            model.rotation.set(-0.2, 0, 0)
            model.position.set(-0, 4, 4.5)
            scene.add(model);
            $this.meshArr.push(model)
          })
        },
        // 加载模型-冲锋枪
        loadModel_chongFengQiang(scene) {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/d3/冲锋枪.glb").then(gltf => {
            let model = gltf.scene
            model.traverse(async function (object) {
              if (object.isMesh) {
                object.goodsId = "chongFengQiang"
                $this.modelObjects["chongFengQiang"].push(object)
                if (object.name === "mp18") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/冲锋枪.png")
                  })
                }
              }
            })
            model.goodsId = "chongFengQiang"
            model.scale.set(80, 80, 80)
            model.rotation.set(0, Math.PI - 1, 0)
            model.position.set(2.5, 3.67, 2.5)
            scene.add(model);
            $this.meshArr.push(model)
          })
        },
        // 加载模型-38步枪
        loadModel_38Gun(scene) {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/d3/38步枪.glb").then(gltf => {
            let model = gltf.scene
            model.traverse(async function (object) {
              if (object.isMesh) {
                object.goodsId = "g38Gun"
                $this.modelObjects["g38Gun"].push(object)
                if (object.name === "polySurface2_1") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/38步枪2.png")
                  })
                }
                if (object.name === "polySurface2_2") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/38步枪1.png")
                  })
                }
              }
            })
            model.goodsId = "g38Gun"
            model.scale.set(16, 16, 16)
            model.rotation.set(0, Math.PI + 0.4, -1.5)
            model.position.set(4.5, 4.9, 4.5)
            scene.add(model);
            $this.meshArr.push(model)
          })
        },
        // 加载模型-毛瑟步枪
        loadModel_maoSeGun(scene) {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/d3/毛瑟步枪.glb").then(gltf => {
            let model = gltf.scene
            model.traverse(async function (object) {
              if (object.isMesh) {
                object.goodsId = "maoSeGun"
                $this.modelObjects["maoSeGun"].push(object)
                if (object.name === "maoseshouqiang_1") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/毛瑟步枪1.png")
                  })
                }
                object.material.metalness = 0.5
                object.material.roughness = 1
                if (object.name === "maoseshouqiang_2") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/手榴弹1.png")
                  })
                  object.material.metalness = 0
                  object.material.roughness = 1
                }
              }
            })
            model.goodsId = "maoSeGun"
            model.scale.set(10, 10, 10)
            model.rotation.set(0, Math.PI + 0.4, -1.5)
            model.position.set(6.5, 4.9, 3)
            scene.add(model);
            $this.meshArr.push(model)
          })
        },
        // 加载模型-手榴弹
        loadModel_shouLiuDan(scene) {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/d3/手榴弹.glb").then(gltf => {
            let model = gltf.scene
            model.traverse(async function (object) {
              if (object.isMesh) {
                object.goodsId = "shouLiuDan"
                $this.modelObjects["shouLiuDan"].push(object)
                if (object.name === "muzhi_shouliudan_1") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/手榴弹2.png")
                  })
                }
                if (object.name === "muzhi_shouliudan_2") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/手榴弹1.png")
                  })
                }
              }
            })
            model.goodsId = "shouLiuDan"
            model.scale.set(23, 23, 23)
            model.rotation.set(0.2, -Math.PI / 2 + 0.4, -1.5)
            model.position.set(7.5, 4.9, 2.5)
            scene.add(model);
            $this.meshArr.push(model)
          })
        },
        // 加载模型-迫击炮
        loadModel_paiJiPao(scene) {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/d3/迫击炮.glb").then(gltf => {
            let model = gltf.scene
            model.traverse(async function (object) {
              if (object.isMesh) {
                object.goodsId = "paiJiPao"
                $this.modelObjects["paiJiPao"].push(object)
                if (object.name === "paijipao_1") {// 炮头 todo 调亮一点
                  object.material = new THREE.MeshStandardMaterial({
                    color: "#78ab73"
                  })
                  object.material.metalness = 0.7
                  object.material.roughness = 0.2
                }
                if (object.name === "paijipao_4") {
                  object.material = new THREE.MeshStandardMaterial({
                    color: "#78ab73"
                  })
                  object.material.metalness = 0.7
                  object.material.roughness = 0.2
                }
                if (object.name === "paijipao_2") {// 炮身
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/迫击炮2.png")
                  })
                }
                if (object.name === "paijipao_3") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/d3/迫击炮1.png")
                  })
                }
              }
            })
            model.goodsId = "paiJiPao"
            model.scale.set(6.5, 6.5, 6.5)
            model.rotation.set(0, -Math.PI / 2 + 0.4, 0)
            model.position.set(8.2, 4.9, 0.5)
            scene.add(model);
            $this.meshArr.push(model)
          })
        },
        // 渲染器
        render() {
          let delta = $this.three.clock.getDelta()
          window.renderId = requestAnimationFrame($this.ThreeMethods().render)
          window.renderer.render(window.scene, window.camera)
          //window.css2DRender.render(window.scene, window.camera);
          if (window.composer) {
            window.composer.render()
          }
        },
        // 开始监听屏幕点击事件
        listenOnScreenClick: function () {
          window.screenClickListenFn = (e) => {
            $this.ThreeMethods().onScreenClick(e);
          }
          window.addEventListener('click', window.screenClickListenFn);
        },
        // 屏幕点击事件
        onScreenClick: function (event) {
          // 监听是全window监听，为了避免射线穿透html面板，这儿做下判断，只监听canvas的点击
          let targetDom = event.target
          if (targetDom.id !== 'unity-canvas') {
            return
          }
          //event.preventDefault();
          // 将鼠标位置归一化为设备坐标。x 和 y 方向的取值范围是 (-1 to +1)
          // renderer为three的渲染器
          let px = window.renderer.domElement.getBoundingClientRect().left;
          let py = window.renderer.domElement.getBoundingClientRect().top;
          $this.three.mouse.x = ((event.clientX - px) / (window.renderer.domElement.offsetWidth)) * 2 - 1;
          $this.three.mouse.y = -((event.clientY - py) / (window.renderer.domElement.offsetHeight)) * 2 + 1;
          // 通过摄像机和鼠标位置更新射线
          window.raycaster.setFromCamera($this.three.mouse, window.camera);
          // 计算物体和射线的焦点
          let intersects = window.raycaster.intersectObjects($this.meshArr, true);
          if (intersects.length > 0) {
            let goodsId = intersects[0].object.goodsId

            // 如果model已经隐藏就不响应点击事件
            let [findKey, findObj] = ThreeModel.findObjectFromArrByNameAndVal(window.scene.children, "goodsId", goodsId)
            if (window.scene.children[findKey].visible === false) {
              return;
            }

            $this.ThreeMethods().chooseOneGoods(goodsId)
          } else {
          }
        },
        // 选择了某个物体
        chooseOneGoods(goodsId) {
          $this.desBox.show = true
          $this.modelSelectedDesObject = $this.modelDesObject[goodsId]
          $this.modelSelectedDesObject.goodsId = goodsId
        },
        // 点击物体介绍弹窗的选择按钮
        clickDesBoxChooseBtn() {
          let goodsId = $this.modelSelectedDesObject.goodsId
          if ($this.selectedGoodsIds.indexOf(goodsId) === -1) {
            $this.selectedGoodsIds.push(goodsId)
          }
          // 找到模型并隐藏
          let [findKey, findObj] = ThreeModel.findObjectFromArrByNameAndVal(window.scene.children, "goodsId", goodsId)
          window.scene.children[findKey].visible = false
          // 隐藏面板
          $this.desBox.show = false
        },
        // 点击物体介绍弹窗的放弃按钮
        clickDesBoxCancelBtn() {
          let goodsId = $this.modelSelectedDesObject.goodsId
          $this.desBox.show = false
        },
        // 选择角色高亮
        outLineGoods(goodsId) {
          let outLine = false
          if ($this.mobileType !== "iPhone") { // iphone手机不显示轮廓 避免崩溃
            outLine = true
          }
          // todo outLine
          $this.ThreeMethods().outlineObj($this.modelObjects[goodsId])
        },
        // 高亮显示模型
        outlineObj(selectedObjects) {
          // 创建一个EffectComposer（效果组合器）对象，然后在该对象上添加后期处理通道。
          let composer = new EffectComposer(window.renderer)
          // 新建一个场景通道  为了覆盖到原理来的场景上
          let renderPass = new RenderPass(window.scene, window.camera)
          composer.addPass(renderPass);
          // 物体边缘发光通道
          let outlinePass = new OutlinePass(new THREE.Vector2(window.innerWidth, window.innerHeight), window.scene, window.camera, selectedObjects)
          outlinePass.selectedObjects = selectedObjects
          outlinePass.edgeStrength = 15.0 // 边框的亮度
          outlinePass.edgeGlow = 2// 光晕[0,1]
          outlinePass.usePatternTexture = false // 是否使用父级的材质
          outlinePass.edgeThickness = 2.0 // 边框宽度
          outlinePass.downSampleRatio = 1 // 边框弯曲度
          outlinePass.visibleEdgeColor.set(parseInt(0xff0000)) // 呼吸显示的颜色
          outlinePass.clear = true
          composer.addPass(outlinePass)
          // 自定义的着色器通道 作为参数
          let effectFXAA = new ShaderPass(FXAAShader)
          effectFXAA.uniforms.resolution.value.set(0.25 / window.innerWidth, 0.25 / window.innerHeight) // 决定选择后屏幕清晰度，1会变模糊
          effectFXAA.renderToScreen = true
          composer.addPass(effectFXAA)
          window.composer = composer
        },
        // 计时倒计时
        secondCount() {
          if ($this.leftSecond >= 1) {
            $this.leftSecond--
            if ($this.leftSecond === 0) {// 倒计时结束
              $this.ThreeMethods().selectGoodsJudge()
            }
          }
        },
        // 选择物体判断
        async selectGoodsJudge() {
          let result = true
          let rightGoods = ["dao", "chongFengQiang", "maoSeGun", "shouLiuDan"] // 正确的答案
          $this.selectedGoodsIds.length !== rightGoods.length ? result = false : result = true // 答案数量不正确
          rightGoods.forEach((goodsId) => {
            if ($this.selectedGoodsIds.indexOf(goodsId) === -1) {
              result = false
            }
          })
          console.log(result)
          if (!result) {
            showDialog({
              message: '选择错误！任务失败！',
              theme: 'round-button',
            }).then(() => {
              // 记录错误次数，错误次数超过3次显示提示按钮
              $this.errNumber++
              // 恢复初始化
              // 将隐藏的问题显示
              $this.selectedGoodsIds.forEach(goodsId => {
                let [findKey, findObj] = ThreeModel.findObjectFromArrByNameAndVal(window.scene.children, "goodsId", goodsId)
                window.scene.children[findKey].visible = true
              })
              // 选择清空
              $this.selectedGoodsIds = []
              // 重置倒计时
              $this.leftSecond = $this.leftSecondDefault
            });
          }
          if (result) {// 选择正确
            // 完成任务
            if (await TaskModel.completeOneTask($this.nowTaskId)) {
              // 跳转到主界面
              // $this.$router.push({
              //   name: "AR_Main_View",
              //   query: {}
              // })
              window.location.href = "/ar_main"
            }
          }
        },
        // 当屏幕尺寸改变
        onWindowResize() {
          window.renderer.setSize(window.innerWidth, window.innerHeight)
          // window.css2DRender.setSize(window.innerWidth, window.innerHeight)
          window.camera.aspect = window.innerWidth / window.innerHeight;
          window.camera.updateProjectionMatrix();
        },
        //退出前清理
        beforeExit() {
          if (!window.scene) {
            return
          }
          cancelAnimationFrame(window.renderId);// Stop the animation
          // 消除threejs和全局变量
          if (document.querySelector("#fdldq_d3_css2DRender")) {
            document.querySelector("#fdldq_d3_css2DRender").remove()
          }
          delete window.scene
          delete window.renderer
          delete window.scene
          delete window.camera
          delete window.raycaster
          delete window.mesh1
          delete window.mesh2
          delete window.composer
          delete window.renderId
          window.removeEventListener('click', window.screenClickListenFn);
          // delete window.css2DRender
          delete window.screenClickListenFn
          clearInterval($this.leftSecondInterval)
          console.log("清理完成")
        },
        // 完成任务
        async onCompleteTask() {
          $this.clickJumped = true
          if (await TaskModel.completeOneTask($this.nowTaskId)) {
            // 重置meta viewport
            ToolsModel.resetMetaViewport();

            // 跳转到主界面
            // $this.$router.push({
            //   name: "AR_Main_View",
            //   query: {}
            // })
            window.location.href = "/ar_main"
          }
        },
      }
    },
  }
}
</script>
<style>

</style>
<style scoped lang="less">
.complete-btn {
  position: absolute;
  top: 0px;
  right: 0px;
  z-index: 3;
}

#info-container {
  position: fixed;
  bottom: 2px;
  width: 155px;
  left: 110px;
  z-index: 2;
  color: #222;
  background-color: #fff;
  padding: 5px;
  border-radius: 5px;
  padding-top: 8px;

  .text {
    font-size: 7px;
    text-align: center;
  }

  .second {
    font-size: 8px;
    margin-top: 5px;
    text-align: center;
    color: red;
  }

  .buttons {
    margin-top: 5px;
  }
}

#des-container, .help-container {
  position: fixed;
  top: 20px;
  width: 200px;
  left: 87.5px;
  z-index: 2;
  color: #222;
  background-color: #fff;
  padding: 10px 10px;
  border-radius: 5px;

  .name {
    text-align: center;
    font-size: 8px;
    font-weight: bold;
  }

  .des {
    margin-top: 7px;
    text-indent: 2em;
    font-size: 6.5px;
    line-height: 10px;
    color: #444;
  }

  .buttons {
    margin-top: 10px;
  }
}

.help-container {
  top: 10px;
  width: 150px;
  left: 112.5px;
}

.help-btn {
  width: 20px;
  height: 20px;
  position: fixed;
  left: 175px;
  top: 7px;
  z-index: 2;
}

.page-container {
  background-color: rgba(0, 0, 0, 0.6);
  min-height: 100vh;
  color: #fff;

  .task-title {
    text-align: center;
    font-size: 10px;
    color: #fff;
  }
}
</style>
