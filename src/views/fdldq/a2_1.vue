<template>
  <div class="page-container" v-show="screenDirection==='landscape'" id="fdldq-a2_1">
<!--    <div class="title">{{ taskConfig["taskId"] }}-{{ taskConfig["name"] }}</div>-->
    <ar-select-panel :questionInfo="selectQuestionInfo" @onCompleteOneSelect="onCompleteOneSelect"
                     @onGetOneClue="onGetOneClue"></ar-select-panel>
  </div>
  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
  <div class="full-btn full-btn-landscape" @click="screenfull.toggle()" v-if="mobileType!=='iPhone'&&screenDirection==='landscape'">
    <img src="@/assets/ui/fdldq/common/full-btn-icon.png" alt="">
    <span>全屏</span>
  </div>
</template>

<script>
import {msg_err, msg_err_vant, msg_success_vant, toast_err} from "../../utils/nut_component";
import {Button} from 'vant';
import {ref, onBeforeUpdate} from 'vue';
import screenfull from 'screenfull'
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";
import {TaskModel} from "../../model/TaskModel";
import {
  unity_fdldq_completeOneSelect,
  unity_fdldq_completeOneTask,
  unity_fdldq_getOneClue
} from "../../scripts/fdldq/unityMethods";
import arSelectPanel from "@/views/components/scene/ar-selectPanel.vue"
import {ToolsModel} from "../../model/ToolsModel";
import {ArCameraModel} from "../../model/easyAR/ArCameraModel";

export default {
  name: "f_a2_1_View",
  components: {arSelectPanel},
  computed: {
    ...mapState(TaskStore, {
      nowTaskId: store => store.nowTaskId,
      recordInfo: store => store.recordInfo,
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull,
      mobileType: store => store.mobileType,
    }),
  },
  watch: {
    // 监听是否全屏
    screenFull(newVal, oldVal) {
      // 切换video猖狂
      this.arCameraModel.videoElement.setAttribute('width', `${window.innerWidth}px`);
      this.arCameraModel.videoElement.setAttribute('height', `${window.innerHeight}px`);
    },
    // 监听横竖屏
    screenDirection(newVal, oldVal) {
      if (newVal === "landscape") {// 如果切换为横屏
        if (!this.arCameraModel) {// 如果尚未设置宽高
          this.arCameraModel = new ArCameraModel("fdldq-a2_1")
          this.arCameraModel.openCameraInPage()
        }
      }
    }
  },
  data() {
    return {
      arCameraModel:undefined,
      screenfull: screenfull,
      directionRight: true,
      taskId: "A2",
      taskConfig: {},
      taskInfo: {},
      selectQuestionInfo: {
        clueList: [],
        answer: [],
        userAnswer: []
      }
    }
  },
  async mounted() {
    // 关卡记录-不允许返回
    TaskModel.judgeOneTaskIsCompletedOrJumpBack(this.taskId)

    // 如果加载时是横屏
    if (this.screenDirection === "landscape") {
      this.arCameraModel = new ArCameraModel("fdldq-a2_1")
      this.arCameraModel.openCameraInPage()
    }

    // 关卡信息初始化
    this.taskConfig = TaskModel.getOneTaskConfig(this.taskId)
    this.taskInfo = this.recordInfo[this.nowTaskId]

    // 子任务检查
    let childTaskIndex = this.taskInfo["childTaskIndex"]
    if (childTaskIndex === 1) {
      this.$router.push({
        name: "f_a2_2_View",
        query: {}
      })
    }
    if (childTaskIndex === 2) {
      this.$router.push({
        name: "f_a2_3_View",
        query: {}
      })
    }

    let selectQuestionInfo = this.taskConfig['childTaskInfo']['lists'][0]["selectQuestionList"][0]
    selectQuestionInfo["index"] = 0 // 该选择题在该任务中的序号，一个任务中可以存在多个选择题
    selectQuestionInfo["userAnswer"] = []
    selectQuestionInfo["userClueList"] = []
    this.selectQuestionInfo = selectQuestionInfo

    let $this = this;
    document.title = this.taskConfig["taskId"] + this.taskConfig["name"]

    this.regUnityMethods()
  },
  methods: {
    // 注册unity全局方法
    regUnityMethods() {
      unity_fdldq_completeOneTask(this)
      unity_fdldq_completeOneSelect(this)
      unity_fdldq_getOneClue(this)
    },

    // 点击完成任务按钮
    async clickCompleteTaskBtn() {
      this.completeTask()
    },
    /**
     * 监听-选择题完成信息
     * @param answer 选择的答案
     * @param selectIndex 选择题编号
     * @param taskId 任务编号，可以由网页内或unity传入
     * @returns {Promise<void>}
     */
    async onCompleteOneSelect(answer, selectIndex, taskId) {
      if (!taskId) {
        taskId = this.nowTaskId
      }
      // todo 选择题决策接口
      if (await TaskModel.completeOneSelect(taskId, answer, selectIndex)) {
        await this.completeTask(); // 完成任务
      }
      return true
    },
    /**
     * 监听-获取一个线索
     * @param clue 线索文字内容
     * @param clueIndex 线索序号
     * @param selectIndex 所属的选择题序号
     * @param taskId 任务编号，可以由网页内或unity传入
     * @returns {Promise<void>}
     */
    async onGetOneClue(clue, clueIndex, selectIndex, taskId) {
      if (!taskId) {
        taskId = this.nowTaskId
      }
      let result = await TaskModel.getOneClue(taskId, clue, clueIndex, selectIndex)
      if (result === true) {
        msg_success_vant("您已成功获取一个线索")
        // todo unity传入返回成功信息
      }
    },
    // 完成任务
    async completeTask() {
      if (await TaskModel.markTaskDoing_completeOneChildTask(this.nowTaskId, 0)) {
        // 跳转到主界面
        this.$router.push({
          name: "f_a2_2_View",
          query: {}
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
.page-container {
  background-color: rgba(0, 0, 0, 0.6);
  min-height: 100vh;
  padding: 10px;
}

.title {
  text-align: center;
  font-size: 10px;
}

.buttons {
  margin-top: 50px;
}
</style>
