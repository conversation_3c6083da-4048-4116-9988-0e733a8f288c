<template>
  <div class="webgl-content" v-show="screenDirection==='landscape'">
    <!--    <div id="unityContainer" style="position: absolute; left: 50%; top: 50%; -webkit-transform: translate(-50%, -50%); transform: translate(-50%, -50%);width: 100%; height: 100%;">-->
    <div id="unityContainer">
      <canvas id="unity-canvas" tabindex="-1"></canvas>
    </div>
    <div id="unity-progress">
      <div id="scene-text"></div>
      <div id="progress-text"></div>
    </div>
    <!--    -->
    <div id="video-container">
      <div id="mui-player"></div>
    </div>
    <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
    <div class="full-btn full-btn-landscape"
         v-if="webglInfo.loaded&&mobileType!=='iPhone'&&screenDirection==='landscape'" @click="toggleFull">
      <img src="@/assets/ui/fdldq/common/full-btn-icon.png" alt="">
      <span>全屏</span>
    </div>
    <van-button type="success" class="complete-btn" @click="onCompleteTask" :disabled="clickJumped" v-if="webglInfo.loaded&&nowTaskId!=='S1'">
      跳过
    </van-button>
  </div>
  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
</template>

<script>
/**
 *
 */


import {msg_err_vant, msg_success_vant, toast_text} from "../../utils/nut_component";
import screenfull from 'screenfull'
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";
import {TaskModel} from "../../model/TaskModel";
import {
  unity_fdldq_afterPlayVideo,
  unity_fdldq_beforePlayVideo,
  unity_fdldq_chooseRole,
  unity_fdldq_completeOneSelect,
  unity_fdldq_completeOneTask, unity_fdldq_getOneClue, unity_fdldq_loadedScene, unity_fdldq_setScene
} from "../../scripts/fdldq/unityMethods";
import {ScriptRecordModel} from "../../model/ScriptRecordModel";
import {UnityModel} from "../../model/unity/UnityModel";
import MuiPlayer from "mui-player";
import $ from "jquery"
import {ToolsModel} from "../../model/ToolsModel";

export default {
  name: "f_common2022_View",
  components: {},
  computed: {
    ...mapState(TaskStore, {
      nowTaskId: store => store.nowTaskId,
      recordInfo: store => store.recordInfo,
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull,
      mobileType: store => store.mobileType,
    }),
  },
  data() {
    return {
      clickJumped: false,
      screenfull: screenfull,
      sceneStringArr: {
        S1: "A0,0,0",
        A1: "A1,0,1",
        B1: "B1,1,0",
        B2: "B2,1,1",
        B3: "B3,1,2",
        C1: "C1,2,0",
        C2: "C2,2,1",
        C3: "C3,2,2",
        D1: "D1,3,0",
        D2: "D2,3,1",
        D3: "D3,3,2",
        D4: "D4,3,3",
        D5: "D5,3,4",
      },
      taskConfig: {},
      taskInfo: {},
      webglInfo: {
        screenWidth: false,
        screenHeight: false,
        width: false,
        height: false,
        loaded: false,
        unityPath: ""
      },
      videoInfo: {},
    }
  },
  watch: {
    // 监听是否全屏
    screenFull(newVal, oldVal) {
      // this.toggleWebglShow();
    },
    // 监听横竖屏
    screenDirection(newVal, oldVal) {
      if (newVal === "landscape") {// 如果切换为横屏
        if (!this.webglInfo.width) {// 如果尚未设置宽高
          setTimeout(() => {
            this.saveWidthAndHeight()
            this.loadWebgl()
          }, 1000) // 切换横竖屏后，要等待一段时间才能获取到innerWidth
        }
        // 如果实验已加载就设置分辨率
        if (this.webglInfo.loaded) {
          // this.toggleWebglShow();
        }
      }
      // 如果切换为竖屏竖屏，恢复metaViewPort
      if (newVal === "portrait") {
        ToolsModel.resetMetaViewport();
      }
    }
  },
  async mounted() {
    // 注册unity全局方法
    this.regUnityMethods()
    // 关卡记录-不允许返回
    TaskModel.judgeOneTaskIsCompletedOrJumpBack(this.nowTaskId)

    // 关卡信息初始化
    this.taskConfig = TaskModel.getOneTaskConfig(this.nowTaskId)
    this.taskInfo = this.recordInfo[this.nowTaskId]

    let $this = this;
    document.title = this.taskConfig["taskId"] + "-" + this.taskConfig["name"]

    // 如果加载时是横屏
    if (this.screenDirection === "landscape") {
      this.saveWidthAndHeight()
      // 加载实验
      this.loadWebgl()
    }
  },
  methods: {
    // 注册unity全局方法
    regUnityMethods() {
      unity_fdldq_setScene(this, this.sceneStringArr[this.nowTaskId])
      unity_fdldq_loadedScene(this, this.nowTaskId)
      unity_fdldq_chooseRole(this)
      unity_fdldq_completeOneTask(this)
      unity_fdldq_completeOneSelect(this)
      unity_fdldq_getOneClue(this)
      unity_fdldq_beforePlayVideo(this)
      unity_fdldq_afterPlayVideo(this)
    },
    // 切换全屏
    toggleFull() {
      unityInstance.SetFullscreen(1);
      // if (screenfull.isEnabled) {
      // screenfull.toggle();
      // 只将canvas全屏，退出实验后会自动退出全屏
      // screenfull.request(document.getElementById("unity-canvas"), {navigationUI: 'hide'});
      // }
    },
    // 错误提示
    showWebglMsg(msg, type) {
      console.log(msg, type)
    },
    // 加载unity webgl
    loadWebgl() {
      let $this = this;
      let unityPath = ""
      if (navigator.userAgent.toLowerCase().indexOf('micromessenger') !== -1) {
        unityPath = "https://resouce.cdzyhd.com/experiment/webgl/arjbs/fdldq/2022122103/exp/"
      } else {
        if (this.mobileType === 'iPhone') {// iphone加载视频浏览器播放版本
          unityPath = "https://resouce.cdzyhd.com/experiment/webgl/arjbs/fdldq/2022122102/browser/"
        } else {// 其他加载视频实验内播放版本
          unityPath = "https://resouce.cdzyhd.com/experiment/webgl/arjbs/fdldq/2022122103/exp/"
        }
      }
      this.webglInfo.unityPath = unityPath
      let config = {
        dataUrl: unityPath + "Build/HSAR_FDLDQ_Webgl.data.unityweb",
        frameworkUrl: unityPath + "Build/HSAR_FDLDQ_Webgl.framework.js.unityweb",
        codeUrl: unityPath + "Build/HSAR_FDLDQ_Webgl.wasm.unityweb",
        streamingAssetsUrl: unityPath + "/StreamingAssets",
        companyName: "DefaultCompany",
        productName: "FDLDQ_Webgl",
        productVersion: "0.1",
        showBanner: this.showWebglMsg,
        // matchWebGLToCanvasSize: false,
      };
      config.devicePixelRatio = 1.5; // 设置物理分辨率缩放

      let canvas = document.getElementById("unity-canvas")
      let width = this.webglInfo.width
      let height = this.webglInfo.height
      canvas.width = Math.round(width);
      canvas.height = Math.round(height);
      canvas.style.width = width + "px"
      canvas.style.height = height + "px"
      UnityModel.loadWebGlResource2022(unityPath + "Build/HSAR_FDLDQ_Webgl.loader.js").then(loaded => {
        if (loaded) {
          window.createUnityInstance(canvas, config, (progress) => {
            console.log("速度", progress)
            document.getElementById("scene-text").innerText = $this.taskConfig["taskId"] + "-" + $this.taskConfig["name"]
            document.getElementById("progress-text").innerText = "加载中，请稍后 " + Math.floor(progress * 100) + "%"
            if (progress === 1) {
              document.getElementById("unity-progress").style.display = "none"
            }
          }).then((unityInstance) => {
            window.unityInstance = unityInstance
          }).catch((message) => {
            alert(message);
          });
        }
      })
    },
// 切换显示分辨率
    toggleWebglShow() {
      if (this.screenFull) {
        // todo 全屏时webgl显示清晰
        console.log("全屏显示")
        this.unitySizeFixedOnFull()
      } else {
        console.log("非全屏显示")
        this.unitySizeFixed(2000, true)
      }
    },
    // 调整unity webgl尺寸，解决模糊和显示问题
    unitySizeFixed(timeOut, plus) {
      let width = this.webglInfo.width
      let height = this.webglInfo.height
      if (plus) {
        width = width * 1.5 // 乘以屏幕缩放率
        height = height * 1.5
      }
      let metaViewport = document.getElementById("viewport")
      if (metaViewport) {
        // metaViewport.parentNode.removeChild(metaViewport)
        metaViewport.setAttribute('content', 'width=' + width + "");// todo 移动端能否正常缩放清晰看这个viewport和webgl的viewport
      }
      let canvas = document.getElementById("unity-canvas")
      canvas.width = Math.round(width);
      canvas.height = Math.round(height);
      let gl = canvas.getContext("webgl2");
      window.gl = gl
      gl.viewport(0, 0, gl.drawingBufferWidth, gl.drawingBufferHeight); // 这么没什么作用？？
      document.getElementById("unity-canvas").style.width = width + "px"
      document.getElementById("unity-canvas").style.height = height + "px"
      setTimeout(() => {

      }, timeOut ? timeOut : 3000)
    },

    // 调整unity webgl尺寸，解决显示问题--全屏时 todo 也显示清晰
    /**
     * 全屏时width设置为0，后面再放大，可以显示未经缩放的webgl图片
     * 好像全屏时直接设置canvas style的width和height就可以了显示一倍不清晰webgl
     * 全屏所有和全屏canvas试下
     */
    unitySizeFixedOnFull() {
      let width = this.webglInfo.screenWidth
      let height = this.webglInfo.screenHeight
      let metaViewport = document.getElementById("viewport")
      if (metaViewport) {
        // metaViewport.parentNode.removeChild(metaViewport)
        metaViewport.setAttribute('content', 'width=' + width);
      }
      let canvas = document.getElementById("unity-canvas")
      canvas.width = Math.round(width);
      canvas.height = Math.round(height);
      let gl = canvas.getContext("webgl2");
      window.gl = gl
      gl.viewport(0, 0, gl.drawingBufferWidth, gl.drawingBufferHeight);
      document.getElementById("unity-canvas").style.width = width + "px"
      document.getElementById("unity-canvas").style.height = height + "px"
      setTimeout(() => {

      }, 3000)
    },

    initVideo() {
      $("#video-container").show()
      let $this = this
      let height = 0
      if (this.screenFull) {
        height = this.webglInfo.screenHeight
      } else {
        height = this.webglInfo.height
      }
      let mp = new MuiPlayer({
        container: '#mui-player',
        // height: window.screen.height,// todo iphone适配
        height: height,
        autoFit: false,
        autoplay: true,
        // initFullFixed:true,
        videoAttribute: [
          {attrKey: 'webkit-playsinline', attrValue: 'webkit-playsinline'},
          {attrKey: 'playsinline', attrValue: 'playsinline'},
          {attrKey: 'x5-video-player-type', attrValue: 'h5-page'},
        ],
        src: this.videoInfo.url,
      })
      console.log(mp)
      mp.video().onended = () => {
        this.onAfterPlayVideoCallBack()
      };
      this.videoInfo.mp = mp
    },

    /**
     * 监听-设置场景
     */
    onSetScene() {

    },

    /**
     * 监听-完成场景加载
     */
    onLoadedScene(taskId) {
      console.log("加载完场景")
      if (!this.webglInfo.loaded) { // 已加载了，就不再次改变分辨率
        // this.toggleWebglShow();
        this.webglInfo.loaded = true
      }
    },

    // 保存横屏屏幕和视窗大小
    saveWidthAndHeight() {
      // 保存屏幕和操作区域信息
      console.log("获取宽高")
      this.webglInfo.width = window.innerWidth
      this.webglInfo.height = window.innerHeight
      this.webglInfo.screenWidth = window.screen.width
      this.webglInfo.screenHeight = window.screen.height
    },

    /**
     * 监听-播放视频前
     */
    onBeforePlayVideo(videoName, videoIndex) {
      console.log("开始播放视频")
      this.videoInfo.url = this.webglInfo.unityPath + "/StreamingAssets/Video/" + videoName + ".mp4"
      $("#unityContainer").hide()
      this.initVideo()
    },
    /**
     * 监听-播放视频后
     */
    onAfterPlayVideo() {
      console.log("结束播放视频")
    },

    /**
     * 监听-视频播放完成后回调
     */
    onAfterPlayVideoCallBack() {
      $("#video-container").hide()
      this.videoInfo.mp.destroy();
      $("#unityContainer").show()
      $("#unityContainer").focus()
      $("unity-canvas").focus();
      window.unityInstance.SendMessage("CallBack", "Js_fdldq_playVideoEnd_callBack", JSON.stringify({
        code: "000000",
        msg: "播放视频完成",
        data: ""
      }));
    },

    /**
     * 监听-选择角色
     * @param roleId 角色id r1 r2
     * @returns {Promise<void>}
     */
    async onChooseRole(roleId) {
      if (roleId === "r1" || roleId === "r2") {
        let taskStore = TaskStore()
        taskStore.$patch({
          roleId: roleId
        })
        // 保存角色选择结果到后端
        if (!(await ScriptRecordModel.updateRecordInfo("roleId", roleId))) {
          return
        }
        // 完成该关
        if (!(await TaskModel.completeOneTask("S1"))) {
          return
        }
        // 重置meta viewport
        ToolsModel.resetMetaViewport();
        // 跳转到主界面
        // this.$router.push({
        //   name: "AR_Main_View",
        //   query: {}
        // })
        window.location.href = "/ar_main"
      } else {
        msg_err_vant("角色信息错误!")
      }
    },
    /**
     * 监听-选择题完成信息
     * @param answer 选择的答案
     * @param selectIndex 选择题编号
     * @param taskId 任务编号，可以由网页内或unity传入
     * @returns {Promise<void>}
     */
    async onCompleteOneSelect(answer, selectIndex, taskId) {
      if (!taskId) {
        taskId = this.nowTaskId
      }
      // todo 选择题决策接口
      if (await TaskModel.completeOneSelect(taskId, answer, selectIndex)) {
        window.unityInstance.SendMessage("CallBack", "Js_fdldq_completeOneSelect_callBack", JSON.stringify({
          code: "000000",
          msg: "成功完成决策",
          data: ""
        }));
      }
      return true
    },
    /**
     * 监听-获取一个线索
     * @param clue 线索文字内容
     * @param clueIndex 线索序号
     * @param selectIndex 所属的选择题序号
     * @param taskId 任务编号，可以由网页内或unity传入
     * @returns {Promise<void>}
     */
    async onGetOneClue(clue, clueIndex, selectIndex, taskId) {
      console.log("test1")
      if (!taskId) {
        taskId = this.nowTaskId
        console.log("test2")
      }
      let result = await TaskModel.getOneClue(taskId, clue, clueIndex, selectIndex)
      if (result === true) {
        window.unityInstance.SendMessage("CallBack", "Js_fdldq_getOneClue_callBack", JSON.stringify({
          code: "000000",
          msg: "成功获取线索",
          data: ""
        }));
        msg_success_vant("您已成功获取一个线索")
        // todo unity传入返回成功信息
      }
    },
    /**
     * 监听-完成本任务
     */
    async onCompleteTask(taskId) {
      let $this = this;
      $this.clickJumped=true
      if (await TaskModel.completeOneTask(this.nowTaskId)) {
        // 重置meta viewport
        ToolsModel.resetMetaViewport();

        // 完成任务回调
        window.unityInstance.SendMessage("CallBack", "Js_fdldq_completeOneTask_callBack", JSON.stringify({
          code: "000000",
          msg: "成功完成任务",
          data: ""
        }));

        // 判断是否是最后一个任务
        if ($this.taskConfig.nextTaskId === "none") {
          // 跳转到结束页面
          // $this.$router.push({
          //   name: "f_end_View",
          //   query: {}
          // })
          window.location.href = "/fdldq/end"
          return
        }

        // 跳转到主界面
        // $this.$router.push({
        //   name: "AR_Main_View",
        //   query: {}
        // })
        window.location.href = "/ar_main"
      }
    },
  }
}
</script>

<style scoped lang="less">
.full-btn {
  position: fixed;
  left: 3px;
  top: 2px;
}

.webgl-content {
  position: relative;
  background-color: #221f20;
  min-height: 100vh;

  #unity-progress {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    color: #fff;
    width: 150px;
    text-align: center;
    height: 0px;
    margin: auto;
    background-color: #f6f6f6;

    #scene-text {
      margin-top: -20px;
      margin-bottom: 5px;
      font-size: 20px;
    }
  }

}

.complete-btn {
  position: absolute;
  top: 0px;
  right: 0px;
}

.direction-tip {
  line-height: 100vh;
  text-align: center;
  font-size: 25px;
  color: #333;
}
</style>
