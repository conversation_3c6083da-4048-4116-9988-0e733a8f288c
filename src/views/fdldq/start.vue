<template>
  <div v-show="screenDirection==='landscape'">
    <div class="page-container">
      <div class="task-title">前往剧本开始地点</div>
      <div class="start-container">
        <div class="address-text">请开启<span class="em">定位开关</span>，不要关闭本页面。跟随<span class="em">任务引导图</span>或<span
            class="em">工作人员</span>前往剧本开始地点。
        </div>
        <img src="@/assets/ui/fdldq/start/start.png" alt="" class="hr">
        <div class="gps-container">
          <div class="title">距离目的点还有</div>
          <div class="meter em">{{ distanceSubMeter }}</div>
          <div class="title">米</div>
        </div>
        <div class="button-container flex flex-center">
          <div class="button common-short-btn" @click="clickStartBtn" v-if="arrived">开始剧本任务</div>
        </div>
      </div>

    </div>

    <div class="full-btn full-btn-landscape" @click="screenfull.toggle()" v-if="mobileType!=='iPhone'">
      <img src="@/assets/ui/fdldq/common/full-btn-icon.png" alt="">
      <span>全屏</span>
    </div>
    <van-button type="success" class="complete-btn" @click="clickJumpBtn" :disabled="clickJumped">跳过</van-button>
  </div>
  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
</template>

<script>
import {TaskStore} from "../../store/TaskStore";
import {TaskModel} from "../../model/TaskModel";
import {mapState} from "pinia";
import {GpsModel} from "../../model/GpsModel";
import $ from "jquery";
import {playSound} from "../../utils/common";
import screenfull from 'screenfull'
import enums from "@/enums/index"
import fdldqTaskConfig from "@/scripts/fdldq/taskConfig";

export default {
  name: "f_start_View",
  components: {},
  computed: {
    ...mapState(TaskStore, {
      nowTaskId: store => store.nowTaskId,
      recordInfo: store => store.recordInfo,
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull,
      mobileType: store => store.mobileType,
    }),
  },
  data() {
    return {
      clickJumped: false,
      screenfull: screenfull,
      nowTaskConfig: {},
      // gps watcherId
      watcherId: "",
      gpsInited: false,
      // gps配置
      gpsOptions: {
        //是否使用高精度设备，如GPS。默认是true
        enableHighAccuracy: true,
        //超时时间，单位毫秒，默认为0
        timeout: 5000,
        //使用设置时间内的缓存数据，单位毫秒
        //默认为0，即始终请求新数据，其它时间则按时间刷新
        //如设为Infinity，则始终使用缓存数据
        maximumAge: 0
      },
      position: {
        coords: {},
      },
      // 当前经度
      lonNow: 0,
      // 当前纬度
      latNow: 0,
      // 任务坐标要求坐标
      taskCoordinate: [],
      // 距离判断误差 米
      distanceError: 5,
      // 到达计数
      arrivedTime: 0,
      // 是否已到达过
      arrived: false,
      // 是否需要判断位置是否达到
      needArrive: false,
      // 和任务相差的距离
      distanceSubMeter: 0,
    }
  },
  mounted() {
    document.title = "前往剧本开始地点"
    let $this = this;
    // 关卡记录-不允许返回
    TaskModel.judgeOneTaskIsCompletedOrJumpBack("S1")


    // 关卡信息初始化
    this.nowTaskConfig = TaskModel.getOneTaskConfig(this.nowTaskId)
    this.taskInfo = this.recordInfo[this.nowTaskId]
    this.needArrive = this.nowTaskConfig["locationNeeded"]

    // 地图模式
    fdldqTaskConfig.configObject.mapType === 'gps' && this.needArrive === true ? this.needArrive = true : this.needArrive = false

    if (this.needArrive) { // 如果需要定位
      this.watchGps()
    } else {
      this.clickStartBtn()
    }
  },
  methods: {
    // 监视gps位置变化
    watchGps() {
      this.watcherId = navigator.geolocation.watchPosition((position) => {
        this.position = position
        if (!this.gpsInited) {// 初始化
          this.taskCoordinate[0] = this.nowTaskConfig["locationCoordinate"][0]
          this.taskCoordinate[1] = this.nowTaskConfig["locationCoordinate"][1]
          this.distanceError = this.nowTaskConfig["locationAllowableError"]

          this.gpsInited = true
        } else {// 位置变动
          this.onChange(position)
        }
      }, (error) => {
        switch (error.code) {
          case error.TIMEOUT:
            alert('定位更新超时');
            break;
          case error.PERMISSION_DENIED:
            alert('用户拒绝提供地理位置');
            break;
          case error.POSITION_UNAVAILABLE:
            alert('地理位置不可用');
            break;
          default:
            break;
        }
      }, this.gpsOptions);
    },
    // 当gps位置改变
    onChange(position) {
      let lonNow = position.coords.longitude
      let latNow = position.coords.latitude
      this.lonNow = lonNow
      this.latNow = latNow
      // 判断是否达到任务区域
      if (this.needArrive) { // 需要定位
        this.judgeArriveTaskLocation()
      }
    },
    // 判断是否达到某一任务区域
    async judgeArriveTaskLocation() {
      // 任务坐标
      let taskLon = this.taskCoordinate[0]
      let taskLat = this.taskCoordinate[1]
      // 当前坐标
      let lonNow = this.lonNow
      let latNow = this.latNow
      // 误差判断-方法1-计算两点之间距离，判断是否在误差范围内
      let distance = GpsModel.wgs84PointsDistance(lonNow, latNow, taskLon, taskLat)
      this.distanceSubMeter = Math.round(distance)
      if (distance < this.distanceError) {
        if (this.arrivedTime < 3) {// todo 同一时间内3次达到表示真的达到了，避免误差
          this.arrivedTime++
        }
      } else {
        this.arrivedTime = 0
        this.arrived = false
      }
      if (this.arrivedTime >= 3) {
        if (!this.arrived) {
          playSound(enums.audioList[0]) // 播放成功提示音
          this.arrived = true
        }
      }
      return distance <= this.distanceError;
    },
    // 点击开始任务按钮
    async clickStartBtn() {
      await TaskModel.arriveOneTaskGpsAddress(this.nowTaskId) // 标记达到定位位置
      await TaskModel.goToOneTaskPage("S1") // 进
    },
    // 点击跳过按钮
    async clickJumpBtn() {
      this.clickJumped = true
      await TaskModel.arriveOneTaskGpsAddress(this.nowTaskId) // 标记达到定位位置
      await TaskModel.goToOneTaskPage("S1") // 进入
    },
  }
}
</script>

<style scoped lang="less">
.complete-btn {
  position: fixed;
  right: 0px;
  top: 0px;
}

.page-container {
  background-color: rgba(0, 0, 0, 0.6);
  min-height: 100vh;
  padding: 10px;
  color: #fff;
  font-weight: 500;

  .em {
    color: #faef51;
  }

  .task-title {
    margin-top: 10px;
    text-align: center;
    font-size: 10px;
    font-weight: bold;
    color: #fff;
  }

  .start-container {
    margin-top: 20px;

    .address-text {
      text-align: center;
      font-size: 8px;
    }

    img.hr {
      margin-top: 9px;
      width: 100%;
      display: block;
      height: 4px;
    }

    .gps-container {
      margin-top: 10px;
      text-align: center;

      .title {
        font-size: 8px;
        margin-top: 5px;
        margin-bottom: 5px;
      }

      .meter {
        font-size: 14px;
        font-weight: bold;
      }
    }

    .button-container {
      text-align: center;
      margin-top: 10px;

      .button {
        width: 105px;
        height: 20px;
        line-height: 20px;
        border-radius: 12px;
        text-align: center;
        font-size: 8px;
        color: #fff;
        background-image: url(../../assets/ui/platform/common/short-round-btn-bg.png);
        background-position: 50% 50%;
      }
    }
  }
}
</style>
