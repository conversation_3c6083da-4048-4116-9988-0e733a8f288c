<template>
  <div class="page-container" v-show="screenDirection==='landscape'">
    <ar-video-panel ref="arVideoPanel" :videoInfo="videoInfo" @onPlayEnd="clickCompleteTaskBtn"></ar-video-panel>
  </div>
  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
<!--  <div class="full-btn full-btn-landscape" @click="screenfull.toggle()" v-if="mobileType!=='iPhone'">-->
  <!--    <img src="@/assets/ui/fdldq/common/full-btn-icon.png" alt="">-->
  <!--    <span>全屏</span>-->
  <!--  </div>-->
</template>

<script>
import {msg_err, msg_err_vant, msg_success_vant, toast_err} from "../../utils/nut_component";
import {Button} from 'vant';
import {ref, onBeforeUpdate} from 'vue';
import screenfull from 'screenfull'
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";
import {TaskModel} from "../../model/TaskModel";
import {
  unity_fdldq_completeOneTask,
} from "../../scripts/fdldq/unityMethods";
import arVideoPanel from "@/views/components/scene/ar-VideoPanel.vue"
import {ToolsModel} from "../../model/ToolsModel";

export default {
  name: "f_a2_3_View",
  components: {arVideoPanel},
  computed: {
    ...mapState(TaskStore, {
      nowTaskId: store => store.nowTaskId,
      recordInfo: store => store.recordInfo,
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull,
      mobileType: store => store.mobileType,
    }),
  },
  data() {
    return {
      screenfull: screenfull,
      directionRight: true,
      taskId: "A2",
      taskConfig: {},
      taskInfo: {},
      videoInfo: {
        url: ""
      }
    }
  },
  async mounted() {
    // 关卡记录-不允许返回
    TaskModel.judgeOneTaskIsCompletedOrJumpBack(this.taskId)

    // 关卡信息初始化
    this.taskConfig = TaskModel.getOneTaskConfig(this.taskId)
    this.taskInfo = this.recordInfo[this.nowTaskId]
    this.videoInfo = this.taskConfig['childTaskInfo']['lists'][2]["videoList"][0]
    setTimeout(() => {
      this.$refs["arVideoPanel"].initVideo()
    }, 500)

    let $this = this;
    document.title = this.taskConfig["taskId"] + this.taskConfig["name"]

    this.regUnityMethods()
  },
  methods: {
    // 注册unity全局方法
    regUnityMethods() {
      unity_fdldq_completeOneTask(this)
    },
    // 点击完成任务按钮
    async clickCompleteTaskBtn() {
      unity_fdldq_completeOneTask(this)
      this.completeTask()
    },
    // 完成任务
    async completeTask() {
      if (await TaskModel.completeOneTask(this.nowTaskId)) {
        // 跳转到主界面
        this.$router.push({
          name: "AR_Main_View",
          query: {}
        })
      }
    }
  }
}
</script>

<style scoped lang="less">
.page-container {
  background-color: rgba(0, 0, 0, 0.6);
  min-height: 100vh;
}

.title {
  text-align: center;
  font-size: 10px;
}

.buttons {
  margin-top: 50px;
}
</style>
