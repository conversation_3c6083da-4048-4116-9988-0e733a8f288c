<template>
  <div class="page-container" v-show="screenDirection==='landscape'" id="fdldq-c2">
    <div id="three"></div>
    <div class="tip-text">请点击{{ tipText }}，查看历史资料</div>
    <div id="mui-player" style="display: none"></div>
    <!--场景2 对话框 纯文字 -->
    <div class="scene scene-2" v-if="sceneIdShow==='scene2'">
      <div class="fdldq_dialog_pureText">
        <img src="@/assets/ui/fdldq/common/文字背景框.png" alt="" class="bg">
        <div class="container">
          <textarea class="text" v-model="scene2.textShow" disabled readonly></textarea>
          <div class="button" @click="SceneMethods().clickSureBtn('scene2')">确定</div>
        </div>
      </div>
    </div>
    <!--场景3 对话框 选择题 -->
    <div class="scene scene-3" v-if="sceneIdShow==='scene3'">
      <div class="fdldq_dialog_pureText">
        <img src="@/assets/ui/fdldq/common/文字背景框.png" alt="" class="bg">
        <div class="container choose-box">
          <div class="title">{{ scene3.question }}</div>
          <div class="option-box">
            <div class="li flex flex-start" v-for="(item,index) in scene3.options">
              <img src="@/assets/ui/fdldq/common/选择题背景框.png" alt=""
                   @click="SceneMethods().clickChooseOption('scene3',index)">
              <van-icon name="success" class="checked" v-show="scene3.userAnswer.indexOf(index)!==-1"/>
              <div class="question">{{ item.content }}</div>
            </div>
          </div>
          <div class="button" @click="SceneMethods().clickShowClueBtn('scene3')">查看线索</div>
        </div>
      </div>
      <van-popup v-model:show="scene3.clueShow" position="right" :style="{ width: '60%',height:'100%' }">
        <van-collapse v-model="scene3.clueCollapse">
          <van-collapse-item v-for="(item,index) in scene3.clueList" :title="'线索'+(index+3)" :name="index"
                             @click="SceneMethods().clickChooseClueItem('scene3',item,index)">
            {{ item.content }}
          </van-collapse-item>
        </van-collapse>
      </van-popup>
    </div>
    <!--场景4 对话框 选择题 -->
    <div class="scene scene-4" v-if="sceneIdShow==='scene4'">
      <div class="fdldq_dialog_pureText">
        <img src="@/assets/ui/fdldq/common/文字背景框.png" alt="" class="bg">
        <div class="container choose-box">
          <div class="title">{{ scene4.question }}</div>
          <div class="option-box">
            <div class="li flex flex-start" v-for="(item,index) in scene4.options">
              <img src="@/assets/ui/fdldq/common/选择题背景框.png" alt=""
                   @click="SceneMethods().clickChooseOption('scene4',index)">
              <van-icon name="success" class="checked" v-show="scene4.userAnswer.indexOf(index)!==-1"/>
              <div class="question">{{ item.content }}</div>
            </div>
          </div>
          <div class="button" @click="SceneMethods().clickShowClueBtn('scene4')">查看线索</div>
        </div>
      </div>
      <van-popup v-model:show="scene4.clueShow" position="right" :style="{ width: '60%',height:'100%' }">
        <van-collapse v-model="scene4.clueCollapse">
          <van-collapse-item v-for="(item,index) in scene4.clueList" :title="'线索'+(index+4)" :name="index"
                             @click="SceneMethods().clickChooseClueItem('scene4',item,index)">
            {{ item.content }}
          </van-collapse-item>
        </van-collapse>
      </van-popup>
    </div>
    <!--场景5 对话框 选择题 -->
    <div class="scene scene-5" v-if="sceneIdShow==='scene5'">
      <div class="fdldq_dialog_pureText">
        <img src="@/assets/ui/fdldq/common/文字背景框.png" alt="" class="bg">
        <div class="container choose-box">
          <div class="title">{{ scene5.question }}</div>
          <div class="option-box">
            <div class="li flex flex-start" v-for="(item,index) in scene5.options">
              <img src="@/assets/ui/fdldq/common/选择题背景框.png" alt=""
                   @click="SceneMethods().clickChooseOption('scene5',index)">
              <van-icon name="success" class="checked" v-show="scene5.userAnswer.indexOf(index)!==-1"/>
              <div class="question">{{ item.content }}</div>
            </div>
          </div>
          <div class="button" @click="SceneMethods().clickShowClueBtn('scene5')">查看线索</div>
        </div>
      </div>
      <van-popup v-model:show="scene5.clueShow" position="right" :style="{ width: '60%',height:'100%' }">
        <van-collapse v-model="scene5.clueCollapse">
          <van-collapse-item v-for="(item,index) in scene5.clueList" :title="'线索'+(index+5)" :name="index"
                             @click="SceneMethods().clickChooseClueItem('scene5',item,index)">
            {{ item.content }}
          </van-collapse-item>
        </van-collapse>
      </van-popup>
    </div>

  </div>

  <van-button type="success" class="complete-btn" @click="SceneMethods().onCompleteTask()" :disabled="clickJumped">
    跳过
  </van-button>
  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
  <div class="full-btn full-btn-landscape" @click="screenfull.toggle()"
       v-if="mobileType!=='iPhone'&&screenDirection==='landscape'">
    <img src="@/assets/ui/fdldq/common/full-btn-icon.png" alt="">
    <span>全屏</span>
  </div>
</template>

<script>
import {msg_err_vant, msg_success_vant,} from "../../utils/nut_component";
import screenfull from 'screenfull'
import {unity_fdldq_chooseRole} from "../../scripts/fdldq/unityMethods";
import {TaskStore} from "../../store/TaskStore";
import {ScriptRecordModel} from "../../model/ScriptRecordModel";
import {TaskModel} from "../../model/TaskModel";
import {mapState} from "pinia";
import * as THREE from 'three'
import {GLTFLoader} from 'three/examples/jsm/loaders/GLTFLoader'
import {DRACOLoader} from 'three/examples/jsm/loaders/DRACOLoader'
import {CSS2DObject, CSS2DRenderer} from 'three/examples/jsm/renderers/CSS2DRenderer';
import {ThreeModel} from "../../model/ThreeModel";
import {EffectComposer} from "three/examples/jsm/postprocessing/EffectComposer.js"
import {RenderPass} from "three/examples/jsm/postprocessing/RenderPass.js"
import {OutlinePass} from "three/examples/jsm/postprocessing/OutlinePass.js"
import {ShaderPass} from "three/examples/jsm/postprocessing/ShaderPass.js"
import {FXAAShader} from "three/examples/jsm/shaders/FXAAShader.js"
import {showLoadingToast} from 'vant';
import {ToolsModel} from "../../model/ToolsModel";
import $ from 'jquery'
import {OrbitControls} from 'three/examples/jsm/controls/OrbitControls'
import {GUI} from 'three/examples/jsm/libs/lil-gui.module.min'
import {ThreeCssModel} from "../../model/ThreeCssModel"
import MuiPlayer from "mui-player";

const publicPath = import.meta.env.BASE_URL
export default {
  name: "f_c2_View",
  components: {},
  computed: {
    ...mapState(TaskStore, {
      nowTaskId: store => store.nowTaskId,
      recordInfo: store => store.recordInfo,
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull,
      mobileType: store => store.mobileType,
    }),
  },
  watch: {
    // 监听是否全屏
    screenFull(newVal, oldVal) {

    },
    // 监听横竖屏
    screenDirection(newVal, oldVal) {
      if (newVal === "landscape") {// 如果切换为横屏
        // 如果实验已加载就设置分辨率
        if (!this.three.loaded) {
          setTimeout(() => {
            this.ThreeMethods().load()
          }, 1000) // 切换横竖屏后，要等待一段时间才能获取到innerWidth
        } else {
          if (window.css2DRender) {
            $("#fdldq_c2_css2DRender").show()
            // 切换到横屏要重新设置css2d，不然点击失效
            window.css2DRender.setSize(window.innerWidth, window.innerHeight)
          }
        }
      }
      // 如果切换为竖屏竖屏，恢复metaViewPort
      if (newVal === "portrait") {
        ToolsModel.resetMetaViewport();
        if (window.css2DRender) {
          $("#fdldq_c2_css2DRender").hide()
        }
      }
    }
  },
  data() {
    return {
      publicPath: import.meta.env.BASE_URL,
      clickJumped: false,
      taskId: "C2",
      screenfull: screenfull,
      three: {
        loaded: false,
        canClickObjects: [],// 需要进行点击的对象数组
        mouse: new THREE.Vector2(),
      },
      sceneIdShow: "",
      sceneId: "",
      tipText: "放映机",
      scene1: {
        videoUrl: publicPath + "video/fdldq/c2/1.mp4",
      },
      scene2: {
        text: "\t就在红军先遣队沿左岸急行军时，忽然对岸出现了无数火把，像条长蛇向泸定桥的方向奔去。经过侦查，我军发现那是往泸定桥方向增援的敌军。",
        textShow: "\t就在红军先遣队沿左岸急行军时，忽然对岸出现了无数火把，像条长蛇向泸定桥的方向奔去。经过侦查，我军发现那是往泸定桥方向增援的敌军。",
      },
      scene3: {
        selectIndex: 0,
        clueShow: false,
        clueCollapse: [],
        userAnswer: [],
        firstAnswer: [],
        userClueList: [],
        "type": "single",
        "question": "你认为我方红军应该怎么应对？",
        "options": [
          {
            content: "A. 先发制人，立即突袭。"
          },
          {
            content: "B. 点亮火把，继续静默前进。"
          },
          {
            content: "C. 找到对方带头人，说服对方改变行军路线或截止日期。"
          },
        ],
        answer: [1],// 正确答案
        clueList: [
          {
            content: "线索1：对方对我们已有警觉，只是因为天色暗，隔着河，所以不那么确定是敌是友。"
          },
          {
            content: "线索2：轻举妄动可能导致暴露和任务失败。"
          },
        ],
      },
      scene4: {
        selectIndex: 1,
        clueShow: false,
        clueCollapse: [],
        userAnswer: [],
        firstAnswer: [],
        userClueList: [],
        "type": "single",
        "question": "对方询问我们是哪个部分的队伍，你认为应该如何回答？",
        "options": [
          {
            content: "A.红军。"
          },
          {
            content: "B.日军。"
          },
          {
            content: "C.川军。"
          },
          {
            content: "D.国军。"
          }
        ],
        answer: [2],// 正确答案
        clueList: [
          {
            content: "线索1：四川军阀已归属国民党。"
          },
          {
            content: "线索2：四川军阀和部队士兵基本都是四川人。"
          },
        ],
      },
      scene5: {
        selectIndex: 2,
        clueShow: false,
        clueCollapse: [],
        userAnswer: [],
        firstAnswer: [],
        userClueList: [],
        "type": "single",
        "question": "行军途中，对岸的火把突然熄灭了，我军用号谱询问对方现在什么情况，敌方回答：宿营了！你认为我军该做什么样的决策呢？",
        "options": [
          {
            content: "A.宿营。"
          },
          {
            content: "B.继续行军。"
          },
          {
            content: "C.朝对岸敌人开火"
          }
        ],
        answer: [1],// 正确答案
        clueList: [
          {
            content: "线索1：一旦天亮，对方会马上发现我们是红军。"
          },
          {
            content: "线索2：轻举妄动大概率会导致战斗及任务失败。"
          },
        ],
      },
    }
  },
  async beforeCreate() {

  },
  async mounted() {
    document.title = "C2-隔河竞走"
    let $this = this;
    // 关卡记录-不允许返回
    //await TaskModel.judgeOneTaskIsCompletedOrJumpBack("C1")

    // 如果是横屏才加载
    if (this.screenDirection === "landscape") {
      await $this.ThreeMethods().load()
    }
  },
  beforeUnmount() {
    this.ThreeMethods().beforeExit()
  },
  beforeRouteLeave() {
    this.ThreeMethods().beforeExit()
  },
  methods: {
    ThreeMethods() {
      let $this = this;
      // todo 加载的资源全是可以下载的 思考保护方法 refer等？
      return {
        // 开始加载
        async load() {
          // 如果是在本页才加载，避免生命周期错误 todo 优化逻辑
          if (window.location.href.indexOf("fdldq/c2") !== -1) {
            showLoadingToast({
              message: '加载中...',
              forbidClick: true,
            });
            $this.three.loaded = true
            await $this.ThreeMethods().init()
          }
        },
        // 初始化
        async init() {
          // WebGL渲染器
          const width = window.innerWidth, height = window.innerHeight;
          const renderer = new THREE.WebGLRenderer()
          renderer.setSize(width, height)
          renderer.setPixelRatio(window.devicePixelRatio)
          renderer.domElement.id = "unity-canvas"
          document.getElementById('three').appendChild(renderer.domElement)

          // 场景
          const scene = new THREE.Scene()
          $this.three.clock = new THREE.Clock()
          window.scene = scene

          // 光
          const ambient = new THREE.AmbientLight(0xffffff, 0.6)
          scene.add(ambient)

          // 相机
          const camera = new THREE.PerspectiveCamera(60, width / height, 0.3, 1000)
          camera.position.set(-10.6, 48.4, -3.2)
          camera.lookAt(0, 0, 0)
          // GUI
          // let gui = new GUI()
          // window.gui = gui
          // ThreeModel.guiAddCameraFolder(gui, camera, "相机", [-100, 100])


          // 辅助坐标轴
          const axesHelper = new THREE.AxesHelper(100)
          // scene.add(axesHelper)


          // 模型加载-draco
          let dracoLoader = new DRACOLoader()
          dracoLoader.setDecoderPath("js")
          let gltfLoader = new GLTFLoader()
          gltfLoader.setDRACOLoader(dracoLoader)

          // 加载模型
          $this.ThreeMethods().loadModel_zhangPeng()
          $this.ThreeMethods().loadModel_desk()

          // css2D渲染器
          let css2DRender = new CSS2DRenderer();
          css2DRender.setSize(width, height)
          css2DRender.domElement.style.position = 'absolute';
          // css2DRender.domElement.style.zIndex = -1
          css2DRender.domElement.style.top = '0px';
          css2DRender.domElement.style.touchAction = "none"
          css2DRender.domElement.id = "fdldq_c2_css2DRender"
          document.body.appendChild(css2DRender.domElement);


          // 视角控制器
          // const controls = new OrbitControls(camera, css2DRender.domElement)

          // window全局变量-避免渲染错误
          window.css2DRender = css2DRender
          window.renderer = renderer
          window.camera = camera
          window.raycaster = new THREE.Raycaster();


          // 等待一段时间后开始渲染
          setTimeout(() => {
            $this.ThreeMethods().render()
            // 点击事件监听
            $this.ThreeMethods().listenOnScreenClick()
            // 屏幕尺寸改变监听
            window.addEventListener('resize', $this.ThreeMethods().onWindowResize);
          }, 500)
        },

        // 渲染器
        render() {
          let delta = $this.three.clock.getDelta()
          window.renderId = requestAnimationFrame($this.ThreeMethods().render)
          window.renderer.render(window.scene, window.camera)
          window.css2DRender.render(window.scene, window.camera);
          if (window.composer) {
            window.composer.render()
          }
        },
        // 加载模型-帐篷
        loadModel_zhangPeng() {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/a1/帐篷.glb").then(gltf => {
            let model = gltf.scene
            model.traverse(async function (object) {
              if (object.isMesh) {
                if (object.name === "SDCS_mubu") {
                  // object.material = new THREE.MeshStandardMaterial({
                  //   map: await ThreeModel.loadTexture($this.publicPath+"textures/fdldq/a1/帐篷幕布.png")
                  // })
                  object.visible = false
                }
                if (object.name === "SDCS_zhangpeng") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/a1/帐篷.png")
                  })
                }

              }
            })
            model.scale.set(200, 200, 200)
            model.position.set(-32.8, 33.6, -8.2)
            model.rotation.set(0.0502654824574367, -0.285, -1.18123883774976)
            // ThreeModel.guiAddModelFolder(window.gui,model,"帐篷",[-1,1])
            scene.add(model);
          })
        },
        // 加载模型-桌子
        loadModel_desk() {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/d3/桌子.glb").then(gltf => {
            let model = gltf.scene
            model.scale.set(250, 250, 250)
            model.position.set(-23, 18.8, -7)
            model.rotation.set(0.0502654824574367, -0.287610597594363, -1.18123883774976)
            model.goodsId = "desk"
            scene.add(model);
            $this.ThreeMethods().loadModel_diTu(model)
            $this.ThreeMethods().loadModel_dangAn(model)
            $this.ThreeMethods().loadModel_meiYouDeng(model)
            $this.ThreeMethods().loadModel_xinFeng(model)
            $this.ThreeMethods().loadModel_fangYingJi(model)
            // ThreeModel.guiAddModelFolder(window.gui,model,"桌子",[-100,100])
          })
        },
        // 加载模型-煤油灯
        async loadModel_meiYouDeng(parentModel) {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/a1/煤油灯.glb").then(gltf => {
            let model = gltf.scene
            model.scale.set(0.07, 0.07, 0.07)
            model.position.set(0.020, 0.041, -0.058)
            model.rotation.set(-1.39371669411541, -1.5707963267949, -1.41371669411541)
            parentModel.add(model)
            //ThreeModel.guiAddModelFolder(window.gui,model,"煤油灯",[-1,1])
            model.traverse(async function (object) {
              if (object.isMesh) {
                if (object.name === "polySurface4_1") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/a1/煤油灯1.png")
                  })
                }
                if (object.name === "polySurface4_2") {
                  object.material = new THREE.MeshPhongMaterial({
                    color: "#f3af59"
                  })
                  // 启用透明
                  object.material.transparent = true
                  //透明度
                  object.material.opacity = 0.8
                  //透明反射效果
                  object.material.refractionRatio = 1
                  object.material.metalness = 0.2
                  object.material.roughness = 0
                }
              }
            })
            // 创建点光源
            let light = new THREE.PointLight("#D97E28")
            light.distance = 45
            light.decay = 2
            light.position.set(0, 0.5, 0)
            light.power = 18 * Math.PI
            model.add(light)
          })
        },
        // 加载模型-档案
        async loadModel_dangAn(parentModel) {
          let geometry = new THREE.BoxGeometry(1.4, 2.2, 0.01);
          let material = new THREE.MeshBasicMaterial({
            map: await ThreeModel.loadTexture($this.publicPath + "images/fdldq/a1/文件.jpg")
          });
          let model = new THREE.Mesh(geometry, material);
          model.goodsId = "dangAn"
          $this.three.canClickObjects.push(model)
          model.scale.set(0.012, 0.012, 0.012)
          model.position.set(-0.008, 0.041, -0.053)
          model.rotation.set(-1.5707963267949, -0.0251327412287183, -1.49539810310874)
          parentModel.add(model)
          //ThreeModel.guiAddModelFolder(window.gui,model,"档案",[-1,1])
        },
        // 加载模型-地图
        async loadModel_diTu(parentModel) {
          let geometry = new THREE.BoxGeometry(3.75, 2.44, 0.01);
          let material = new THREE.MeshBasicMaterial({
            map: await ThreeModel.loadTexture($this.publicPath + "images/fdldq/a1/红军长征地图.jpeg")
          });
          let model = new THREE.Mesh(geometry, material);
          model.goodsId = "diTu"
          $this.three.canClickObjects.push(model)
          model.scale.set(0.015, 0.015, 0.015)
          model.position.set(-0.008, 0.042, -0.012)
          model.rotation.set(-1.5707963267949, -0.0251327412287183, -1.5707963267949)
          parentModel.add(model)
          //ThreeModel.guiAddModelFolder(window.gui,model,"地图",[-1,1])
        },
        // 加载模型-信封
        async loadModel_xinFeng(parentModel) {
          let geometry = new THREE.BoxGeometry(1.22, 2.44, 0.01);
          let material = new THREE.MeshBasicMaterial({
            map: await ThreeModel.loadTexture($this.publicPath + "images/fdldq/a1/信封.jpg")
          });
          // 信封1
          let model = new THREE.Mesh(geometry, material);
          model.goodsId = "xinFeng"
          $this.three.canClickObjects.push(model)
          model.scale.set(0.008, 0.008, 0.008)
          model.position.set(-0.009, 0.042, 0.026)
          model.rotation.set(-1.5707963267949, -0.0251327412287183, -1.49539810310874)
          parentModel.add(model)
          //ThreeModel.guiAddModelFolder(window.gui,model,"信封",[-1,1])
          // 信封2
          let model2 = new THREE.Mesh(geometry, material);
          model2.goodsId = "xinFeng"
          $this.three.canClickObjects.push(model2)
          model2.scale.set(0.008, 0.008, 0.008)
          model2.position.set(-0.009, 0.041, 0.033)
          model2.rotation.set(-1.5707963267949, -0.0251327412287183, -2.1865484868985)
          parentModel.add(model2)
          //ThreeModel.guiAddModelFolder(window.gui,model2,"信封2",[-1,1])
        },
        // 加载模型-放映机
        async loadModel_fangYingJi(parentModel) {
          ThreeModel.loadGltfDraco($this.publicPath + "models/fdldq/gltf/a1/放映机.glb").then(gltf => {
            let model = gltf.scene
            model.scale.set(0.0033, 0.0033, 0.0033)
            model.position.set(0.007, 0.041, 0.068)
            model.rotation.set(-3.141592653589793, -3.141592653589793, -3.141592653589793)
            model.goodsId = "fangYingJi"
            parentModel.add(model)
            $this.three.canClickObjects.push(model)
            //ThreeModel.guiAddModelFolder(window.gui,model,"放映机",[-1,1])
            model.traverse(async function (object) {
              if (object.isMesh) {
                object.goodsId = "fangYingJi"
                object.material = new THREE.MeshPhongMaterial({
                  map: await ThreeModel.loadTexture($this.publicPath + "textures/fdldq/a1/放映机.png")
                })
              }
            })
            $this.ThreeMethods().outLineGoods("fangYingJi")
          })
        },
        // 屏幕点击事件
        onScreenClick: function (event) {
          let targetDom = event.target
          if (targetDom.id !== 'fdldq_c2_css2DRender') {
            return
          }
          //event.preventDefault();
          // 将鼠标位置归一化为设备坐标。x 和 y 方向的取值范围是 (-1 to +1)
          // renderer为three的渲染器
          let px = window.renderer.domElement.getBoundingClientRect().left;
          let py = window.renderer.domElement.getBoundingClientRect().top;
          $this.three.mouse.x = ((event.clientX - px) / (window.renderer.domElement.offsetWidth)) * 2 - 1;
          $this.three.mouse.y = -((event.clientY - py) / (window.renderer.domElement.offsetHeight)) * 2 + 1;
          // 通过摄像机和鼠标位置更新射线
          window.raycaster.setFromCamera($this.three.mouse, window.camera);
          // 计算物体和射线的焦点
          let intersects = window.raycaster.intersectObjects($this.three.canClickObjects, true);
          if (intersects.length > 0) {
            let goodsId = intersects[0].object.goodsId
            let objects = [intersects[0].object]
            if (goodsId === "xinFeng") {// 如果是信封就两个一起标记
              let [deskKey,] = ThreeModel.findObjectFromArrByNameAndVal(window.scene.children, "goodsId", "desk")
              let deskModel = window.scene.children[deskKey]
              objects = ThreeModel.findObjectsFromArrByNameAndVal(deskModel.children, "goodsId", "xinFeng")
              // $this.ThreeMethods().outlineObj(objects)
            }
            $this.ThreeMethods().clickGoods(objects, goodsId)
          } else {
          }
        },
        // 选择了某个物品
        clickGoods(objects, goodsId) {
          if (goodsId === "fangYingJi" && $this.sceneIdShow === "" && $this.sceneId === "") {// 触发场景1
            $this.sceneId = "scene1"
            $this.SceneMethods().playVideo($this.scene1.videoUrl)
          }
          if (goodsId === "dangAn" && $this.sceneIdShow === "" && $this.sceneId === "scene2") {// 触发场景2
            $this.sceneIdShow = "scene2"
          }
        },
        // 开始监听屏幕点击事件
        listenOnScreenClick: function () {
          window.screenClickListenFn = (e) => {
            $this.ThreeMethods().onScreenClick(e);
          }
          window.addEventListener('click', window.screenClickListenFn);
        },
        // 高亮某个物体
        outLineGoods(goodId) {
          if ($this.mobileType === "iPhone") { // iphone手机不显示轮廓 避免崩溃
            return
          }
          let [deskKey,] = ThreeModel.findObjectFromArrByNameAndVal(window.scene.children, "goodsId", "desk")
          let deskModel = window.scene.children[deskKey]
          let objects = ThreeModel.findObjectsFromArrByNameAndVal(deskModel.children, "goodsId", goodId)
          $this.ThreeMethods().outlineObj(objects)
        },
        // 高亮显示模型
        outlineObj(selectedObjects) {
          // 创建一个EffectComposer（效果组合器）对象，然后在该对象上添加后期处理通道。
          let composer = new EffectComposer(window.renderer)
          // 新建一个场景通道  为了覆盖到原理来的场景上
          let renderPass = new RenderPass(window.scene, window.camera)
          composer.addPass(renderPass);
          // 物体边缘发光通道
          let outlinePass = new OutlinePass(new THREE.Vector2(window.innerWidth, window.innerHeight), window.scene, window.camera, selectedObjects)
          outlinePass.selectedObjects = selectedObjects
          outlinePass.edgeStrength = 15.0 // 边框的亮度
          outlinePass.edgeGlow = 2// 光晕[0,1]
          outlinePass.usePatternTexture = false // 是否使用父级的材质
          outlinePass.edgeThickness = 2.0 // 边框宽度
          outlinePass.downSampleRatio = 1 // 边框弯曲度
          outlinePass.visibleEdgeColor.set(parseInt(0xff0000)) // 呼吸显示的颜色
          outlinePass.clear = true
          composer.addPass(outlinePass)
          // 自定义的着色器通道 作为参数
          let effectFXAA = new ShaderPass(FXAAShader)
          effectFXAA.uniforms.resolution.value.set(0.25 / window.innerWidth, 0.25 / window.innerHeight) // 决定选择后屏幕清晰度，1会变模糊
          effectFXAA.renderToScreen = true
          composer.addPass(effectFXAA)
          window.composer = composer
        },
        // 当屏幕尺寸改变
        onWindowResize() {
          window.renderer.setSize(window.innerWidth, window.innerHeight)
          window.css2DRender.setSize(window.innerWidth, window.innerHeight)
          window.camera.aspect = window.innerWidth / window.innerHeight;
          window.camera.updateProjectionMatrix();
        },
        // 退出前清理
        beforeExit() {
          if (!window.scene) {
            return
          }
          cancelAnimationFrame(window.renderId);// Stop the animation
          // 消除threejs和全局变量
          if (document.querySelector("#fdldq_c2_css2DRender")) {
            document.querySelector("#fdldq_c2_css2DRender").remove()
          }
          delete window.scene
          delete window.renderer
          delete window.camera
          delete window.raycaster
          delete window.composer
          delete window.renderId
          window.removeEventListener('click', window.screenClickListenFn);
          delete window.css2DRender
          delete window.screenClickListenFn
          console.log("清理完成")
        }
      }
    },
    SceneMethods() {
      let $this = this;
      return {
        /**
         * 播放文字音频-人声同步
         * todo 要实现随网页高度改变而改变对话框背景大小 1、更合理的css方法 2、用threeJs？
         * todo 解决字显示时有中文符合，字行数改变问题
         */
        async playTextAudio(sceneName) {
          // qq浏览器手动拖动文字编辑框会重新渲染canvas，但是自动scroll无效
          // document.body.scrollTop = 50;
          // document.documentElement.scrollTop = 50;
          let [audio, duration] = await ThreeCssModel.playBgSound($this[sceneName].audioUrl)
          $this[sceneName].audio = audio
          // 和声音时长同步显示文字 audio播放进度算出文字该显示的进度 这种算法要求声音完全是朗读文字，字出现和声音才刚好对上
          duration = duration * 1000
          let textLength = $this[sceneName].text.length
          audio.ontimeupdate = (e) => {
            let currentTime = audio.currentTime * 1000
            let percent = currentTime / duration
            let needShowTextLength = Math.round(textLength * percent)
            $this[sceneName].textShow = $this[sceneName].text.slice(0, needShowTextLength)
          }
        },
        // 点击文字面板的确认按钮
        async clickSureBtn(sceneName) {
          if (sceneName !== "scene2") {
            $this[sceneName].audio.remove()
            if ($this[sceneName].textShow !== $this[sceneName].text) {
              $this[sceneName].textShow = $this[sceneName].text
              return;
            }
          }
          if (sceneName === "scene2") {
            $this.sceneId = "scene3"
            $this.sceneIdShow = "scene3"
          }
        },
        // 播放某个视频
        playVideo(videoUrl) {
          $("#mui-player").show()
          let height = 0
          if ($this.screenFull) {
            height = window.screen.height
          } else {
            height = window.innerHeight
          }
          let mp = new MuiPlayer({
            container: '#mui-player',
            // height: window.screen.height,// todo iphone适配
            height: height,
            autoFit: false,
            autoplay: true,
            // initFullFixed:true,
            videoAttribute: [
              {attrKey: 'webkit-playsinline', attrValue: 'webkit-playsinline'},
              {attrKey: 'playsinline', attrValue: 'playsinline'},
              {attrKey: 'x5-video-player-type', attrValue: 'h5-page'},
            ],
            src: videoUrl,
          })
          console.log(mp)
          mp.video().onended = () => {
            if ($this.sceneId === "scene1") {
              $this.sceneId = "scene2"
              $this.sceneIdShow = ""
              $("#mui-player").hide()
              $("#fdldq_b3_css2DRender").focus()
              $this.tipText = "档案"
              $this.ThreeMethods().outLineGoods("dangAn")
            }

          };
        },
        // 完成任务
        async onCompleteTask() {
          console.log("已完成任务")
          $this.clickJumped = true
          if (await TaskModel.completeOneTask($this.nowTaskId)) {
            // 重置meta viewport
            ToolsModel.resetMetaViewport();

            // 跳转到主界面
            // $this.$router.push({
            //   name: "AR_Main_View",
            //   query: {}
            // })
            window.location.href = "/ar_main"
          }
        },
        // 点击选择题某个答案
        async clickChooseOption(sceneId, optionIndex) {
          $this[sceneId].userAnswer = [optionIndex]
          let result = true
          $this[sceneId].answer.forEach(needAnswer => {
            if ($this[sceneId].userAnswer.indexOf(needAnswer) === -1) {
              result = false
            }
          })
          if ($this[sceneId].firstAnswer.length === 0) { // 如果没有选择过
            $this[sceneId].firstAnswer = JSON.parse(JSON.stringify($this[sceneId].userAnswer)) // 保存首次答案，用于计分
          }
          if (result) {
            msg_success_vant("答案正确")
            let taskId = $this.nowTaskId
            if (await TaskModel.completeOneSelect(taskId, $this[sceneId].firstAnswer, $this[sceneId].selectIndex)) {
              if($this.sceneId==="scene3"){
                $this.sceneIdShow = "scene4"
                $this.sceneId = "scene4"
                return
              }
              if($this.sceneId==="scene4"){
                $this.sceneIdShow = "scene5"
                $this.sceneId = "scene5"
                return
              }
              if($this.sceneId==="scene5"){ // 结束任务
                $this.SceneMethods().onCompleteTask()
              }
            }
          } else {
            msg_err_vant("答案错误")
          }
        },
        // 点击查看线索按钮
        clickShowClueBtn(sceneId) {
          $this[sceneId]["clueShow"] = true
        },
        // 点击某个线索按钮
        async clickChooseClueItem(sceneId, clue, clueIndex) {
          if ($this[sceneId].userClueList.indexOf(clueIndex) === -1) {
            $this[sceneId].userClueList.push(clueIndex)
            let result = await TaskModel.getOneClue($this.nowTaskId, clue, clueIndex, $this[sceneId].selectIndex)
            if (result === true) {
              msg_success_vant("您已成功获取一个线索！")
            }
          }
        }
      }
    }
  }
}
</script>
<style>
/*动态添加的dom*/
</style>
<style scoped lang="less">
.choose-box {
  .title {
    margin-top: 25px;
    margin-bottom: 5px;
    font-size: 6px;
    line-height: 7px;
    color: #222;
    text-indent: 2em;
  }

  .option-box {
    margin-bottom: 5px;

    .li {
      position: relative;
      height: 17px;

      img {
        width: 12px;
        height: 12px;
        margin-right: 3px;
        margin-bottom: 3px;
      }

      .checked {
        position: absolute;
        left: 2.6px;
        top: 3.5px;
        font-size: 7px;
        color: #777;
      }

      .question {
        font-size: 5.5px;
        padding-bottom: 2.5px;
        line-height: 7px;
        color: #222;
      }
    }
  }
}

.complete-btn {
  position: absolute;
  top: 0px;
  right: 0px;
  z-index: 3;
}

.page-container {
  background-color: rgba(0, 0, 0, 0.6);
  min-height: 100vh;
  color: #fff;

  .task-title {
    text-align: center;
    font-size: 10px;
    color: #fff;
  }
}

.tip-text {
  position: fixed;
  bottom: 60px;
  width: 120px;
  text-align: center;
  left: (375-100)/2px;
  color: #cdaa39;
  font-size: 8px;
  font-weight: bolder;
  animation: blink 1s 0.3s linear both infinite;
  -webkit-animation: blink 1s 0.3s linear both infinite;

  @keyframes blink {
    0% {
      opacity: 0;
    }
    20% {
      opacity: 0.5;
    }
    70% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
}

.scene {
  z-index: 3;
  //position: fixed;
  //left: 0px;
  //top: 0px;
  //width: 100%;
  //min-height: 130px;
}

.fdldq_dialog_pureText, .fdldq_dialog_pureImg {
  .bg {
    width: 230px;
    position: absolute;
    left: 72.5px;
    top: 5px;
    z-index: 3;
  }

  .container {
    width: 150px;
    position: absolute;
    left: 112.5px;
    top: 15px;
    z-index: 4;

    ::-webkit-scrollbar {
      background-color: rgba(0, 0, 0, 0);
      color: transparent;
    }

    .text {
      width: 100%;
      background: none;
      border: none;

      margin-top: 30px;
      font-size: 6px;
      line-height: 9px;
      letter-spacing: 0.3px;
      color: #222;
      height: 90px;
      overflow-y: scroll;
      font-weight: 400;
      resize: none;

      ::-webkit-scrollbar {
        background-color: rgba(0, 0, 0, 0);
        color: transparent;
      }
    }

    .button {
      display: block;
      background-size: contain;
      background-position: 50% 50%;
      -webkit-backface-visibility: hidden;
      background-image: url("../../assets/ui/fdldq/common/按钮.png");
      position: absolute;
      width: 50px;
      height: 15px;
      bottom: -10px;
      left: 50.5px;
      line-height: 15px;
      text-align: center;
      font-size: 6px;
      color: #333;
    }
  }
}

.fdldq_dialog_pureImg {
  .container {
    img.pure-img {
      margin-top: 30px;
      margin-bottom: 15px;
      width: 1082/7px;
      height: 520/7px;
    }
  }
}

#mui-player {
  display: none;
  position: fixed;
  left: 0px;
  right: 0px;
  top: 0px;
  bottom: 0px;
  width: 100%;
  //height: 100vh;
  z-index: 10;
}
</style>
