<template>
  <div class="webgl-content">
    <!--    <div id="unityContainer" style="position: absolute; left: 50%; top: 50%; -webkit-transform: translate(-50%, -50%); transform: translate(-50%, -50%);width: 100%; height: 100%;">-->
    <div id="unityContainer">
    </div>
    <div id="unity-progress">
      <div id="scene-text">123</div>
      <div id="progress-text">50%</div>
    </div>
    <van-button type="success" class="full-btn" @click="toggleFull">全屏</van-button>
  </div>
</template>
<link rel="stylesheet" href="">
<script>
import {UnityModel} from "../../model/unity/UnityModel";
import {msg_err_vant} from "../../utils/nut_component";
import screenfull from 'screenfull'

export default {
  name: "f_1_2View",
  data() {
    return {
      screenAsFull: false,
      webglInfo: {
        screenHeight: 400,
        screenWidth: 800,
        width: 400,
        height: 800
      },
    }
  },
  beforeCreate() {
    // 删除header meta

  },
  mounted() {
    let $this=this;
    this.webglInfo.width = window.innerWidth
    this.webglInfo.height = window.innerHeight
    this.webglInfo.screenWidth = window.screen.width
    this.webglInfo.screenHeight = window.screen.height
    let unityPath = "/webgl/fdldq/"
    UnityModel.loadWebGlResource2019(unityPath).then(loaded => {
      if (loaded) {
        // $("#unityContainer").width(window.screen.width + "px");
        window.unityInstance = UnityLoader.instantiate("unityContainer", unityPath + "Build/HSAR_FDLDQ_Webgl.json", {onProgress: (unityInstance, progress) => UnityProgress(unityInstance, progress, "A1-长征困局")});
      }
    })
    // 调整unity webgl尺寸，解决模糊和显示问题
    window.unitySizeFixed = function (timeOut, plus) {
      let width = $this.webglInfo.width
      let height = $this.webglInfo.height
      plus = true
      if (plus) {
        width = width * devicePixelRatio
        height = height * devicePixelRatio
      }
      let metaViewport = document.getElementById("viewport")
      if (metaViewport) {
        // metaViewport.parentNode.removeChild(metaViewport)
        metaViewport.setAttribute('content', 'width=' + width + "");// todo 移动端能否正常缩放清晰看这个viewport和webgl的viewport
      }
      setTimeout(() => {
        let canvas = document.getElementById("#canvas")
        canvas.width = Math.round(width);
        canvas.height = Math.round(height);
        let gl = canvas.getContext("webgl2");
        window.gl = gl
        gl.viewport(0, 0, gl.drawingBufferWidth, gl.drawingBufferHeight);
        document.getElementById("#canvas").style.width = width + "px"
        document.getElementById("#canvas").style.height = height + "px"
      }, timeOut ? timeOut : 3000)
    }

    // 调整unity webgl尺寸，解决显示问题--全屏时 todo 也显示清晰
    window.unitySizeFixedOnFull = function () {
      let width = $this.webglInfo.screenWidth
      let height = $this.webglInfo.screenHeight
      let metaViewport = document.getElementById("viewport")
      if (metaViewport) {
        // metaViewport.parentNode.removeChild(metaViewport)
        metaViewport.setAttribute('content', 'width=' + width);
      }
      setTimeout(() => {
        let canvas = document.getElementById("#canvas")
        canvas.width = Math.round(width);
        canvas.height = Math.round(height);
        let gl = canvas.getContext("webgl2");
        window.gl = gl
        gl.viewport(0, 0, gl.drawingBufferWidth, gl.drawingBufferHeight);
        document.getElementById("#canvas").style.width = width + "px"
        document.getElementById("#canvas").style.height = height + "px"
      }, 500)
    }

    // 异步测试-延时2秒
    window.async_wait = function () {
      return new Promise((resove, reject) => {
        setTimeout(() => {
          resove(true)
        }, 2000)
      })
    }

    // 设置场景-同步返回方法-要用这个就改下方法名称去掉3
    window.js_fdldq_setScene = function () {
      //window.unitySizeFixed(false, false)
      return "B2,1,1"
    }

    // 选择角色
    window.js_fdldq_chooseRole = async function (roleName) {
      await window.async_wait();
      alert("成功请求-选择角色")
      if (roleName === "r1") {
        alert("您选择了李世强")
      }
      if (roleName === "r2") {
        alert("您选择了徐峰")
      }
      window.unityInstance.SendMessage("CallBack", "Js_fdldq_chooseRole_callBack", JSON.stringify({
        code: "000000",
        msg: "成功选择角色",
        data: ""
      }));
    }

    // 完成某个选择题/决策
    window.js_fdldq_completeOneSelect = async function (answer, selectIndex, taskId) {
      await window.async_wait();
      alert("成功请求-完成某个选择题");
      alert(`您的初始答案是${answer}`);
      alert(`这是本任务中第${selectIndex}个选择题`);
      alert(`任务id是${taskId}`);
      window.unityInstance.SendMessage("CallBack", "Js_fdldq_completeOneSelect_callBack", JSON.stringify({
        code: "000000",
        msg: "成功完成决策",
        data: ""
      }));
    }

    // 获取某个线索
    window.js_fdldq_getOneClue = async function (clue, clueIndex, selectIndex, taskId) {
      await window.async_wait();
      alert("成功请求-获取某个线索")
      alert(`线索文字内容是${clue}`)
      alert(`这是本决策中第${clueIndex}个线索`)
      alert(`这是本任务中第${selectIndex}个选择题`)
      alert(`任务id是${taskId}`)
      window.unityInstance.SendMessage("CallBack", "Js_fdldq_getOneClue_callBack", JSON.stringify({
        code: "000000",
        msg: "成功获取线索",
        data: ""
      }));
    }

    // 完成某个任务
    window.js_fdldq_completeOneTask = async function (taskId) {
      await window.async_wait();
      alert("成功请求-完成某个任务")
      alert(`您已完成${taskId}任务`)
      window.unityInstance.SendMessage("CallBack", "Js_fdldq_completeOneTask_callBack", JSON.stringify({
        code: "000000",
        msg: "成功完成任务",
        data: ""
      }));
    }
  },
  methods: {
    toggleFull() {
      // window.unityInstance.SetFullscreen(1)
      screenfull.toggle().then(res => {
        this.screenAsFull = !this.screenAsFull
        if (this.screenAsFull) {
          // todo全屏时webgl显示
          window.unitySizeFixedOnFull()
        } else {
          window.unitySizeFixed(600, false)
        }
      })
    },
    setApiUrl(msg) {
      unityInstance.SendMessage("Main Camera", "SetApiAdress", "http://**************:8002/");
    },
    setLoginInfo(arg) {
      // 如果有accessToken 就直接登录
      let experimentSpace2021UserInfo = {
        accessToken: "123",
        username: "456",
        userDisName: ""
      }
      // 改为unity里面正式设置ticket的方法
      unityInstance.SendMessage("Main Camera", "Receive", JSON.stringify(experimentSpace2021UserInfo));
    }

  }
}
</script>

<style scoped lang="less">
.webgl-content {
  position: relative;

  #unity-progress {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    color: #fff;
    width: 150px;
    text-align: center;
    height: 0px;
    margin: auto;

    #scene-text {
      margin-top: -20px;
      margin-bottom: 5px;
      font-size: 20px;
    }
  }

}

.full-btn {
  position: absolute;
  top: 50px;
  left: 50px;
}
</style>
