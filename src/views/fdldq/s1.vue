<template>
  <div class="page-container" v-show="screenDirection==='landscape'" id="fdldq-s1">
    <div id="three"></div>
  </div>

  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
  <div class="full-btn full-btn-landscape" @click="screenfull.toggle()"
       v-if="mobileType!=='iPhone'&&screenDirection==='landscape'">
    <img src="@/assets/ui/fdldq/common/full-btn-icon.png" alt="">
    <span>全屏</span>
  </div>
</template>

<script>
import {msg_err_vant,} from "../../utils/nut_component";
import screenfull from 'screenfull'
import {unity_fdldq_chooseRole} from "../../scripts/fdldq/unityMethods";
import {TaskStore} from "../../store/TaskStore";
import {ScriptRecordModel} from "../../model/ScriptRecordModel";
import {TaskModel} from "../../model/TaskModel";
import {mapState} from "pinia";
import * as THREE from 'three'
import {GLTFLoader} from 'three/examples/jsm/loaders/GLTFLoader'
import {DRACOLoader} from 'three/examples/jsm/loaders/DRACOLoader'
import {CSS2DObject, CSS2DRenderer} from 'three/examples/jsm/renderers/CSS2DRenderer';
import {ThreeModel} from "../../model/ThreeModel";
import {EffectComposer} from "three/examples/jsm/postprocessing/EffectComposer.js"
import {RenderPass} from "three/examples/jsm/postprocessing/RenderPass.js"
import {OutlinePass} from "three/examples/jsm/postprocessing/OutlinePass.js"
import {ShaderPass} from "three/examples/jsm/postprocessing/ShaderPass.js"
import {FXAAShader} from "three/examples/jsm/shaders/FXAAShader.js"
import {showLoadingToast} from 'vant';
import {ToolsModel} from "../../model/ToolsModel";
import $ from 'jquery'

export default {
  name: "f_s1_View",
  components: {},
  computed: {
    ...mapState(TaskStore, {
      nowTaskId: store => store.nowTaskId,
      recordInfo: store => store.recordInfo,
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull,
      mobileType: store => store.mobileType,
    }),
  },
  watch: {
    // 监听是否全屏
    screenFull(newVal, oldVal) {

    },
    // 监听横竖屏
    screenDirection(newVal, oldVal) {
      if (newVal === "landscape") {// 如果切换为横屏
        // 如果实验已加载就设置分辨率
        if (!this.three.loaded) {
          setTimeout(() => {
            this.ThreeMethods().load()
          }, 1000) // 切换横竖屏后，要等待一段时间才能获取到innerWidth
        } else {
          if (window.css2DRender) {
            $("#fdldq_s1_css2DRender").show()
            // 切换到横屏要重新设置css2d，不然点击失效
            window.css2DRender.setSize(window.innerWidth, window.innerHeight)
          }
        }
      }
      // 如果切换为竖屏竖屏，恢复metaViewPort
      if (newVal === "portrait") {
        ToolsModel.resetMetaViewport();
        if (window.css2DRender) {
          $("#fdldq_s1_css2DRender").hide()
        }
      }
    }
  },
  data() {
    return {
      publicPath: import.meta.env.BASE_URL,
      taskId: "S1",
      screenfull: screenfull,
      roleId: "r1",
      three: {
        loaded: false,
        INTERSECTED: null, // 暂存射线相交的物体（交互的模型对象）
        mouse: new THREE.Vector2(),
        xuFengObjects: [],
        liShiQiangObjects: []
      }
    }
  },
  async beforeCreate() {

  },
  async mounted() {
    // this.publicPath="https://resouce.cdzyhd.com/jbs/fe_dist/2023010308/"
    document.title = "S1-选择角色"
    let $this = this;
    // 关卡记录-不允许返回
    // await TaskModel.judgeOneTaskIsCompletedOrJumpBack("S1")

    // 如果是横屏才加载
    if (this.screenDirection === "landscape") {
      await $this.ThreeMethods().load()
    }
  },
  beforeUnmount() {
    this.ThreeMethods().beforeExit()
  },
  beforeRouteLeave() {
    this.ThreeMethods().beforeExit()
  },
  methods: {
    ThreeMethods() {
      let $this = this;
      return {
        // 开始加载
        async load() {
          // 如果是在本页才加载，避免生命周期错误 todo 优化逻辑
          if (window.location.href.indexOf("fdldq/s1") !== -1) {
            showLoadingToast({
              message: '加载中...',
              forbidClick: true,
            });
            $this.three.loaded = true
            await $this.ThreeMethods().init()
          }
        },
        // 初始化
        async init() {
          // WebGL渲染器
          const width = window.innerWidth, height = window.innerHeight;
          const renderer = new THREE.WebGLRenderer()
          renderer.setSize(width, height)
          renderer.setPixelRatio(window.devicePixelRatio)
          document.getElementById('three').appendChild(renderer.domElement)

          // 场景
          const scene = new THREE.Scene()
          $this.three.clock = new THREE.Clock()

          // 光
          const ambient = new THREE.AmbientLight(0xffffff, 0.5),
              light1 = new THREE.PointLight(0xffffff, 0.4),
              light2 = new THREE.PointLight(0xffffff, 0.4)
          const hemiLight = new THREE.HemisphereLight(0xffffff, 0x444444)
          scene.add(ambient)
          light1.position.set(200, 300, 400)
          scene.add(light1)
          light2.position.set(-200, -300, -400)
          scene.add(light2)
          scene.add(hemiLight)

          // 相机
          const camera = new THREE.PerspectiveCamera(60, width / height, 0.3, 1000)
          camera.position.set(-5, 3, -18)
          camera.lookAt(0, 0, 0)
          camera.layers.enableAll();
          camera.layers.toggle(1);

          // 辅助坐标轴
          const axesHelper = new THREE.AxesHelper(100)
          // scene.add(axesHelper)

          // 加载徐峰模型
          ThreeModel.loadGltfDraco($this.publicPath+"models/fdldq/gltf/s1/xufeng.glb").then(gltf => {
            let model = gltf.scene
            model.traverse(async function (object) {
              if (object.isMesh) {
                object.roleId = "xufeng"
                $this.three.xuFengObjects.push(object)
                // 枪和身体分开渲染
                if (object.name === "polySurface2_1" || object.name === "polySurface2_2") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath+"textures/fdldq/s1/38_qiang.png")
                  })
                } else {
                  object.material = new THREE.MeshStandardMaterial({
                    map: await ThreeModel.loadTexture($this.publicPath+"textures/fdldq/s1/xufeng.jpg")
                  })
                }
              }
            })
            model.scale.set(50, 50, 50)
            model.rotation.set(Math.PI / 20, Math.PI / 0.98, 0)
            model.position.set(-7, -4, -3)
            scene.add(model)
            // 模型动画
            window.mesh1 = model
            let clip = gltf.animations[0]
            let mixer = new THREE.AnimationMixer(model)
            let action = mixer.clipAction(clip)
            window.mixer1 = mixer
            action.play()
            // 姓名标签-徐峰
            let xueFengDiv = document.createElement('div');
            xueFengDiv.className = 'label label-r2';
            xueFengDiv.textContent = '徐峰';
            let xueFengLabel = new CSS2DObject(xueFengDiv);
            xueFengLabel.position.set(-0.01, 0.15, 0.05);
            model.add(xueFengLabel);
          })

          // 模型加载-李世强
          ThreeModel.loadGltfDraco($this.publicPath+"models/fdldq/gltf/s1/lishiqiang.glb").then(gltf => {
            let model = gltf.scene
            model.traverse(function (object) {
              if (object.isMesh) {
                object.roleId = "lishiqiang"
                $this.three.liShiQiangObjects.push(object)
                if (object.name === "polySurface2") {
                  object.material = new THREE.MeshStandardMaterial({
                    map: new THREE.TextureLoader().load($this.publicPath+"textures/fdldq/s1/38_qiang.png")
                  })
                } else {
                  object.material = new THREE.MeshStandardMaterial({
                    map: new THREE.TextureLoader().load($this.publicPath+"textures/fdldq/s1/lishiqiang.jpg")
                  })
                }
              }
            })
            model.scale.set(5.7, 5.7, 5.7)
            model.rotation.set(Math.PI / 50, Math.PI * 1.15, 0)
            model.position.set(6, -4, -6.5)
            scene.add(model)
            window.mesh2 = model
            let clip = gltf.animations[0]
            let mixer = new THREE.AnimationMixer(model)
            let action = mixer.clipAction(clip)
            window.mixer2 = mixer
            action.play()
            // 角色姓名标签-李世强
            let liShiQiangDiv = document.createElement('div');
            liShiQiangDiv.className = 'label label-r1';
            liShiQiangDiv.textContent = '李世强';
            let liShiQiangLabel = new CSS2DObject(liShiQiangDiv);
            liShiQiangLabel.position.set(-0.2, 1.35, 0.05);
            model.add(liShiQiangLabel);
            setTimeout(() => {
              // 默认渲染李世强
              $this.ThreeMethods().outLineRole("lishiqiang")
            }, 1000)
          })

          // 场景背景-天空盒
          let texture = new THREE.TextureLoader().load($this.publicPath+"textures/fdldq/s1/sky.jpg")
          texture.mapping = THREE.EquirectangularReflectionMapping;
          scene.background = texture;


          // css2D渲染器
          let css2DRender = new CSS2DRenderer();
          css2DRender.setSize(width, height)
          css2DRender.domElement.style.position = 'absolute';
          // css2DRender.domElement.style.zIndex = -1
          css2DRender.domElement.style.top = '0px';
          css2DRender.domElement.style.touchAction = "none"
          css2DRender.domElement.id = "fdldq_s1_css2DRender"
          document.body.appendChild(css2DRender.domElement);

          // 创建确定选择按钮
          let chooseBtn = document.createElement("button")
          chooseBtn.id = "chooseRoleBtn"
          chooseBtn.textContent = "选定角色并开始"
          css2DRender.domElement.appendChild(chooseBtn)
          chooseBtn.addEventListener("click", function () {
            $this.clickChooseRoleBtn()
          })

          // 视角控制器
          // const controls = new OrbitControls(camera, css2DRender.domElement)

          // window全局变量-避免渲染错误
          window.css2DRender = css2DRender
          window.renderer = renderer
          window.scene = scene
          window.camera = camera
          window.raycaster = new THREE.Raycaster();


          // 等待一段时间后开始渲染
          setTimeout(() => {
            $this.ThreeMethods().render()
            // 屏幕尺寸改变监听
            window.addEventListener('resize', $this.ThreeMethods().onWindowResize);
            // 点击事件监听
            $this.ThreeMethods().listenOnScreenClick()
          }, 1000)
        },

        // 渲染器
        render() {
          let delta = $this.three.clock.getDelta()
          window.renderId = requestAnimationFrame($this.ThreeMethods().render)
          window.renderer.render(window.scene, window.camera)
          window.css2DRender.render(window.scene, window.camera);
          if ($this.roleId === "r2") {
            if (window.mixer1) {
              window.mixer1.update(delta)
            }
          }
          if ($this.roleId === "r1") {
            if (window.mixer2) {
              window.mixer2.update(delta)
            }
          }
          if (window.composer) {
            window.composer.render()
          }

        },
        // 屏幕点击
        onScreenClick: function (event) {
          event.preventDefault();
          // 将鼠标位置归一化为设备坐标。x 和 y 方向的取值范围是 (-1 to +1)
          // renderer为three的渲染器
          let px = window.renderer.domElement.getBoundingClientRect().left;
          let py = window.renderer.domElement.getBoundingClientRect().top;
          $this.three.mouse.x = ((event.clientX - px) / (window.renderer.domElement.offsetWidth)) * 2 - 1;
          $this.three.mouse.y = -((event.clientY - py) / (window.renderer.domElement.offsetHeight)) * 2 + 1;
          // 通过摄像机和鼠标位置更新射线
          window.raycaster.setFromCamera($this.three.mouse, window.camera);
          // 计算物体和射线的焦点
          let intersects = window.raycaster.intersectObjects([window.mesh1, window.mesh2], true);
          if (intersects.length > 0) {
            let roleId = intersects[0].object.roleId
            console.log(roleId)
            $this.ThreeMethods().outLineRole(roleId)
          } else {
          }
        },
        // 选择角色高亮
        outLineRole(roleId) {
          if (roleId === "xufeng") {
            if ($this.mobileType !== "iPhone") { // iphone手机不显示轮廓 避免崩溃
              $this.ThreeMethods().outlineObj($this.three.xuFengObjects)
            }
            $this.roleId = "r2"
            document.querySelector(".label-r2").className = "label label-r2 label-active"
            document.querySelector(".label-r1").className = "label label-r1"
          }
          if (roleId === "lishiqiang") {
            if ($this.mobileType !== "iPhone") { // iphone手机不显示轮廓 避免崩溃
              $this.ThreeMethods().outlineObj($this.three.liShiQiangObjects)
            }
            $this.roleId = "r1"
            document.querySelector(".label-r1").className = "label label-r1 label-active"
            document.querySelector(".label-r2").className = "label label-r2"
          }
        },
        // 开始监听屏幕点击
        listenOnScreenClick: function () {
          window.screenClickListenFn = (e) => {
            $this.ThreeMethods().onScreenClick(e);
          }
          window.addEventListener('click', window.screenClickListenFn);
        },
        // 高亮显示模型
        outlineObj(selectedObjects) {
          // 创建一个EffectComposer（效果组合器）对象，然后在该对象上添加后期处理通道。
          let composer = new EffectComposer(window.renderer)
          // 新建一个场景通道  为了覆盖到原理来的场景上
          let renderPass = new RenderPass(window.scene, window.camera)
          composer.addPass(renderPass);
          // 物体边缘发光通道
          let outlinePass = new OutlinePass(new THREE.Vector2(window.innerWidth, window.innerHeight), window.scene, window.camera, selectedObjects)
          outlinePass.selectedObjects = selectedObjects
          outlinePass.edgeStrength = 15.0 // 边框的亮度
          outlinePass.edgeGlow = 2// 光晕[0,1]
          outlinePass.usePatternTexture = false // 是否使用父级的材质
          outlinePass.edgeThickness = 2.0 // 边框宽度
          outlinePass.downSampleRatio = 1 // 边框弯曲度
          outlinePass.visibleEdgeColor.set(parseInt(0xff0000)) // 呼吸显示的颜色
          outlinePass.clear = true
          composer.addPass(outlinePass)
          // 自定义的着色器通道 作为参数
          let effectFXAA = new ShaderPass(FXAAShader)
          effectFXAA.uniforms.resolution.value.set(0.25 / window.innerWidth, 0.25 / window.innerHeight) // 决定选择后屏幕清晰度，1会变模糊
          effectFXAA.renderToScreen = true
          composer.addPass(effectFXAA)
          window.composer = composer
        },
        // 当屏幕尺寸改变
        onWindowResize() {
          window.renderer.setSize(window.innerWidth, window.innerHeight)
          window.css2DRender.setSize(window.innerWidth, window.innerHeight)
          window.camera.aspect = window.innerWidth / window.innerHeight;
          window.camera.updateProjectionMatrix();
        },
        //退出前清理
        beforeExit() {
          if (!window.scene) {
            return
          }
          cancelAnimationFrame(window.renderId);// Stop the animation
          // 消除threejs和全局变量
          if (document.querySelector("#fdldq_s1_css2DRender")) {
            document.querySelector("#fdldq_s1_css2DRender").remove()
          }
          delete window.scene
          delete window.renderer
          delete window.scene
          delete window.camera
          delete window.raycaster
          delete window.mesh1
          delete window.mesh2
          delete window.composer
          delete window.renderId
          window.removeEventListener('click', window.screenClickListenFn);
          delete window.css2DRender
          delete window.screenClickListenFn
          console.log("清理完成")
        }
      }
    },
    // 点击确定选择角色按钮
    clickChooseRoleBtn() {
      if (!this.roleId) {
        msg_err_vant("请先选择角色！")
        return
      }
      unity_fdldq_chooseRole(this)
      window.js_fdldq_chooseRole(this.roleId)
    },
    // 设置角色
    async onChooseRole(roleId) {
      let taskStore = TaskStore()
      taskStore.$patch({
        roleId: roleId
      })
      // 保存角色选择结果到后端
      if (!(await ScriptRecordModel.updateRecordInfo("roleId", roleId))) {
        return
      }
      // 完成该关
      if (!(await TaskModel.completeOneTask("S1"))) {
        return
      }
      // 跳转到主界面
      // this.$router.push({
      //   name: "AR_Main_View",
      //   query: {}
      // })
      window.location.href = "/ar_main"
    }
  }
}
</script>
<style>
/*动态添加的dom*/
#fdldq_s1_css2DRender .label {
  color: #fff;
  font-size: 7px;
}

#fdldq_s1_css2DRender .label-active {
  color: #ff0000;
  font-size: 10px;
  font-weight: bold;
}

#fdldq_s1_css2DRender #chooseRoleBtn {
  position: fixed;
  margin: auto;
  left: 0;
  right: 0;
  bottom: 10px;
  width: 70px;
  padding: 5px 10px;
  color: #555;
  font-size: 6px;
  border-radius: 10px;
  font-weight: bold;
  border: 0px;
}
</style>
<style scoped lang="less">

.page-container {
  background-color: rgba(0, 0, 0, 0.6);
  min-height: 100vh;
  color: #fff;

  .task-title {
    text-align: center;
    font-size: 10px;
    color: #fff;
  }
}
</style>
