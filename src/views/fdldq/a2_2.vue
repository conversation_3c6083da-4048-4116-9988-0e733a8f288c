<template>
  <div class="page-container" v-show="screenDirection==='landscape'" id="fdldq-a2_2">
<!--    <div class="title">{{ taskConfig["taskId"] }}-{{ taskConfig["name"] }}</div>-->
    <ar-text-panel :textInfo="textInfo" @onClickSureBtn="clickCompleteTaskBtn"></ar-text-panel>
  </div>
  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
  <div class="full-btn full-btn-landscape" @click="screenfull.toggle()" v-if="mobileType!=='iPhone'&&screenDirection==='landscape'">
    <img src="@/assets/ui/fdldq/common/full-btn-icon.png" alt="">
    <span>全屏</span>
  </div>
</template>

<script>
import {toast_err} from "../../utils/nut_component";
import screenfull from 'screenfull'
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";
import {TaskModel} from "../../model/TaskModel";
import {
  unity_fdldq_completeOneSelect,
  unity_fdldq_completeOneTask, unity_fdldq_getOneClue
} from "../../scripts/fdldq/unityMethods";
import arTextPanel from "@/views/components/scene/ar-textPanel.vue"
import {ToolsModel} from "../../model/ToolsModel";
import {ArCameraModel} from "../../model/easyAR/ArCameraModel";

export default {
  name: "f_a2_2_View",
  components: {arTextPanel},
  computed: {
    ...mapState(TaskStore, {
      nowTaskId: store => store.nowTaskId,
      recordInfo: store => store.recordInfo,
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull,
      mobileType: store => store.mobileType,
    }),
  },
  watch: {
    // 监听是否全屏
    screenFull(newVal, oldVal) {
      // 切换video猖狂
      this.arCameraModel.videoElement.setAttribute('width', `${window.innerWidth}px`);
      this.arCameraModel.videoElement.setAttribute('height', `${window.innerHeight}px`);
    },
    // 监听横竖屏
    screenDirection(newVal, oldVal) {
      if (newVal === "landscape") {// 如果切换为横屏
        if (!this.arCameraModel) {// 如果尚未设置宽高
          this.arCameraModel = new ArCameraModel("fdldq-a2_2")
          this.arCameraModel.openCameraInPage()
        }
      }
    }
  },
  data() {
    return {
      arCameraModel:undefined,
      screenfull: screenfull,
      directionRight: true,
      taskId: "A2",
      taskConfig: {},
      taskInfo: {},
      textInfo: {content: ""}
    }
  },
  async mounted() {
    // 关卡记录-不允许返回
    TaskModel.judgeOneTaskIsCompletedOrJumpBack(this.taskId)

    // 如果加载时是横屏
    if (this.screenDirection === "landscape") { // 显示ar视频背景
      this.arCameraModel = new ArCameraModel("fdldq-a2_2")
      this.arCameraModel.openCameraInPage()
    }

    // 关卡信息初始化
    this.taskConfig = TaskModel.getOneTaskConfig(this.taskId)
    this.taskInfo = this.recordInfo[this.nowTaskId]

    this.textInfo = this.taskConfig['childTaskInfo']['lists'][1]["textList"][0]

    let $this = this;
    document.title = this.taskConfig["taskId"] + this.taskConfig["name"]

    this.regUnityMethods()
  },
  methods: {
    // 注册unity全局方法
    regUnityMethods() {
      unity_fdldq_completeOneTask(this)
    },
    // 点击完成任务按钮
    async clickCompleteTaskBtn() {
      this.completeTask()
    },
    // 完成任务
    async completeTask() {
      if (await TaskModel.markTaskDoing_completeOneChildTask(this.nowTaskId, 1)) {
        // 跳转到主界面
        // this.$router.push({
        //   name: "f_a2_3_View",
        //   query: {}
        // })
        window.location.href="/fdldq/a2_3"
      }
    }
  }
}
</script>

<style scoped lang="less">
.page-container {
  background-color: rgba(0, 0, 0, 0.6);
  min-height: 100vh;
  padding: 10px;
}

.title {
  text-align: center;
  font-size: 10px;
}

.buttons {
  margin-top: 50px;
}
</style>
