<template>
  <div class="webgl-content">
    <div id="unityContainer" style="height: 100vh"></div>
    <van-button type="success" class="full-btn" @click="toggleFull">全屏</van-button>
  </div>
</template>
<link rel="stylesheet" href="">
<script>
import {UnityModel} from "../../model/unity/UnityModel";
import $ from "jquery"
import screenfull from 'screenfull'
import {msg_err} from "../../utils/nut_component";

export default {
  name: "f_1_1View",
  data() {
    return {}
  },
  mounted() {
    let unityPath = "http://resouce.cdzyhd.com/experiment/webgl/sdcs/xicai/2022051901/"
    UnityModel.loadWebGlResource2019(unityPath).then(loaded => {
      if (loaded) {
        $("#unityContainer").width(window.screen.width + "px");
        window.unityInstance = UnityLoader.instantiate("unityContainer", unityPath + "Build/SiDuChiShui.json", {});
      }
    })
    window.setApiUrl = this.setApiUrl;
    window.setLoginInfo=this.setLoginInfo;
    window.setUserInfo=function (){

    }
  },
  methods: {
    toggleFull() {
      screenfull.toggle()
    },
    setApiUrl(msg) {
      unityInstance.SendMessage("Main Camera", "SetApiAdress", "http://**************:8002/");
    },
    setLoginInfo(arg) {
      // 如果有accessToken 就直接登录
      let experimentSpace2021UserInfo = {
        accessToken: "123",
        username: "456",
        userDisName: ""
      }
      // 改为unity里面正式设置ticket的方法
      unityInstance.SendMessage("Main Camera", "Receive", JSON.stringify(experimentSpace2021UserInfo));
    }

  }
}
</script>

<style scoped lang="less">
.full-btn {
  position: absolute;
  top: 50px;
  left: 50px;
}
</style>
