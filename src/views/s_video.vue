<template>
  <div class="video-container" v-show="directionRight">
    <div id="mui-player"></div>
  </div>

  <van-button type="danger" size="small" class="full-btn" @click="screenfull.toggle()">全屏</van-button>
</template>

<script>
import {msg_err, msg_err_vant, msg_success_vant, toast_err} from "../utils/nut_component";
import {Button} from 'vant';
import {ref, onBeforeUpdate} from 'vue';
import screenfull from 'screenfull'
import 'mui-player/dist/mui-player.min.css'
import MuiPlayer from 'mui-player'
import {isIphone} from "../utils/common";

export default {
  name: "S_Video_View",
  components: {},
  data() {
    return {
      screenfull: screenfull,
      directionRight: true,
      videoInfo: {
        url: "http://resouce.cdzyhd.com/680ce718-c232-43bb-8cbc-4938a767722a.mp4"
      }
    }
  },
  mounted() {
    let $this = this;
    window.addEventListener('orientationchange', function () {
      let direction = window.neworientation.current
      if (direction === "landscape") {
        $this.directionRight = true
      } else {
        $this.directionRight = false;
        toast_err("请切换到横屏使用本程序!")
      }
    }, false);

    let mp = new MuiPlayer({
      container: '#mui-player',
      // height: window.screen.height,// todo iphone适配
      height:"300",
      autoFit: false,
      videoAttribute: [
        {attrKey: 'webkit-playsinline', attrValue: 'webkit-playsinline'},
        {attrKey: 'playsinline', attrValue: 'playsinline'},
        {attrKey: 'x5-video-player-type', attrValue: 'h5-page'},
      ],
      src: 'http://resouce.cdzyhd.com/680ce718-c232-43bb-8cbc-4938a767722a.mp4',
    })
    mp.video().onended = () => {
      msg_success_vant("播放完成")
    };
  },
  methods: {}
}
</script>

<style scoped lang="less">
.full-btn {
  position: fixed;
  left: 20px;
  bottom: 0px;
}

.video-container {
  background-color: rgba(0, 0, 0, 0.6);
  min-height: 100vh;

  #mui-player {
    width: 100%;
    height: 100vh;
  }
}

.button-box {
  margin-top: 10px;
  text-align: center;

  .van-button {
    margin-right: 30px;
  }
}
</style>
