<template>
  <div class="select-container" v-show="directionRight">
    <div class="title">{{
        selectQuestionInfo.title
      }}{{ selectQuestionInfo.maxSelectNumber > 1 ? '（多选）' : '（单选）' }}
    </div>
    <div class="options">
      <div class="option-li flex flex-start" v-for="(item,index) in selectQuestionInfo.options"
           @click="selectOption(index)">
        <van-icon size="1.5rem" class="square" name="passed" v-show="selectQuestionInfo.userAnswer.indexOf(index)!==-1"
        />
        <van-icon size="1.5rem" class="square" name="circle"
                  v-show="selectQuestionInfo.userAnswer.indexOf(index)===-1"/>
        <div>{{ item.content }}</div>
      </div>
    </div>
    <div class="button-box">
      <van-button type="primary" size="normal" @click="clickShowClueBtn">
        查看线索（{{ this.selectQuestionInfo.clueList.length }}条）
      </van-button>
      <van-button type="success" size="normal" @click="clickSureChooseBtn">确认选择</van-button>
    </div>
    <van-popup v-model:show="clueShow" position="right" :style="{ width: '60%',height:'100%' }">
      <van-collapse v-model="clueCollapse">
        <van-collapse-item v-for="(item,index) in selectQuestionInfo.clueList" :title="'线索'+(index+1)" :name="index">
          {{ item.content }}
        </van-collapse-item>
      </van-collapse>
    </van-popup>
  </div>
  <van-button type="danger" size="small" class="full-btn" @click="screenfull.toggle()">全屏</van-button>
</template>

<script>
import {msg_err, msg_err_vant, msg_success_vant, toast_err} from "../utils/nut_component";
import {Button} from 'vant';
import {ref, onBeforeUpdate} from 'vue';
import screenfull from 'screenfull'

export default {
  name: "S_Select_View",
  components: {},
  data() {
    return {
      screenfull: screenfull,
      directionRight: true,
      selectQuestionInfo: {
        maxSelectNumber: 1,
        title: "现在战士们还是拂晓前吃的饭，跑了这么多路，又一路打仗，肚子饿得难受，在此时接到缩短行军时间的红四团部队应该如何应对这条命令？",
        options: [
          {
            content: "A. 一天行军240里是不可能的，只能尽力而为，需要提前做好正面打一场硬仗的准备。"
          },
          {
            content: "B. 让骡马冲在前面吸引敌人的火力，部队全速奔袭。"
          },
          {
            content: "C. 除了重机枪，尽可能丢弃所有缴获的行李担子，全速奔袭。"
          },
          {
            content: "D. 丢弃重机枪等武器，带上食物补给，一边吃东西一边全速奔袭。"
          }
        ],
        answer: [2],
        userAnswer: [],
        clueList: [
          {
            content: "线索1：辎重太多会降低行军速度。"
          },
          {
            content: "线索2：若规定时间赶不到泸定桥，敌人大量增援就会提前正面布防。"
          },
          {
            content: "线索3：丢弃行李和食物长途奔袭，大概率会导致有红军在途中体力不支牺牲。"
          },
          {
            content: "线索4：武器装备是胜利的关键。"
          },
        ],
      },
      clueShow: false,
      clueCollapse: [],
    }
  },
  mounted() {
    let $this = this;
    window.addEventListener('orientationchange', function () {
      let direction = window.neworientation.current
      if (direction === "landscape") {
        $this.directionRight = true
      } else {
        $this.directionRight = false;
        toast_err("请切换到横屏使用本程序!")
      }
    }, false);
  },
  methods: {
    // 选择某个选项
    selectOption(index) {
      let findIndex = this.selectQuestionInfo.userAnswer.indexOf(index);
      if (findIndex === -1) {
        if (this.selectQuestionInfo.maxSelectNumber === 1) {// 单选
          this.selectQuestionInfo.userAnswer = []
          this.selectQuestionInfo.userAnswer.push(index)
        } else {
          this.selectQuestionInfo.userAnswer.push(index)
        }

      } else {
        this.selectQuestionInfo.userAnswer.splice(findIndex, 1)
      }
    },
    // 点击查看线索按钮
    clickShowClueBtn() {
      this.clueShow = true;
    },
    // 点击确认选择按钮
    clickSureChooseBtn() {
      if(this.selectQuestionInfo.userAnswer.length>0){
        // 答案判断逻辑
        let result=true
        this.selectQuestionInfo.answer.forEach(needAnswer=>{
          if(this.selectQuestionInfo.userAnswer.indexOf(needAnswer)===-1){
            result=false
          }
        })
        if(result){
          msg_success_vant("答案正确")
        }else{
          msg_err_vant("答案错误")
        }
      }else{
        msg_err_vant("请先选择您的答案")
        return
      }
    },
  }
}
</script>

<style scoped lang="less">
.full-btn {
  position: fixed;
  left: 20px;
  bottom: 0px;
}

.select-container {
  background-color: rgba(0, 0, 0, 0.6);
  min-height: 100vh;
  padding: 10px;

  .title {
    color: #fff;
    font-size: 8px;
    line-height: 10px;
    text-indent: 2em;
    font-weight: bold;
  }

  .options {
    margin-top: 5px;
    padding: 0px 5px;
    color: #fff;

    .option-li {
      margin-bottom: 6px;
      font-size: 7.5px;

      .square {
        margin-right: 5px;
      }
    }
  }
}

.button-box {
  margin-top: 10px;
  text-align: center;

  .van-button {
    margin-right: 30px;
  }
}
</style>
