<template>
  <div v-show="screenDirection==='portrait'" class="hall-container">
    <pageHeader title-name="任务大厅" :showLeft="false"></pageHeader>
    <pageFooter :active-number="0"></pageFooter>
    <nut-swiper :pagination-visible="true" pagination-color="#426543" auto-play="4000" :is-preventDefault="false">
      <template v-for="item in focusList" :key="item.name">
        <nut-swiper-item v-if="item.img" @click="clickFocusLi(item)">
          <img :src="item.img" alt=""/>
        </nut-swiper-item>
      </template>
    </nut-swiper>
    <nut-tabs v-model="seriesData.tabIndex" title-scroll title-gutter="10" class="script-box">
      <nut-tabpane v-for="series in seriesData.list" :title="series.name">
        <div class="script-li flex flex-between" v-for="script in series.scriptList"
             @click="$router.push('/scriptInfo?id='+script.scriptId)">
          <nut-image class="avatar" :src="script.avatarUrl" fit="cover"></nut-image>
          <div class="info-box">
            <div class="name">{{ script.name }}</div>
            <div class="tags flex flex-start">
              <nut-tag class="tag" color="#E9E9E9" textColor="#999999" v-for="tag in script.tags">{{ tag }}</nut-tag>
            </div>
            <div class="des">{{ script.shortDesText }}</div>
            <div class="address">支持地点：{{ script.addressText }}</div>
          </div>
        </div>
        <div v-if="series.scriptList.length===0" class="empty-box">暂无此类剧本</div>
      </nut-tabpane>
    </nut-tabs>
    <nut-divider dashed class="more-tip">更多精彩剧本，敬请期待</nut-divider>
  </div>
  <div class="direction-tip" v-show="screenDirection==='landscape'">请切换到竖屏使用</div>
</template>

<script>
import {reactive, toRefs} from 'vue';
import pageFooter from "@/views/components/pageFooter.vue";
import pageHeader from "@/views/components/pageHeader.vue";
import {MainStore} from "../store/MainStore";
import {ScriptSeriesModel} from "../model/ScriptSeriesModel";
import {ScriptModel} from "../model/ScriptModel";
import {TabPane} from "@nutui/nutui";
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";

export default {
  name: "hall",
  components: {pageFooter, pageHeader, TabPane},
  setup() {

  },
  computed: {
    ...mapState(TaskStore, {
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  data() {
    return {
      focusList: [],
      seriesData: {
        tabIndex: '0',
        list: []
      }
    }
  },
  async beforeCreate() {
    // 获取大厅配置
    const mainStore = MainStore();
    await mainStore.getHallConfig();
    this.focusList = mainStore.hallConfig.focusList;
  },
  async mounted() {
    document.title = "AR红色数字剧坊-任务大厅"
    // 获取系列和系列下面剧本列表
    let scriptSeriesList = await ScriptSeriesModel.getList({});
    for (let i = 0; i < scriptSeriesList.length; i++) {
      let scriptList = await ScriptModel.getList({scriptSeriesId: scriptSeriesList[i]["scriptSeriesId"]})
      scriptSeriesList[i]["scriptList"] = scriptList
    }
    this.seriesData.list = scriptSeriesList
  },
  methods: {
    // 点击轮播图的某一项
    clickFocusLi(item) {
      if (item.url) {
        if (item.url.indexOf("http") > -1) { // 链接里有http
          window.location.href = item.href

        } else {
          this.$router.push(item.url)
        }
      }
    }
  }

}
</script>
<style>
.hall-container .nut-tabs__titles {
  background-color: #fff !important;
  border: 1px solid #e6e6e6;
  margin-bottom: 10px;
}

.hall-container .nut-tabs__content {
  border: 1px solid #e6e6e6;
}
.script-box .nut-tabpane{
  padding:10px;
}
</style>
<style scoped lang="less">
.hall-container {
  background-color: #f2f2f2;
  min-height: 100vh;
}

.nut-swiper-item {
  line-height: 150px;

  img {
    width: 100%;
    height: 100%;
  }
}

.script-box {
  width: 90%;
  margin: 0 auto;
  margin-top: 10px;
}

// 剧本列表
.script-li {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #cecece;

  &:last-child {
    border-bottom: none;
  }

  .avatar {
    display: inline-block;
    width: 70px;
    box-shadow: 1px 1px 3px 1px rgba(0, 0, 0, 0.2);
  }

  .info-box {
    width: 230px;
    color: #333;
  }

  .name {
    font-size: 16px;
  }

  .tag {
    margin-top: 5px;
    margin-right: 5px;
    font-size: 11px;
  }

  .des {
    margin-top: 5px;
    font-size: 12px;
    line-height: 16px;
    color: #666;
  }

  .address {
    margin-top: 5px;
    font-size: 12px;
    line-height: 16px;
    color: #666;
  }
}
.script-li:last-child{
  margin-bottom: 0px;
  padding-bottom: 0px;
}
.empty-box{
  text-align: center;
  font-size: 13px;
  color: #888;
}

.more-tip {
  width: 80%;
  margin: 0 auto;
  margin-top: 120px;
  font-size: 14px;
  color: #888;
}

.direction-tip {
  line-height: 100vh;
  text-align: center;
  font-size: 25px;
  color: #333;
}
</style>
