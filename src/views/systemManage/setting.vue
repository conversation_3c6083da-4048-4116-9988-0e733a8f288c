<template>
  <div class="app-container">
<!--    <el-card style="margin-bottom: 20px">-->
<!--      <div slot="header" class="clearfix">-->
<!--        <span>网站信息</span>-->
<!--        <el-button style="margin-left: 10px; padding: 3px 0" type="text"-->
<!--                   @click="webConfig.cardShow=!webConfig.cardShow">-->
<!--          <i class="el-icon-arrow-up" v-show="webConfig.cardShow"></i>-->
<!--          <i class="el-icon-arrow-down" v-show="!webConfig.cardShow"></i>-->
<!--          {{ webConfig.cardShow ? '收起' : '展开' }}-->
<!--        </el-button>-->
<!--        <el-button style="float:right" size="small"-->
<!--                   @click="clickSaveBtn('webConfig',webConfig.config,'webConfigManage')">保存-->
<!--        </el-button>-->
<!--      </div>-->
<!--      <div class="container" v-show="webConfig.cardShow">-->
<!--        <el-form ref="webConfigManage" :model="webConfig.config" :rules="webConfig.formRules" label-width="120px">-->
<!--          <el-form-item label="网站名称:" prop="webName">-->
<!--            <el-input v-model="webConfig.config.webName"></el-input>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="网站备案号:" prop="beiAnText">-->
<!--            <el-input v-model="webConfig.config.beiAnText"></el-input>-->
<!--          </el-form-item>-->
<!--        </el-form>-->
<!--      </div>-->
<!--    </el-card>-->
<!--    <el-card style="margin-bottom: 20px">-->
<!--      <div slot="header" class="clearfix">-->
<!--        <span>系统设置</span>-->
<!--        <el-button style="margin-left: 10px; padding: 3px 0" type="text"-->
<!--                   @click="systemConfig.cardShow=!systemConfig.cardShow">-->
<!--          <i class="el-icon-arrow-up" v-show="systemConfig.cardShow"></i>-->
<!--          <i class="el-icon-arrow-down" v-show="!systemConfig.cardShow"></i>-->
<!--          {{ systemConfig.cardShow ? '收起' : '展开' }}-->
<!--        </el-button>-->
<!--        <el-button style="float:right" size="small"-->
<!--                   @click="clickSaveBtn('systemConfig',systemConfig.config,'systemConfigManage')">保存-->
<!--        </el-button>-->
<!--      </div>-->
<!--      <div class="container" v-show="systemConfig.cardShow">-->
<!--        <el-form ref="systemConfigManage" :model="systemConfig.config" :rules="systemConfig.formRules"-->
<!--                 label-width="140px">-->
<!--          <el-form-item label="注册功能:" prop="regFunction">-->
<!--            <el-select v-model="systemConfig.config.regFunction">-->
<!--              <el-option value="open" label="开放" key="open"></el-option>-->
<!--              <el-option value="close" label="关闭" key="close"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--          <el-form-item label="注册用户默认启用:" prop="regUserEnable">-->
<!--            <el-alert title="如果默认不启用，需管理员启用该用户后才能登录系统。" style="margin-bottom: 10px"></el-alert>-->
<!--            <el-select v-model="systemConfig.config.regUserEnable">-->
<!--              <el-option value="true" label="是" key="true"></el-option>-->
<!--              <el-option value="false" label="否" key="false"></el-option>-->
<!--            </el-select>-->
<!--          </el-form-item>-->
<!--        </el-form>-->
<!--      </div>-->
<!--    </el-card>-->
    <el-card style="margin-bottom: 20px" class="hall-box">
      <div slot="header" class="clearfix">
        <div>
          <span>任务大厅设置</span>
          <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                     @click="hallConfig.cardShow=!hallConfig.cardShow">
            <i class="el-icon-arrow-up" v-show="hallConfig.cardShow"></i>
            <i class="el-icon-arrow-down" v-show="!hallConfig.cardShow"></i>
            {{ hallConfig.cardShow ? '收起' : '展开' }}
          </el-button>
          <el-button style="float:right" size="small"
                     @click="clickSaveBtn('hallConfig',hallConfig.config,'hallConfigManage')">保存
          </el-button>
        </div>
      </div>
      <div class="card-container success-container" v-show="hallConfig.cardShow">
        <div class="focus-box">
          <div class="title" style="font-size: 15px;font-weight: bold;margin-bottom: 10px;">轮播设置</div>
          <div style="font-size: 13px;color: #999;margin-bottom: 10px;">
            图片大小2MB以内，图片分辨率750px*300px
          </div>
          <el-table :data="hallConfig.config.focusList" border fit
                    highlight-current-row
                    style="width: 100%;">
            <el-table-column label="名称" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.name"></el-input>
              </template>
            </el-table-column>
<!--            <el-table-column label="链接地址" align="center">-->
<!--              <template slot-scope="scope">-->
<!--                <el-input v-model="scope.row.url"></el-input>-->
<!--              </template>-->
<!--            </el-table-column>-->
            <el-table-column label="图片" align="center" width="300px">
              <template slot-scope="scope">
                <erp-uploader-one-pic :key="scope.row.id" v-if="scope.$index!==hallConfig.config.focusList.length-1"
                                      :img-in="scope.row.img"
                                      :uploader-id="'success_'+scope.row.id"
                                      @afterDelete="data=>HallMethods().afterDelete(data)"
                                      :show-des="false"
                                      :uploader-size="[188,100]" :pixel-limit="[300,160]"
                                      :size-limit="2048"
                                      @uploadSuccess="data=>HallMethods().fileUploadSuccess(data)"></erp-uploader-one-pic>
                <erp-uploader-one-pic :key="hallConfig.config.focusList[hallConfig.config.focusList.length-1].id"
                                      v-if="scope.$index===hallConfig.config.focusList.length-1"
                                      :img-in="hallConfig.config.focusList[hallConfig.config.focusList.length-1].img"
                                      :uploader-id="'success_'+hallConfig.config.focusList[hallConfig.config.focusList.length-1].id"
                                      @afterDelete="data=>HallMethods().afterDelete(data)"
                                      :show-des="false"
                                      :uploader-size="[188,100]" :pixel-limit="[300,160]"
                                      :size-limit="2048"
                                      @uploadSuccess="data=>HallMethods().fileUploadSuccess(data)"></erp-uploader-one-pic>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="250"
                             class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button type="primary" size="mini" round
                           v-show="scope.$index!==0&&scope.$index!==hallConfig.config.focusList.length-1"
                           @click="HallMethods().clickUpBtn(scope.row,scope.$index)">上移
                </el-button>
                <el-button type="primary" size="mini" round
                           v-show="scope.$index!==hallConfig.config.focusList.length-1&&scope.$index!==hallConfig.config.focusList.length-2"
                           @click="HallMethods().clickDownBtn(scope.row,scope.$index)">下移
                </el-button>
                <el-button type="danger" size="mini" round v-show="scope.$index!==hallConfig.config.focusList.length-1"
                           @click="HallMethods().clickDelBtn(scope.row,scope.$index)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

      </div>
    </el-card>
  </div>
</template>

<script>

import Tinymce from "@/components/Tinymce";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import {msg_confirm, msg_confirm_choose, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {validateMaxLength} from "@/utils/validate";
import {ConfigModel} from "@/model/ConfigModel";
import {find_obj_from_arr_by_id, randomNumber} from "@/utils/common";

export default {
  name: "systemSetting",
  components: {Tinymce, erpUploaderOnePic},
  data() {
    return {
      // 网站信息
      webConfig: {
        cardShow: true,
        config: {},
        formRules: {
          'webName': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 25, "网站名称"),
            trigger: 'blur'
          },
          'beiAnText': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "网站备案号"),
            trigger: 'blur'
          },
        }
      },
      // 系统设置
      systemConfig: {
        cardShow: false,
        config: {},
        formRules: {
          'regFunction': {required: true, message: '请选择', trigger: 'change'},
          'regUserEnable': {required: true, message: '请选择', trigger: 'change'},
        }
      },
      // 大厅设置
      hallConfig: {
        cardShow: false,
        config: {
          focusList: [{
            id: new Date().getTime(),
          }]
        },
      },
    }
  },
  async mounted() {
    // 获取配置信息
    ConfigModel.getConfig("webConfig").then(res => {
      this.$set(this.webConfig, "config", JSON.parse(res))
    })
    // ConfigModel.getConfig("systemConfig").then(res => {
    //   this.$set(this.systemConfig, "config", JSON.parse(res))
    // })
    ConfigModel.getConfig("hallConfig").then(res => {
      this.$set(this.hallConfig, "config", JSON.parse(res))
    })
  },
  methods: {
    // 点击保存按钮
    async clickSaveBtn(field, config, formRef) {
      if (this.$refs[formRef]) {
        this.$refs[formRef].validate(async validate => {
          if (validate) {
            // 保存接口
            if (await ConfigModel.editConfig(field, config)) {
              msg_success("保存成功")
            }
          }
        });
      } else {
        if (formRef === "hallConfigManage") {
          if (!this.HallMethods().beforeSaveCheck()) {
            return
          }
        }
        // 保存接口
        if (await ConfigModel.editConfig(field, config)) {
          msg_success("保存成功")
        }
      }
    },
    // 文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 任务大厅相关
    HallMethods() {
      let $this = this;
      return {
        // 点击删除按钮
        clickDelBtn(edit, index) {
          $this.hallConfig.config.focusList.splice(index, 1)
        },
        // 点击上移按钮
        clickUpBtn(edit, index) {
          let arr = []
          arr = $this.hallConfig.config.focusList
          arr[index] = arr.splice(index - 1, 1, arr[index])[0]
          $this.$set($this.hallConfig.config, "list", arr)
        },
        // 点击下移按钮
        clickDownBtn(edit, index) {
          let arr = []
          arr = $this.hallConfig.config.focusList
          arr[index] = arr.splice(index + 1, 1, arr[index])[0]
          $this.$set($this.hallConfig.config, "list", arr)
        },
        fileUploadSuccess(data) {
          console.log(data)
          let id = Number(data[0].replace("success_", ""))
          let index = find_obj_from_arr_by_id("id", id, $this.hallConfig.config.focusList)[0]
          console.log(index)
          if (!$this.hallConfig.config.focusList[index].hasOwnProperty('img')) {
            $this.hallConfig.config.focusList.push({
              id: new Date().getTime(),
            })
          }
          $this.$set($this.hallConfig.config.focusList[index], "img", data[1])
        },
        afterDelete(data) {
          let id = Number(data[0].replace("success_", ""))
          let index = find_obj_from_arr_by_id("id", id, $this.hallConfig.config.focusList)[0]
          $this.$set($this.hallConfig.config.focusList[index], "img", "")
        },
        // 保存时检测是否填写完整
        beforeSaveCheck() {
          let result = true
          if ($this.hallConfig.config.focusList.length < 2) {
            msg_err("至少上传一个成功案例")
            result = false
          }
          // 判断是否填写完整
          $this.hallConfig.config.focusList.forEach((li, index) => {
            if (index !== $this.hallConfig.config.focusList.length - 1) {
              if (!li.name) {
                msg_err(`第${index + 1}项未填写完整！`)
                result = false
              }
            }
          })
          return result
        }
      }
    },
  }
}
</script>

<style scoped lang="scss">
.introduce-box {
  .tools {
    margin-bottom: 20px;
  }
}
</style>
