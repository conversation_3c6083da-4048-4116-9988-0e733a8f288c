<template>
  <div class="app-container">
    <!--顶部按钮-->
    <div class="top-tools">
      <div style="text-align: right">
        <el-button class="el-button" type="success" style="background-color: #67C23A;border-color:#67C23A"
                   @click="ListMethods().clickAddBtn()"
        >新增问题
        </el-button>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="问题标题" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="问题类型" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.type }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="350"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑
          </el-button>
          <!--          <el-button type="danger" size="mini" round>删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <!--弹窗-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      :close-on-click-modal="false"
      width="1000px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="问题类型:" prop="type">
            <el-select v-model="entityInfo.edit.type"
                       style="width: 100%">
              <el-option v-for="item in entityInfo.filter.helpQuestionType" :value="item.value" :label="item.label"
                         :key="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="问题标题:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name">
              <div slot="suffix" v-if="entityInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 40
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="问题内容:" prop="content">
            <tinymce
              id="tinymce_content"
              ref="tinymce_content"
              v-model="entityInfo.edit.content"
              :height="300"
            />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()" v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery, objectToLVArr} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {validateMaxLength} from "@/utils/validate";
import {HelpQuestionModel} from "@/model/HelpQuestionModel";
import Tinymce from "@/components/Tinymce";

export default {
  name: "gradeManage",
  components: {ListSearchFilter, Tinymce},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    // 校检名称
    return {
      enums: enums,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },

      },
      userInfo: {},
      entityInfo: {
        dialog: false,
        title: "新增标签",
        type: "add",
        filter: {
          helpQuestionType: objectToLVArr(enums.helpQuestionType),
        },
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 50, "问题标题"), trigger: 'blur'},
          'type': {required: true, message: '请选择问题类型', trigger: 'change'},
          'content': {required: true, message: '请输入问题内容', trigger: 'change'},
        },
      },
    }
  },
  mounted() {
    // 获取列表
    this.ListMethods().getList({})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(query) {
          $this.lists.loading = true;
          $this.lists.list = await HelpQuestionModel.getList(query)
          $this.lists.loading = false
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {

        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增常见问题"
          $this.entityInfo.edit = {
            type: "常见问题",
            content:""
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
            window.tinymce.get("tinymce_content").setContent("")
          }, 500);
        },
        // 点击编辑按钮
        async clickEditBtn(entity) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑常见问题"
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
            window.tinymce.get("tinymce_content").setContent(entity.content)
          }, 500)
        },
      }
    },
    // 实体方法集
    EntityMethods() {
      let $this = this;
      return {
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有重名常见问题
              let listResult = await HelpQuestionModel.getList({
                name: $this.entityInfo.edit.name
              })
              if (listResult.length > 0) {
                msg_err("已存在同名的常见问题，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要新增该常见问题吗？')) {
                let result = await HelpQuestionModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList({})
                }
              }
            }
          });
        },
        // 点击编辑按钮
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有重名标签
              let listResult = await HelpQuestionModel.getList({
                name: $this.entityInfo.edit.name,
                helpId: {"$ne": $this.entityInfo.edit.helpId}
              })
              if (listResult.length > 0) {
                msg_err("已存在同名的常见问题，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要编辑该常见问题吗？')) {
                let result = await HelpQuestionModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList({})
                }
              }
            }
          });
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.entityInfo.dialog = false
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top-tools {
  margin-bottom: 15px;
}
</style>
