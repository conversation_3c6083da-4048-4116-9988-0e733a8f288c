<template>
  <div class="page-container" v-show="screenDirection==='landscape'">
    <div class="task-button-list flex flex-start flex-dr">
      <div class="text">选择任务，AR识别选择关卡</div>
      <div class="button">
        <van-button type="success" v-for="(item,index) in taskList" @click="clickOneTask(item)">
          {{ item.taskId }}-{{ item.name }}
        </van-button>
      </div>
    </div>
    <van-overlay :show="taskArImageShow" @click="taskArImageShow = false">
      <div class="flex flex-center">
        <van-image
            width="350px"
            height="350px"
            style="padding:50px;background-color: #fff;"
            :src="taskArImage"
        />
      </div>
    </van-overlay>
  </div>
  <div class="direction-tip" v-show="screenDirection==='portrait'">请切换到横屏使用</div>
  <van-button type="danger" size="small" class="full-btn" @click="screenfull.toggle()">全屏</van-button>
</template>

<script>
import {msg_confirm_vant_config, msg_err, msg_err_vant, msg_success_vant, toast_err} from "../utils/nut_component";
import {Button} from 'vant';
import {ref, onBeforeUpdate} from 'vue';
import screenfull from 'screenfull'
import taskConfig from "../scripts/fdldq/taskConfig";
import {TaskStore} from "@/store/TaskStore";
import {mapState} from "pinia";

export default {
  name: "Task_Select_View",
  computed: {
    ...mapState(TaskStore, {
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  components: {},
  data() {
    return {
      screenfull: screenfull,
      directionRight: true,
      taskList: taskConfig.taskList,
      taskArImage: "",
      taskArImageShow: false,
    }
  },
  mounted() {
    document.title = "选择任务，AR识别"
    let $this = this;

  },
  methods: {
    async clickOneTask(task) {
      if (task.taskId === "S1") {
        return
      }
      if (await msg_confirm_vant_config("请选择", "请选择图片类型", "定位二维码图片", "任务AR图片图片")) {
        this.taskArImageShow = true;
        this.taskArImage = task.qrImageUrl
      } else {
        this.taskArImageShow = true;
        this.taskArImage = task.arImageUrl
      }

    }
  }
}
</script>

<style scoped lang="less">
.full-btn {
  position: fixed;
  left: 20px;
  bottom: 0px;
}

.page-container {
  background-color: rgba(0, 0, 0, 0.2);
  min-height: 100vh;
  padding: 10px;

  .task-button-list {
    width: 100%;

    .text {
      font-size: 10px;
      color: #fff;
      margin-bottom: 5px;
    }

    .button {
      .van-button {
        margin-left: 20px;
        margin-bottom: 10px;
      }
    }
  }

  .van-image {
    margin-top: 10px;
    width: 200px;
    height: 200px;
  }
}
</style>
