<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="importStudent.dialog=true">批量导入用户
          </el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="账号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.account?scope.row.account:'/' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="邮箱" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.email?scope.row.email:'/' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="性别" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sex?scope.row.sex:'/' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="参与的剧本数量" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.scriptRecordNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="完成的任务数量" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.scriptTaskNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="获取的线索数量" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.scriptClueNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="参与的决策数量" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.scriptDecisionNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="350"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickRecordListBtn(scope.row)">剧本记录
          </el-button>
          <el-button type="default" size="mini" round
                     @click="ListMethods().clickResetPwdBtn(scope.row)">重置密码
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--弹窗-->
    <el-dialog
      title="剧本记录列表"
      :visible.sync="recordList.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="1200px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <!--列表-->
        <el-table :data="recordList.list" v-loading="recordList.loading" element-loading-text="加载中" border fit
                  style="width: 100%;">
          <el-table-column label="剧本" align="center">
            <template slot-scope="scope">
              <span>{{ enums.scriptIdNames[scope.row.scriptId] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="得分" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.score | scoreFormat }}</span>
            </template>
          </el-table-column>
          <el-table-column label="开始时间" align="center" width="140px">
            <template slot-scope="scope">
              <span>{{ scope.row.startTime | dateFormat }}</span>
            </template>
          </el-table-column>
          <el-table-column label="结束时间" align="center" width="140px">
            <template slot-scope="scope">
              <span>{{ scope.row.endTime | dateFormat }}</span>
            </template>
          </el-table-column>
          <el-table-column label="用时" align="center" width="140px">
            <template slot-scope="scope">
              <span v-if="scope.row.usedTime">{{ scope.row.usedTime | scoreUseTimeFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否已完成" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.completed ? "是" : "否" }}</span>
            </template>
          </el-table-column>
          <el-table-column label="完成任务数量" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.taskNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="完成决策数量" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.decisionNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="获取线索数量" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.clueNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="100"
                           class-name="small-padding fixed-width">
            <template slot-scope="scope">

            </template>
          </el-table-column>
        </el-table>
        <!--列表分页-->
        <div class="pagination-container">
          <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                         :current-page.sync="recordList.pages.number" :page-size.sync="recordList.pages.size"
                         layout="total,prev, pager, next,jumper,sizes" :total="recordList.pages.totalElements"
                         @size-change="(size)=>ListMethods().pageLimitChange(size)"
                         :page-count="recordList.pages.totalPages">
          </el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">

      </span>
    </el-dialog>
    <!--学生导入input-->
    <input
      id="importStudentFile"
      type="file"
      style="display: none"
      @change="(files)=>{ListMethods().importStudentFileChange(files)}"
    >
    <!--用户导入弹窗-->
    <el-dialog
      title="批量导入用户"
      :visible.sync="importStudent.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form>
          <el-form-item label="导入模板(Excel):">
            <span style="margin-right: 15px">用户批量导入列表.xlsx</span>
            <el-button type="default" size="mini" @click="ListMethods().clickDownloadBtn()">下载</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="importStudent.dialog=false">取 消
        </el-button>
        <el-button type="success" :loading="importStudent.doing"
                   @click="ListMethods().clickImportStudentBtn()">导入用户
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat, scoreFormat, scoreUseTimeFilter} from "@/filters";
import {date_format, downloadFile, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {UserModel} from "@/model/UserModel";
import {ScriptRecordModel} from "@/model/ScriptRecordModel";
import {ScriptSeriesModel} from "@/model/ScriptSeriesModel";
import {CommonModel} from "@/model/CommonModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {msg_err, msg_success} from "@/utils/ele_component";

export default {
  name: "userList",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat, scoreFormat, scoreUseTimeFilter},
  data() {
    let $this = this;
    return {
      enums: enums,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '用户邮箱',
              key: 'email',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [],
        }
      },
      recordList: {
        dialog: false,
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },

      },
      userInfo: {},
      entityInfo: {
        dialog: false,
        title: "新增标签",
        type: "add",
        filter: {},
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {},
      },
      // 导入学生
      importStudent: {
        dialog: false,
        doing: false,
      }
    }
  },
  mounted() {
    // 获取列表
    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          [list, $this.lists.pages] = await UserModel.getPageList(page - 1, size, "createTime,desc", query)
          // 遍历list获取必要信息
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {

        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增系列"
          $this.entityInfo.edit = {
            type: "document"// 定义类型是模型
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          });
        },
        // 点击编辑按钮
        async clickEditBtn(entity) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑系列"
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击剧本记录按钮
        clickRecordListBtn(user) {
          let userId = user.userId
          let query = {
            userId: userId
          }
          $this.RecordListMethods().getList(0, 10, query)
          $this.recordList.dialog = true
        },
        // 点击重置密码按钮
        async clickResetPwdBtn(user) {
          if (await UserModel.resetPwd(user.userId)) {
            msg_success("密码已重置为 123456")
          }
        },
        // 点击下载导入列表按钮
        clickDownloadBtn() {
          // todo 做到配置文件里
          downloadFile("http://jbsjxsr.cdzyhd.com/upload/%E8%B4%A6%E5%8F%B7%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E5%88%97%E8%A1%A8.xlsx", "账号批量导入列表.xlsx")
        },
        // 点击了学生导入按钮
        clickImportStudentBtn() {
          const uploader = document.getElementById('importStudentFile')
          uploader.click()
        },
        // 导入学生文件选择
        async importStudentFileChange(files) {
          const file = files.target.files[0]
          // 判断文件类型
          if (!BaseUploadModel.isImportExcelFile(file)) {
            return false
          }
          document.getElementById('importStudentFile').value = ''
          $this.importStudent.doing = true
          // todo
          if (await UserModel.importAccounts(file).catch(err => {
            $this.importStudent.dialog = false
            msg_err("批量导入用户失败")
          })) {
            $this.importStudent.dialog = false
            msg_success('批量导入用户成功')
            $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
          }
          $this.importStudent.doing = false
        },
      }
    },
    // 实体方法集
    EntityMethods() {
      let $this = this;
    },
    // 记录列表Methods
    RecordListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.recordList.loading = true;
          let list = [];
          [list, $this.recordList.pages] = await ScriptRecordModel.getPageList(page - 1, size, "", query)
          // 遍历list获取必要信息
          $this.$set($this.recordList, "list", list)
          $this.recordList.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.recordList.pages.size, $this.recordList.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.recordList.pages.number - 1, size, $this.recordList.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top-tools {
  margin-bottom: 15px;
}
</style>
