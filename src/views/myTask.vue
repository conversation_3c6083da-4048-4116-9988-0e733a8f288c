<template>
  <div v-show="screenDirection==='portrait'" class="task-container">
    <pageHeader title-name="我的任务" :showLeft="false"></pageHeader>
    <pageFooter :active-number="1"></pageFooter>
    <div class="task-list">
      <div class="script-li flex flex-start" v-for="record in taskList" v-if="taskList.length>0">
        <nut-image class="avatar" :src=" record.scriptInfo['avatarUrl']" fit="cover"
                   @click="$router.push('/scriptInfo?id='+record.scriptId)"></nut-image>
        <div class="info-box">
          <div class="name">{{ record.scriptInfo["name"] }}</div>
          <div class="start-time">开始时间：{{dateFormat(record.startTime,"yyyy-MM-dd HH:mm")}}</div>
          <nut-progress class="progress"
                        :percentage="record.progress"
                        stroke-color="linear-gradient(270deg, rgba(18,126,255,1) 0%,rgba(32,147,255,1) 32.815625%,rgba(13,242,204,1) 100%)"
                        status="active"
          />
          <div class="flex flex-start buttons">
            <nut-button type="primary" size="mini" v-if="record.completed===false"
                        @click="clickContinueTaskBtn(record)">
              继续任务
            </nut-button>
            <nut-button type="primary" size="mini" v-if="record.completed===true" @click="clickRestartTaskBtn(record)">
              重做任务
            </nut-button>
            <nut-button type="info" size="mini" v-if="record.completed===true" @click="clickStatisticBtn(record)">学情分析
            </nut-button>
          </div>
        </div>
      </div>
      <div v-if="taskList.length===0">
        <nut-empty description="暂无任务记录"></nut-empty>
      </div>
    </div>
  </div>
  <div class="direction-tip" v-show="screenDirection==='landscape'">请切换到竖屏使用</div>
</template>

<script>
import {reactive, toRefs} from 'vue';
import pageFooter from "@/views/components/pageFooter.vue";
import pageHeader from "@/views/components/pageHeader.vue";
import {UserModel} from "../model/UserModel";
import {ScriptRecordModel} from "../model/ScriptRecordModel";
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";
import {dateFormat} from "../filters";

export default {
  name: "myTask",
  components: {pageFooter, pageHeader},
  setup() {

  },
  beforeCreate() {
    UserModel.loginCheck()
  },
  computed: {
    ...mapState(TaskStore, {
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  data() {
    return {
      dateFormat:dateFormat,
      window: window,
      taskList: []
    }
  },
  async mounted() {
    document.title = "AR红色数字剧坊-我的任务"
    let taskList = (await ScriptRecordModel.getUserTaskList()).reverse()
    // 计算必要信息
    taskList.forEach(li => {
      if (li.completed === true) {
        li.progress = 100
      } else {
        let scriptInfo = li.scriptInfo
        li.progress = Math.ceil(li.taskNumber / scriptInfo["calObject"]["taskNumber"] * 100)
      }
    })
    this.taskList = taskList
  },
  methods: {
    // 点击继续任务按钮
    clickContinueTaskBtn(record) {
      ScriptRecordModel.continueScript(record.scriptRecordId)
    },
    // 点击重做任务按钮
    clickRestartTaskBtn(record) {
      ScriptRecordModel.restartScript(record)
    },
    // 点击学期分析按钮
    clickStatisticBtn(record) {
      this.$router.push(`/fdldq/end?id=${record.scriptRecordId}`)
    }
  }

}
</script>
<style scoped lang="less">
.task-container {
  background-color: #efefef;
  min-height: 100vh;
}

// 任务列表
.task-list {
  padding-bottom: 70px;


  .script-li {
    margin: 15px;
    padding:8px 15px;
    margin-bottom: 20px;
    padding-bottom: 10px;
    background-color: #fff;
    border-bottom: 1px solid #cecece;

    &:last-child {
      margin-bottom: 0px;
      border-bottom: none;
    }

    .avatar {
      display: inline-block;
      width: 120px;
      box-shadow: 1px 1px 3px 1px rgba(0, 0, 0, 0.2);
      border-radius: 7px;
    }

    .info-box {
      width: 100%;
      margin-left: 20px;
      color: #333;
    }

    .name {
      font-size: 16px;
      color: #333;
      margin-bottom: 5px;
    }
    .start-time{
      font-size: 10px;
      color: #666;
      margin-bottom: 5px;
    }

    .progress {
      margin-bottom: 5px;
    }

    .buttons {
      .nut-button {
        margin-right: 5px;
      }
    }


  }
}

.direction-tip {
  line-height: 100vh;
  text-align: center;
  font-size: 25px;
  color: #333;
}
</style>
