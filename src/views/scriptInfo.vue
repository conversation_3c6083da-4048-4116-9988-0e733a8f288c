<template>
  <div v-show="screenDirection==='portrait'" class="info-container">
    <div class="header-box flex flex-between">
      <nut-image class="avatar" :src="script.avatarUrl" fit="cover"></nut-image>
      <div class="info-box">
        <div class="name">{{ script.name }}</div>
        <div class="tags flex flex-start">
          <nut-tag class="tag" color="#E9E9E9" textColor="#999999" v-for="tag in script.tags">{{ tag }}</nut-tag>
        </div>
        <nut-tag class="series-name" color="#FA685D">{{ script.seriesNameVo }}</nut-tag>
        <div class="address">支持地点：{{ script.addressText }}</div>
      </div>
    </div>
    <div class="description-box">
      <nut-tabs v-model="tabIndex">
        <nut-tabpane title="剧本介绍">
          <div class="html-view" v-html="script.desText"></div>
        </nut-tabpane>
        <nut-tabpane title="剧本帮助">
          <div class="html-view" v-html="script.helpText"></div>
        </nut-tabpane>
      </nut-tabs>
    </div>
    <nut-sticky bottom="60" position="bottom">
      <nut-button type="primary" class="start-btn" @click="clickStartBtn">开始剧本任务</nut-button>
    </nut-sticky>
  </div>
  <div class="direction-tip" v-show="screenDirection==='landscape'">请切换到竖屏使用</div>
</template>

<script>
import {ScriptModel} from "../model/ScriptModel";
import {getQuery} from "../utils/common";
import {mapState} from "pinia";
import {TaskStore} from "@/store/TaskStore";
import {ScriptRecordModel} from "../model/ScriptRecordModel";

export default {
  name: "scriptInfo",
  computed: {
    ...mapState(TaskStore, {
      screenDirection: store => store.screenDirection,
      screenFull: store => store.screenFull
    }),
  },
  data() {
    return {
      tabIndex: 0,
      scriptId: getQuery("id"),
      script: {
        tags: [],
      },
    }
  },
  async mounted() {
    this.script = await ScriptModel.getOne(this.scriptId)
  },
  methods: {
    async clickStartBtn() {
      await ScriptRecordModel.startNewScript(this.scriptId)
    }
  }
}
</script>
<style>
.description-box .nut-tabpane{
  padding:10px;
  background-color: #f2f2f2;
}
.description-box .nut-tabs__content{
  border:none;
}
</style>
<style scoped lang="less">
.info-container {
  background-color: #f2f2f2;
  min-height: 100vh;
}

// 剧本信息
.header-box {
  background-size: cover;
  background-position: 50% 50%;
  -webkit-backface-visibility: hidden;
  background-image: url("http://resouce.cdzyhd.com/5798c6d5-ffd0-46c2-b171-87352d972e8a.jpg");
  margin-bottom: 10px;
  border-bottom: 1px solid #cecece;
  padding-left: 20px;
  padding-top: 10px;
  padding-bottom: 10px;

  &:last-child {
    border-bottom: none;
  }

  .avatar {
    display: inline-block;
    height: 100px;
    width: 100px;
  }

  .info-box {
    width: 230px;
    color: #fff;
  }

  .name {
    font-size: 18px;
  }

  .tag {
    margin-top: 5px;
    margin-right: 5px;
    font-size: 12px;
  }

  .series-name {
    font-size: 14px;
    margin-top: 8px;
  }

  .address {
    margin-top: 6px;
    font-size: 13px;
    color: #fff;
  }
}

.description-box {
  padding: 10px;

  .title {
    color: #333;
    font-weight: bold;
    font-size: 16px;
  }

  .html-view {
    font-size: 14px;
    color: #777;
    text-indent: 2em;
    line-height: 20px;
  }
}

.start-btn {
  display: block;
  width: 310px;
  margin:0 auto;
}
</style>
