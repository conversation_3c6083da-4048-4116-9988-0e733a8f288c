/**
 * nut-ui自定义组件
 */
import {Notify, Dialog, Toast} from "@nutui/nutui";
import {showNotify, showToast} from 'vant';
import {showDialog, showConfirmDialog} from 'vant';

// 错误信息弹出框
export function msg_err(msg) {
    Notify.danger(msg);
}

export function msg_err_vant(msg) {
    showNotify({type: 'danger', message: msg});
}


// 成功消息弹出框
export function msg_success(msg) {
    Notify.success(msg);
}

// 成功消息弹出框
export function msg_success_vant(msg) {
    showNotify({type: 'success', message: msg});
}

// 错误信息弹出框
export function toast_err(msg) {
    Toast.fail(msg);
}

export function toast_text(msg){
    Toast.text(msg);
}

// 成功消息弹出框
export function toast_success(msg) {
    Toast.success(msg);
}

// 选择提示框
export function msg_confirm(title, des) {
    return new Promise(function (resolve, reject) {
        let onCancel = function () {
            resolve(false)
        }
        let onOk = function () {
            resolve(true)
        }
        Dialog({
            title: title,
            content: des,
            onCancel,
            onOk
        });
    });

}

export function toast_text_vant(msg) {
    showToast(msg);
}

// 提示框
export function msg_alert_vant(title, des) {
    return new Promise(function (resolve, reject) {
        showDialog({
            title: title,
            message: des,
            theme: 'round-button',
        })
            .then(() => {
                resolve(true)
            })
            .catch(() => {
                resolve(false)
            });
    });
}

// 确认弹出框
export function msg_confirm_vant(title, des) {
    return new Promise(function (resolve, reject) {
        showConfirmDialog({
            title: title,
            message: des,
        })
            .then(() => {
                resolve(true)
            })
            .catch(() => {
                resolve(false)
            });
    });
}

// 确认弹出框-选项自定义
export function msg_confirm_vant_config(title, des, confirmButtonText, cancelButtonText) {
    return new Promise(function (resolve, reject) {
        showConfirmDialog({
            title: title,
            message: des,
            confirmButtonText: confirmButtonText,
            cancelButtonText: cancelButtonText,
        })
            .then(() => {
                resolve(true)
            })
            .catch(() => {
                resolve(false)
            });
    });
}
