import Cookies from 'js-cookie'

const Token<PERSON>ey = 'Jbs-Admin-Token'

export function getToken() {
  return Cookies.get(Token<PERSON>ey)
}

export function getLoginRole() {
  return Cookies.get("loginRole")
}

export function setToken(token, loginRole) {
  Cookies.set("loginRole", loginRole)
  return Cookies.set(To<PERSON><PERSON><PERSON>, token)
}

export function removeToken() {
  Cookies.remove("loginRole")
  return Cookies.remove(Token<PERSON>ey)
}
