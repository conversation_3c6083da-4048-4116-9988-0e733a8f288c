import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import "@/style/common.css"

const app = createApp(App)

import { createPinia } from 'pinia'
app.use(createPinia())

app.use(router)
// 路由钩子
import './router/hook'

// normalize
import 'normalize.css/normalize.css'

import NutUI from "@nutui/nutui";
import "@nutui/nutui/dist/style.css";
app.use(NutUI)
import "vant/lib/notify/index.css"
import "vant/lib/toast/index.css"
import "vant/lib/dialog/index.css"

app.mount('#app')
