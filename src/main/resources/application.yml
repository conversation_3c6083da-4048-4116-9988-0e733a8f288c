server:
  port: 9797

# redis相关
redis:
  host: **************
  port: 63636
  db: 9
  password: '!ZyhdRedis'

spring:
  application:
    name: system-jbs
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  # mongodb 相关
  data:
    mongodb:
      host: **************
      database: service_jbs_jxsr
      port: 27777
      username: service_jbs_jxsr
      password: service_jbs_jxsr_Mongo
      authentication-database: service_jbs_jxsr
      transactionEnabled: true

jbs:
  # redis hash key配置名称
  redisHashKeyName: "config_jbs_jxsr"
  #  文件存储文件夹路径
#  storageFolder: "/Users/<USER>/Downloads/libAdmin/"
  storageFolder: "/disk1/jars/jbs_jxsr/upload/"
  # api地址
#  apiUrl: "http://*************:9999/"
  apiUrl: "http://jbsjxsr.cdzyhd.com/api/"
  #  easy ar 测试图库 csyangwen3 11-23
  easyAR_CRSAPPID: "89cb0d8f22318071247f1a5ed8fcce35"
  easyAR_APIKey: "8d8ed26e9dee9893948bb348dc8df18b"
  easyAR_APISecret: "25a7cdfb1a87cf037c492f3548a86c17741216a3e51a92409ffdf925809bbe26"
  # web地址
  webUrl: "http://jbsjxsr.cdzyhd.com/"
  zyhdOrderSys:
    organId: "1"
    appId: "1"
    url: "http://*************:8900/v1/jbs/order/pushOrder"
    secretKey: "cdzyhdjbsorder2024@"  # 签名密钥

  order:
    push:
      # 推送重试间隔，和微信支付一样
      retry-intervals: [ 15,15,30,180,600,1200,1800,1800,1800,3600,10800,10800,10800,21600,21600 ]
      redis-key-prefix: "jbs_order:push:task"
      task-queue-key: "jbs_order:push:queue"
      scheduler:
        pool-size: 10
        thread-name-prefix: "jbs-order-push-scheduler-"
        await-termination-seconds: 60