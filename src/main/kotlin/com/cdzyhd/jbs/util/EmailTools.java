package com.cdzyhd.jbs.util;

public class EmailTools {

    // 生成Email 标准样式
    public String buildEmailHtml(String content, String datetime) {
        String htmlText = "<html><div style='background-color: #0F476B;width: 530px;height: 560px;color:#fff;margin:0 auto;padding:20px;'>" +
                "<img src='https://resouce.cdzyhd.com/16515615-42f6-4848-9d50-05451b00b621.png' style='width: 280px;height: 26px;display:block;margin:0 auto;" +
                "margin-bottom:15px;'><hr/><div style='font-size: 16px;margin-bottom: 15px;margin-top:15px;text-indent:2em;'>您好！</div><div style='background-color: #0c3956;" +
                "padding:15px;font-size: 16px;height: 360px;color:#9EADBB;text-indent:2em;'>";

        htmlText += content;
        htmlText += "</div><div style='font-size: 14px;margin-top: 15px;text-align:right;color:#9EADBB;'>AR红色数字剧坊</div><div style='font-size: 14px;text-align:right;color:#9EADBB;'>";
        htmlText += datetime;
        htmlText += "</div></div></html>";
        return htmlText;
    }
}
