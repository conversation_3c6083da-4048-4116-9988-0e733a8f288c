package com.cdzyhd.jbs.a;

import com.cdzyhd.jbs.config.CommonConfig;
import com.cdzyhd.jbs.repository.*;
import com.cdzyhd.jbs.service.RedisService;
import org.springframework.data.mongodb.core.MongoTemplate;

public class StaticBean {
    // CommonConfig
    public static CommonConfig commonConfig = SpringUtil.getBeanByClass(CommonConfig.class);
    // Redis
    public static RedisService redisService = SpringUtil.getBeanByClass(RedisService.class);
    // mongoTemplate
    public static MongoTemplate mongoTemplate = SpringUtil.getBeanByClass(MongoTemplate.class);
    // 管理员用户Repository
    public static AdminUserRepository adminUserRepository = SpringUtil.getBeanByClass(AdminUserRepository.class);
    public static SysLogRepository sysLogRepository = SpringUtil.getBeanByClass(SysLogRepository.class);
    public static UserRepository userRepository = SpringUtil.getBeanByClass(UserRepository.class);
    public static ScriptRepository scriptRepository = SpringUtil.getBeanByClass(ScriptRepository.class);
    public static OrganizationRepository organizationRepository = SpringUtil.getBeanByClass(OrganizationRepository.class);
    public static ScriptSeriesRepository scriptSeriesRepository = SpringUtil.getBeanByClass(ScriptSeriesRepository.class);
    public static ScriptRecordRepository scriptRecordRepository = SpringUtil.getBeanByClass(ScriptRecordRepository.class);
    public static FileRepository fileRepository=SpringUtil.getBeanByClass(FileRepository.class);
    public static HelpQuestionRepository helpQuestionRepository = SpringUtil.getBeanByClass(HelpQuestionRepository.class);

    public static OrderRepository orderRepository=SpringUtil.getBeanByClass(OrderRepository.class);
}