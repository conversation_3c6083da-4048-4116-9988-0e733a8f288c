package com.cdzyhd.jbs.event;

import com.cdzyhd.jbs.a.StaticBean;
import com.cdzyhd.jbs.entity.SysLogEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@EnableAsync
public class SysLogListener {
    @Async
    @Order
    @EventListener(SysLogEvent.class)
    public void saveSysLog(SysLogEvent event) {
        SysLogEntity sysLogEntity = (SysLogEntity) event.getSource();
        //  保存日志
        try {
            StaticBean.sysLogRepository.save(sysLogEntity);
        } catch (Exception ignored) {

        }
    }
}
