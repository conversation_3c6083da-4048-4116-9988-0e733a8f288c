package com.cdzyhd.jbs.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

// 通用配置项
@Data
@Configuration
public class CommonConfig {
    // redis hashkey
    @Value("${jbs.redisHashKeyName}")
    public String redisHashKeyName;

    // api地址
    @Value("${jbs.apiUrl}")
    public String apiUrl;

    // 前台web地址
    @Value("${jbs.webUrl}")
    public String webUrl;

    // easyAR CRS APPID
    @Value("${jbs.easyAR_CRSAPPID}")
    public String easyAR_CRSAPPID;

    // easyAR APIKey
    @Value("${jbs.easyAR_APIKey}")
    public String easyAR_APIKey;

    // easyAR APISecret
    @Value("${jbs.easyAR_APISecret}")
    public String easyAR_APISecret;

    // 智云鸿道订单系统URL
    @Value("${jbs.zyhdOrderSys.url}")
    public String zyhdOrderSysUrl;

    // 智云鸿道订单系统-机构id
    @Value("${jbs.zyhdOrderSys.organId}")
    public String organId;

    // 智云鸿道订单系统-应用id
    @Value("${jbs.zyhdOrderSys.appId}")
    public String appId;

    // 智云鸿道订单系统签名密钥
    @Value("${jbs.zyhdOrderSys.secretKey}")
    public String secretKey;
}
