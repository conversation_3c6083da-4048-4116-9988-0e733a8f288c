package com.cdzyhd.jbs.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import java.util.Arrays;
import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "order.push")
public class OrderPushRetryConfig {
    // 重试间隔（秒），默认值参考微信支付的策略
    private List<Long> retryIntervals = Arrays.asList(
        15L,    // 15秒
        15L,    // 15秒
        30L,    // 30秒
        180L,   // 3分钟
        600L,   // 10分钟
        1200L,  // 20分钟
        1800L,  // 30分钟
        1800L,  // 30分钟
        1800L,  // 30分钟
        3600L,  // 60分钟
        10800L, // 3小时
        10800L, // 3小时
        10800L, // 3小时
        21600L, // 6小时
        21600L  // 6小时
    );
    
    // Redis相关配置
    private String redisKeyPrefix = "jbs_order:push:task";
    private String taskQueueKey = "jbs_order:push:queue";

    // Getters and Setters
    public List<Long> getRetryIntervals() {
        return retryIntervals;
    }

    public void setRetryIntervals(List<Long> retryIntervals) {
        this.retryIntervals = retryIntervals;
    }

    public String getRedisKeyPrefix() {
        return redisKeyPrefix;
    }

    public void setRedisKeyPrefix(String redisKeyPrefix) {
        this.redisKeyPrefix = redisKeyPrefix;
    }

    public String getTaskQueueKey() {
        return taskQueueKey;
    }

    public void setTaskQueueKey(String taskQueueKey) {
        this.taskQueueKey = taskQueueKey;
    }
}