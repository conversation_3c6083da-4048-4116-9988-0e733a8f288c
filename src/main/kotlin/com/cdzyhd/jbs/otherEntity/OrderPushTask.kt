package com.cdzyhd.jbs.entity

import java.time.LocalDateTime

data class OrderPushTask(
    val taskId: String,                    // 任务ID
    val order: OrderEntity,                // 订单信息
    var retryCount: Int = 0,              // 重试次数
    var nextExecuteTime: LocalDateTime,    // 下次执行时间
    var status: TaskStatus,                // 任务状态
    var failReason: String? = null,        // 失败原因
    val createTime: LocalDateTime = LocalDateTime.now(),
    var updateTime: LocalDateTime = LocalDateTime.now()
) {
    enum class TaskStatus {
        PENDING,    // 待执行
        PROCESSING, // 执行中
        SUCCESS,    // 成功
        FAILED      // 失败
    }
}