package com.cdzyhd.jbs.repository;

import com.cdzyhd.jbs.entity.UserEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface UserRepository extends MongoRepository<UserEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByUserId(String id);

    // 通过id获取一个
    UserEntity findFirstByUserId(String id);

    UserEntity findFirstByEmail(String email);

    UserEntity findFirstByAccount(String account);

    // 通过id删除
    Integer deleteByUserId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<UserEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<UserEntity> getList(Document d);

    @Query(value = "?0")
    UserEntity getOne(Document d);

    Boolean existsByAccount(String account);
    Boolean existsByEmail(String email);

    // 新增：按机构查询
    List<UserEntity> findByOrganId(String organId);
    Page<UserEntity> findByOrganId(String organId, Pageable pageable);
    UserEntity findFirstByUserIdAndOrganId(String userId, String organId);
    Boolean existsByAccountAndOrganId(String account, String organId);
    Boolean existsByEmailAndOrganId(String email, String organId);
    UserEntity findFirstByAccountAndOrganId(String account, String organId);
    UserEntity findFirstByEmailAndOrganId(String email, String organId);

    // 修改现有查询方法，添加机构过滤
    @Query(value = "{'organId': ?1, $and: [?0]}")
    Page<UserEntity> getPageListByOrgan(Document d, String organId, Pageable pageable);

    @Query(value = "{'organId': ?1, $and: [?0]}")
    List<UserEntity> getListByOrgan(Document d, String organId);
}
