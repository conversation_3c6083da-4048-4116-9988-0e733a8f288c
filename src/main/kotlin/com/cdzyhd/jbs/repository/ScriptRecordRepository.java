package com.cdzyhd.jbs.repository;

import com.cdzyhd.jbs.entity.ScriptRecordEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface ScriptRecordRepository extends MongoRepository<ScriptRecordEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByScriptRecordId(String id);

    // 通过id获取一个
    ScriptRecordEntity findFirstByScriptRecordId(String id);

    // 通过id删除
    Integer deleteByScriptRecordId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<ScriptRecordEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<ScriptRecordEntity> getList(Document d);

    @Query(value = "?0")
    ScriptRecordEntity getOne(Document d);
}
