package com.cdzyhd.jbs.repository;

import com.cdzyhd.jbs.entity.AdminUserEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface AdminUserRepository extends MongoRepository<AdminUserEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByAdminUserId(String id);

    // 通过id获取一个
    AdminUserEntity findFirstByAdminUserId(String id);

    // 通过id删除
    Integer deleteByAdminUserId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<AdminUserEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<AdminUserEntity> getList(Document d);

    @Query(value = "?0")
    AdminUserEntity getOne(Document d);

    // 判断用户名或手机号是否已经存在
    Boolean existsByUsername(String username);

    AdminUserEntity findFirstByUsername(String username);
}
