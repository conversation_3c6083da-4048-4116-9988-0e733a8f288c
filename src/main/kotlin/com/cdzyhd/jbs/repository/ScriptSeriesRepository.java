package com.cdzyhd.jbs.repository;

import com.cdzyhd.jbs.entity.ScriptSeriesEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface ScriptSeriesRepository extends MongoRepository<ScriptSeriesEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByScriptSeriesId(String id);

    // 通过id获取一个
    ScriptSeriesEntity findFirstByScriptSeriesId(String id);

    // 通过id删除
    Integer deleteByScriptSeriesId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<ScriptSeriesEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<ScriptSeriesEntity> getList(Document d);

    @Query(value = "?0")
    ScriptSeriesEntity getOne(Document d);

    // 新增：按机构查询
    List<ScriptSeriesEntity> findByOrganId(String organId);
    Page<ScriptSeriesEntity> findByOrganId(String organId, Pageable pageable);
    ScriptSeriesEntity findFirstByScriptSeriesIdAndOrganId(String scriptSeriesId, String organId);

    // 修改现有查询方法，添加机构过滤
    @Query(value = "{'organId': ?1, $and: [?0]}")
    Page<ScriptSeriesEntity> getPageListByOrgan(Document d, String organId, Pageable pageable);

    @Query(value = "{'organId': ?1, $and: [?0]}")
    List<ScriptSeriesEntity> getListByOrgan(Document d, String organId);
}
