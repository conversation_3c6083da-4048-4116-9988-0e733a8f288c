package com.cdzyhd.jbs.repository;

import com.cdzyhd.jbs.entity.ScriptEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface ScriptRepository extends MongoRepository<ScriptEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByScriptId(String id);

    // 通过id获取一个
    ScriptEntity findFirstByScriptId(String id);

    // 通过id删除
    Integer deleteByScriptId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<ScriptEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<ScriptEntity> getList(Document d);

    @Query(value = "?0")
    ScriptEntity getOne(Document d);

    Integer countByScriptSeriesId(String seriesId);

    // 新增：按机构查询
    List<ScriptEntity> findByOrganId(String organId);
    Page<ScriptEntity> findByOrganId(String organId, Pageable pageable);
    ScriptEntity findFirstByScriptIdAndOrganId(String scriptId, String organId);
    Integer countByScriptSeriesIdAndOrganId(String seriesId, String organId);

    // 修改现有查询方法，添加机构过滤
    @Query(value = "{'organId': ?1, $and: [?0]}")
    Page<ScriptEntity> getPageListByOrgan(Document d, String organId, Pageable pageable);

    @Query(value = "{'organId': ?1, $and: [?0]}")
    List<ScriptEntity> getListByOrgan(Document d, String organId);
}
