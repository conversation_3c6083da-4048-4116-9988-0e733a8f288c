package com.cdzyhd.jbs.repository;

import com.cdzyhd.jbs.entity.OrderEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * 订单Repository接口
 */
public interface OrderRepository extends MongoRepository<OrderEntity, ObjectId> {
    // 通过id判断是否存在
    Boolean existsByOrderId(String id);

    // 通过id获取一个
    OrderEntity findFirstByOrderId(String id);

    // 通过id删除
    Integer deleteByOrderId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<OrderEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<OrderEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    OrderEntity getOne(Document d);

    // 根据用户ID查询订单列表
    List<OrderEntity> findByUserId(String userId);

    // 根据订单状态查询
    List<OrderEntity> findByStatus(Integer status);

    // 根据用户ID和订单状态查询
    List<OrderEntity> findByUserIdAndStatus(String userId, Integer status);

    OrderEntity findFirstByUserIdAndScriptIdAndStatusAndDeleted(String userId, String scriptId, Integer status, Integer deleted);
} 