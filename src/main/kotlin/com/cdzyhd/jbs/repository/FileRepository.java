package com.cdzyhd.jbs.repository;

import com.cdzyhd.jbs.entity.FileEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FileRepository extends MongoRepository<FileEntity, ObjectId> {
    // 是否存在
    boolean existsByFileId(String Id);

    // 获取第一个
    FileEntity findFirstByFileId(String Id);

    // 删除
    Integer deleteByFileId(String Id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<FileEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<FileEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    FileEntity getOne(Document d);
}
