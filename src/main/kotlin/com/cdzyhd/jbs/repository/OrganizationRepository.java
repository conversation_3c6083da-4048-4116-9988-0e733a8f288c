package com.cdzyhd.jbs.repository;

import com.cdzyhd.jbs.entity.OrganizationEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * 机构Repository接口
 */
public interface OrganizationRepository extends MongoRepository<OrganizationEntity, ObjectId> {
    // 通过organId判断是否存在
    Boolean existsByOrganId(String organId);

    // 通过organId获取一个
    OrganizationEntity findFirstByOrganId(String organId);

    // 通过code获取一个
    OrganizationEntity findFirstByCode(String code);

    // 通过code判断是否存在
    Boolean existsByCode(String code);

    // 通过organId删除
    Integer deleteByOrganId(String organId);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<OrganizationEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<OrganizationEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    OrganizationEntity getOne(Document d);

    // 根据状态查询
    List<OrganizationEntity> findByStatus(Integer status);

    // 根据名称模糊查询
    List<OrganizationEntity> findByNameContaining(String name);
}
