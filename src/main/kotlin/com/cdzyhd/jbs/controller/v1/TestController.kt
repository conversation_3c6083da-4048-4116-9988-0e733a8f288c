package com.cdzyhd.jbs.controller.v1

import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.base.common.util.SnowflakeIdWorker
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.entity.AdminUserEntity
import com.cdzyhd.jbs.model.ScriptRecordModel
import org.springframework.web.bind.annotation.*
import javax.servlet.http.HttpServletResponse

@RestController
@RequestMapping("/v1/api/")
class TestController {


    @GetMapping("/test")
    fun test1(httpServletResponse: HttpServletResponse, @RequestParam ticket: String): String {
        StaticBean.adminUserRepository.save(AdminUserEntity())
        return ""
    }

    @GetMapping("/test2")
    fun test2(): OutResponse<Any> {
        val scriptRecordEntity=StaticBean.scriptRecordRepository.findFirstByScriptRecordId("272680569609916416")
        ScriptRecordModel().calRecordScore(scriptRecordEntity)
        return OutResponse("000000", "上传实验结果成功", "")
    }

    /**
     * 课程实验结果数据回传
     */
    @PostMapping("/dataupload")
    fun dataUpload(
        @RequestParam access_token: String,
        @RequestBody info: String,
    ): OutResponse<Any> {
        return OutResponse("000000", "上传实验结果成功", "")
    }

}