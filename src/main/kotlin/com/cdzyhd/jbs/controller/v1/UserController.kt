package com.cdzyhd.jbs.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.enums.PassWordEnum
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.base.common.util.PasswordUtil
import com.cdzyhd.base.common.util.ResponseDataVo
import com.cdzyhd.base.common.util.StatusCode
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.annotation.NeedAdminToken
import com.cdzyhd.jbs.annotation.NeedToken
import com.cdzyhd.jbs.annotation.SysOperaLog
import com.cdzyhd.jbs.model.RedisConfigModel
import com.cdzyhd.jbs.model.UserModel
import com.cdzyhd.jbs.vo.ChangePasswordVo
import com.cdzyhd.jbs.vo.RegAccountVo
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.servlet.ModelAndView
import javax.servlet.http.HttpServletRequest


@RestController
@RequestMapping(value = ["/v1/user/"])
class UserController {
    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam userId: String): OutResponse<Any> {
        val user = StaticBean.userRepository.findFirstByUserId(userId)
        return OutResponse(
            "000000",
            "",
            user
        )
    }


    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.userRepository.getList(Document.parse(query))
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["createTime"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val pageList =
            StaticBean.userRepository.getPageList(Document.parse(queryObject.toJSONString()), pageable)
        return OutResponse(
            "000000",
            "",
            pageList
        )
    }


    // 获取用户个人中心信息
    @GetMapping("userIndexInfo")
    @NeedToken
    fun getUserIndexInfo(@RequestAttribute uid: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            UserModel().getUserIndexInfo(uid)
        )
    }

    // 平台登录
    @GetMapping("loginInPlatform")
    @SysOperaLog(moduleName = "用户模块", methodName = "平台登录-登录")
    fun loginInPlatform(
        @RequestParam username: String,
        @RequestParam password: String,
    ): OutResponse<Any> {
        if (!username.contains("@")) {
            return OutResponse(
                "000000",
                "",
                UserModel().loginInPlatformByAccount(username, password)
            )
        } else {
            return OutResponse(
                "000000",
                "",
                UserModel().loginInPlatformByEmail(username, password)
            )
        }

    }

    // 平台注册账号
    @PostMapping("regAccount")
    @SysOperaLog(moduleName = "用户模块", methodName = "注册账号")
    fun regAccount(@RequestBody regAccountPo: RegAccountVo): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            UserModel().regAccount(regAccountPo)
        )
    }

    // 通过token获取用户信息
    @GetMapping("infoByToken")
    @NeedToken
    fun getInfoByToken(@RequestAttribute uid: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            UserModel().getInfoByUserId(uid)
        )
    }

    // 发送注册邮箱验证码
    @GetMapping("sendRegEmail")
    @SysOperaLog(moduleName = "用户模块", methodName = "发送注册邮箱验证码")
    fun sendRegEmail(@RequestParam email: String): OutResponse<Any> {
        // 判断邮箱是否已注册
        if (StaticBean.userRepository.existsByEmail(email)) {
            return OutResponse(
                "000001",
                "该邮箱已注册，请更换邮箱再试一次！",
                email
            )
        }
        // 判断是否60秒内有发送记录
        if (StaticBean.redisService.hasKey("jbs_regEmailRecord_${email}")) {
            return OutResponse(
                "000001",
                "发送频率太快，请稍后再试！",
                email
            )
        }
        UserModel().sendRegEmail(email)
        return OutResponse(
            "000000",
            "",
            email
        )
    }

    // 平台修改密码
    @PostMapping("changePassword")
    @SysOperaLog(moduleName = "用户模块", methodName = "修改密码")
    @NeedToken
    fun changePassword(
        @RequestAttribute uid: String,
        @RequestBody changePasswordVo: ChangePasswordVo
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            UserModel().changePassword(uid, changePasswordVo)
        )
    }

    // 批量导入账号
    @PostMapping("/importAccounts")
    @NeedAdminToken
    fun importAccounts(@RequestParam multipartFile: MultipartFile): OutResponse<Any> {
        return OutResponse("000000", "导入成功", UserModel().importAccounts(multipartFile))
    }

    // 后台-重置用户密码
    @GetMapping("/resetPwd")
    @NeedAdminToken
    fun resetPwd(@RequestParam userId: String): OutResponse<Any> {
        val user = StaticBean.userRepository.findFirstByUserId(userId)
        user.password = PasswordUtil.generate("123456");
        StaticBean.userRepository.save(user)
        return OutResponse("000000", "密码已重置为 123456")
    }

    // 发送找回密码邮件
    @GetMapping("sendForgetEmail")
    @SysOperaLog(moduleName = "用户模块", methodName = "发送找回密码邮件")
    fun sendForgetEmail(@RequestParam email: String): OutResponse<Any> {
        // 判断邮箱是否已注册
        if (!StaticBean.userRepository.existsByEmail(email)) {
            return OutResponse(
                "000001",
                "该邮箱尚未注册，请检查后再试一次！",
                email
            )
        }
        // 判断是否60秒内有发送记录
        if (StaticBean.redisService.hasKey("jbs_forgetEmailRecord_${email}")) {
            return OutResponse(
                "000001",
                "发送频率太快，请稍后再试！",
                email
            )
        }
        UserModel().sendForgetEmail(email)
        return OutResponse(
            "000000",
            "",
            email
        )
    }

    // 前台-邮箱-重置密码
    @GetMapping(value = ["/restPasswordByEmail/{code}"], produces = ["text/html;charset=utf-8"])
    fun restPasswordByEmail(@PathVariable("code") code: String): String {
        val responseData: OutResponse<*> = OutResponse<Any>()
        if (!StaticBean.redisService.hasKey("jbs_jxsr_forgetEmail_${code}")) {
            responseData.code = "000001"
            responseData.msg = "链接地址不存在或已过期!"
            return responseData.msg
        }
        val userId = StaticBean.redisService.get("jbs_jxsr_forgetEmail_${code}") as String
        val userEntity = StaticBean.userRepository.findFirstByUserId(userId)
        userEntity.password = PasswordUtil.generate("123456");
        StaticBean.userRepository.save(userEntity)

        RedisConfigModel().setConfig("jbs_jxsr_forgetEmail_${code}", userEntity.userId, 1L)

        responseData.msg = "密码已重置为  123456"
        responseData.code = "000000"
        return responseData.msg
    }

    // 用户动作-前台
    @PostMapping("/action")
    fun action(
        request: HttpServletRequest,
        @RequestAttribute uid: String,
        @RequestParam method: String,
        @RequestBody info: String
    ): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        var returnObject: Any = ""
        val userModel = UserModel()
        when (method) {
            "changeNickname"->{
                userModel.changeNickname(uid,infoObject.getString("nickname"))
            }
        }
        return OutResponse(
            "000000", "执行成功", returnObject
        )
    }
}