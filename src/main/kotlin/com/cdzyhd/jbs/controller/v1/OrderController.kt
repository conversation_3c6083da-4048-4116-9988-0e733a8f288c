package com.cdzyhd.jbs.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.annotation.NeedAdminToken
import com.cdzyhd.jbs.entity.OrderEntity
import com.cdzyhd.jbs.exception.CommonException
import com.cdzyhd.jbs.model.OrderModel
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(value = ["/v1/order/"])
class OrderController(
    private val orderModel: OrderModel
) {

    // 获取订单列表-不分页
    @PostMapping("list")
    @NeedAdminToken
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.orderRepository.getList(Document.parse(query))
        )
    }

    // 获取订单列表-分页
    @NeedAdminToken
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String,
        @PageableDefault(
            value = 20,
            sort = ["createTime"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(StaticBean.orderRepository, queryObject.toJSONString(), pageable)
        )
    }

    // 获取单个订单
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam orderId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.orderRepository, "orderId", orderId)
        )
    }

    // 删除订单（逻辑删除）
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam orderId: String): OutResponse<Any> {
        val order = CommonMongoEntityModel.getOneById(StaticBean.orderRepository, "orderId", orderId) as OrderEntity?
            ?: return OutResponse("999999", "订单不存在", null)
            
        order.deleted = 1
        return OutResponse(
            "000000",
            "",
            StaticBean.orderRepository.save(order)
        )
    }

    // 新增或修改订单
    @PostMapping("")
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.orderRepository,
                OrderEntity(),
                "orderId",
                infoObject
            )
        )
    }

    // 手动重新推送订单
    @PostMapping("rePush")
    fun rePushOrder(@RequestParam orderId: String): OutResponse<Any> {
        val order = CommonMongoEntityModel.getOneById(StaticBean.orderRepository, "orderId", orderId) as OrderEntity?
            ?: return OutResponse("999999", "订单不存在", null)
            
        return orderModel.postOrderToUrl(order)
    }

    // 处理支付成功的订单
    @PostMapping("/payment/success")
    fun handlePaymentSuccess(@RequestParam orderId: String, @RequestParam payAmount: Long): OutResponse<Any> {
        return try {
            orderModel.handlePaymentSuccess(orderId, payAmount)
            OutResponse("000000", "支付成功处理完成", null)
        } catch (e: CommonException) {
            OutResponse(e.code, e.message ?: "处理失败", null)
        } catch (e: Exception) {
            OutResponse("999999", "系统错误：${e.message}", null)
        }
    }

    // 创建激活剧本订单
    @PostMapping("/activation/create")
    fun createActivationOrder(
        @RequestParam userId: String,
        @RequestParam scriptId: String
    ): OutResponse<OrderEntity> {
        return try {
            val order = orderModel.createActivationOrder(userId, scriptId)
            OutResponse("000000", "订单创建或修改成功", order)
        } catch (e: CommonException) {
            OutResponse(e.code, e.message ?: "创建失败", null)
        } catch (e: Exception) {
            OutResponse("999999", "系统错误：${e.message}", null)
        }
    }
}