package com.cdzyhd.jbs.controller.v1

import cn.hutool.extra.servlet.ServletUtil
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.annotation.NeedAdminToken
import com.cdzyhd.jbs.annotation.NeedToken
import com.cdzyhd.jbs.entity.ScriptRecordEntity
import com.cdzyhd.jbs.model.ScriptRecordModel
import com.cdzyhd.jbs.model.UserModel
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import java.util.*
import javax.servlet.http.HttpServletRequest


@RestController
@RequestMapping(value = ["/v1/scriptRecord/"])
class ScriptRecordController {
    // 获取用户的任务列表
    // todo 250116 在管内使用，由于访问的人很多，unity没有做分页，长此以往会积累很多，列表显示非常卡顿，所以要不要限制只返回最近的几十个。
    @GetMapping("userTaskList")
    @NeedToken
    fun getUserTaskList(@RequestAttribute uid: String): OutResponse<Any> {
        val list = StaticBean.scriptRecordRepository.getList(
            Document.parse(
                """
            {
                "userId":"$uid"
            }
            """.trimIndent()
            )
        )
        for (li in list) {
            val scriptInfo = StaticBean.scriptRepository.findFirstByScriptId(li.scriptId)
            scriptInfo.scoreConfig = null
            li.scriptInfo["avatarUrl"] = scriptInfo.avatarUrl
            li.scriptInfo["name"] = scriptInfo.name
            li.scriptInfo["configInfo"] = scriptInfo.configInfo
            li.scriptInfo["calObject"] = scriptInfo.configInfo.getJSONObject("calObject")
        }
        return OutResponse(
            "000000",
            "",
            list
        )
    }

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.scriptRecordRepository.getList(Document.parse(query))
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["scriptRecordId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val pageList =
            StaticBean.scriptRecordRepository.getPageList(Document.parse(queryObject.toJSONString()), pageable)
        return OutResponse(
            "000000",
            "",
            pageList
        )
    }


    // 获取一个
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam scriptRecordId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.scriptRecordRepository, "scriptRecordId", scriptRecordId)
        )
    }

    // 获取某个用户某个记录信息
    // 获取一个
    @GetMapping("/userOneRecordInfo")
    @NeedToken
    fun getUserOneRecordInfo(@RequestAttribute uid: String, @RequestParam scriptRecordId: String): OutResponse<Any> {
        // 判断是不是该用户的记录信息
        val recordInfo = StaticBean.scriptRecordRepository.findFirstByScriptRecordId(scriptRecordId)
        if (recordInfo == null) {
            return OutResponse(
                "000001",
                "剧本记录不存在！",
                ""
            )
        }
        if (recordInfo.userId != uid) {
            return OutResponse(
                "000001",
                "非法请求，不是你的记录信息！",
                ""
            )
        }
        return OutResponse(
            "000000",
            "",
            ScriptRecordModel().getUserOneRecordInfo(recordInfo)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam scriptRecordId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.scriptRecordRepository, "scriptRecordId", scriptRecordId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.scriptRecordRepository,
                ScriptRecordEntity(),
                "scriptRecordId",
                infoObject
            )
        )
    }

    // 初始化某一关
    @PostMapping("initOneTask")
    @NeedToken
    fun initOneTask(@RequestAttribute uid: String, @RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        val recordId = infoObject.getString("recordId")
        val taskInfo = infoObject.getJSONObject("taskInfo")
        val recordEntity = StaticBean.scriptRecordRepository.findFirstByScriptRecordId(recordId)
        if (recordEntity.userId != uid) {
            return OutResponse(
                "000001",
                "非法请求，不是你的记录信息！",
                "非法请求，不是你的记录信息！"
            )
        }
        val recordObject = recordEntity.recordInfo
        val nowTaskId = taskInfo.getString("taskId")
        recordObject[nowTaskId] = taskInfo
        val nowDate = Date().time
        taskInfo["startTime"] = nowDate
        taskInfo["status"] = "open"
        taskInfo["logInfo"] = JSONArray.parseArray(// 任务日志
            """
            [{
                "type":"open",
                "date":${nowDate}
            }]
        """.trimIndent()
        )
        recordEntity.nowTaskId = nowTaskId
        StaticBean.scriptRecordRepository.save(recordEntity)
        return OutResponse(
            "000000",
            "",
            recordEntity.recordInfo
        )
    }

    // 完成某一关
    @PostMapping("completeOneTask")
    @NeedToken
    fun completeOneTask(@RequestAttribute uid: String, @RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        val recordId = infoObject.getString("recordId")
        val taskId = infoObject.getString("taskId")
        val recordEntity = StaticBean.scriptRecordRepository.findFirstByScriptRecordId(recordId)
        if (recordEntity.userId != uid) {
            return OutResponse(
                "000001",
                "非法请求，不是你的记录信息！",
                ""
            )
        }
        val recordObject = recordEntity.recordInfo
        val taskInfo = recordObject.getJSONObject(taskId)
        val nowDate = Date().time
        taskInfo["endTime"] = Date().time
        taskInfo["completed"] = true
        taskInfo["status"] = "completed"
        taskInfo.getJSONArray("logInfo").add(
            JSONObject.parseObject(
                """
            {
                "type":"completed",
                "date":${nowDate}
            }
        """.trimIndent()
            )
        )
        recordEntity.nextTaskId = infoObject.getString("nextTaskId")
        // 记录任务数量加1
        if (taskId != "S1") {
            recordEntity.taskNumber += 1
        }

        // 计算当前所有任务用时
        val thisTaskUsedTime = (taskInfo.getLongValue("endTime") - taskInfo.getLongValue("startTime")) / 1000
        if (recordEntity.usedTime == null) {
            recordEntity.usedTime = 0L
        }
        recordEntity.usedTime += thisTaskUsedTime

        StaticBean.scriptRecordRepository.save(recordEntity)

        // 用户任务数量加一
        val userEntity = StaticBean.userRepository.findFirstByUserId(uid)
        if (taskId != "S1") {
            userEntity.scriptTaskNumber += 1
        }
        StaticBean.userRepository.save(userEntity)

        return OutResponse(
            "000000",
            "",
            recordEntity.recordInfo
        )
    }

    // 更新记录信息
    @PostMapping("updateRecordInfo")
    @NeedToken
    fun updateRecordInfo(
        @RequestAttribute uid: String,
        @RequestParam method: String,
        @RequestParam recordId: String,
        @RequestBody info: String
    ): OutResponse<Any> {
        val recordEntity = StaticBean.scriptRecordRepository.findFirstByScriptRecordId(recordId)
        var msg = ""
        if (recordEntity.userId != uid) {
            return OutResponse(
                "000001",
                "非法请求，不是你的记录信息！",
                ""
            )
        }
        val nowDate = Date().time
        when (method) {
            // 选择角色
            "roleId" -> {
                recordEntity.roleId = info
                msg = "角色设置成功"
            }
            // 更新任务关卡状态 任务状态改变时传递
            "updateTaskStatus" -> {
                val infoData = JSONObject.parseObject(info)
                val recordObject = recordEntity.recordInfo
                val taskId = infoData.getString("taskId")
                val taskObject = recordObject.getJSONObject(taskId)
                val statusPost = infoData.getString("status")
                // todo 221128 如果上一次任务状态是doing进行中，这次又是进行中，表明因为各种原因重新进入任务。当前未做任务进度恢复，需要重新开所以需要减掉
                // todo 250108 现在逻辑是只保留已完成关的信息，在完成某关任务时才提交选择题和线索
//                if (statusPost == "doing" && taskObject["status"] == "doing") {
//                    recordEntity.clueNumber -= taskObject.getJSONArray("clueList").size // 减去已获取的线索数量
//                    taskObject["clueList"] = JSONArray()
//                    val answerList = taskObject.getJSONArray("selectQuestionAnswerList")
//                    var answerTotalSize = 0
//                    for ((index, userAnswer) in answerList.withIndex()) { // 遍历计算已有的选择题数量
//                        val userAnswerArr = JSONArray.parseArray(JSON.toJSONString(userAnswer))
//                        answerTotalSize += userAnswerArr.size
//                    }
//                    recordEntity.decisionNumber -= answerTotalSize
//                    taskObject["selectQuestionAnswerList"] = JSONArray()
//                }
                // 日志记录对象
                var logoObject = JSONObject.parseObject(
                    """
                            {
                                "type":"${infoData.getString("status")}",
                                "date":${nowDate}
                            }
                        """.trimIndent()
                )
                // 保存状态信息
                taskObject["status"] = statusPost
                // 完成某个大任务中的小任务-记录进度
                if (statusPost == "doing_completeOneChildTask") {
                    val childTaskIndex = infoData.getInteger("childTaskIndex")
                    taskObject["childTaskIndex"] = childTaskIndex + 1
                    logoObject = JSONObject.parseObject(
                        """
                            {
                                "type":"${infoData.getString("status")}",
                                "childTaskIndex":$childTaskIndex,
                                "date":${nowDate}
                            }
                        """.trimIndent()
                    )
                    taskObject["status"] = "doing" // 任务状态还是进行中
                }
                // 增加日志记录
                taskObject.getJSONArray("logInfo").add(logoObject)
                msg = "任务状态更新成功"
            }
            // 完成某个选择题/决策
            "completeOneSelect" -> {
                msg = "选择题/决策设置成功"
                val infoData = JSONObject.parseObject(info)
                val recordObject = recordEntity.recordInfo
                val taskId = infoData.getString("taskId")
                val taskObject = recordObject.getJSONObject(taskId)
                var selectQuestionAnswerList = taskObject.getJSONArray("selectQuestionAnswerList")
                val answer = infoData.getJSONArray("answer")
                val selectIndex = infoData.getInteger("selectIndex")

                // 确保数组有足够容量
                if (selectQuestionAnswerList == null) {
                    selectQuestionAnswerList = JSONArray()
                    taskObject["selectQuestionAnswerList"] = selectQuestionAnswerList
                }

                // 检查之前是否已经有答案
                val hadPreviousAnswer = if (selectIndex < selectQuestionAnswerList.size) {
                    val previousAnswer = selectQuestionAnswerList[selectIndex] as JSONArray
                    previousAnswer.isNotEmpty()
                } else {
                    false
                }
                // 只有之前没有答案时才设置答案，增加数量，保证用户不能重复选择
                if (!hadPreviousAnswer) {
                    // 设置答案
                    selectQuestionAnswerList[selectIndex] = answer
                    // 记录决策数量加一
                    recordEntity.decisionNumber += 1
                    // 用户决策数量加一
                    val userEntity = StaticBean.userRepository.findFirstByUserId(uid)
                    userEntity.scriptDecisionNumber += 1
                    StaticBean.userRepository.save(userEntity)
                } else {
                    msg = "重复选择，已跳过"
                }

            }
            // 获取某个决策的线索
            "getOneClue" -> {
                msg = "线索获取成功"
                val infoData = JSONObject.parseObject(info)
                val recordObject = recordEntity.recordInfo
                val taskId = infoData.getString("taskId")
                val taskObject = recordObject.getJSONObject(taskId)
                val clueIndex = infoData.getInteger("clueIndex")
                var clueList = taskObject.getJSONArray("clueList")
                if (clueList == null) {// 如果没有线索列表，创建一个
                    taskObject["clueList"] = JSONArray()
                    clueList = taskObject.getJSONArray("clueList")
                }
                val clueField = clueIndex // 记录格式 选择题序号-线索序号
                if (!clueList.contains(clueField)) { // 判断是否已经获取过此线索，没有获取过才操作
                    clueList.add(clueField)
                    // 记录线索数量加1
                    recordEntity.clueNumber += 1
                    // 用户线索数量加一
                    val userEntity = StaticBean.userRepository.findFirstByUserId(uid)
                    userEntity.scriptClueNumber += 1
                    StaticBean.userRepository.save(userEntity)
                } else {
                    msg = "重复获取线索，已跳过"
                }

            }
            // 保存某个任务内的额外信息
            "saveOneTaskExtraInfo" -> {
                val infoData = JSONObject.parseObject(info)
                val recordObject = recordEntity.recordInfo
                val taskId = infoData.getString("taskId")
                val taskObject = recordObject.getJSONObject(taskId)
                taskObject["extraInfo"] = infoData.getJSONObject("extraInfo")
                msg = "任务额外信息保存成功"
            }
            // 重新进入某个任务
        }
        StaticBean.scriptRecordRepository.save(recordEntity)
        return OutResponse(
            "000000",
            msg,
            recordEntity.recordInfo
        )
    }

    // 获取记录信息
    @PostMapping("getRecordInfo")
    @NeedToken
    fun getRecordInfo(
        @RequestAttribute uid: String,
        @RequestParam method: String,
        @RequestParam recordId: String,
        @RequestBody info: String
    ): OutResponse<Any> {
        var msg = ""
        val recordEntity = StaticBean.scriptRecordRepository.findFirstByScriptRecordId(recordId)
        if (recordEntity.userId != uid) {
            return OutResponse(
                "000001",
                "非法请求，不是你的记录信息！",
                ""
            )
        }
        val nowDate = Date().time
        when (method) {
            // 获取某个任务内的额外信息 用于
            "getOneTaskExtraInfo" -> {
                val infoData = JSONObject.parseObject(info)
                val recordObject = recordEntity.recordInfo
                val taskId = infoData.getString("taskId")
                val taskObject = recordObject.getJSONObject(taskId)
                msg = "任务额外信息获取成功"
                return OutResponse(
                    "000000",
                    msg,
                    taskObject["extraInfo"]
                )
            }
        }

        return OutResponse(
            "000000",
            "",
            ""
        )
    }

    // 完成所有任务
    @PostMapping("completeAllTask")
    @NeedToken
    fun completeAllTask(@RequestAttribute uid: String, @RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        val recordId = infoObject.getString("recordId")
        val recordEntity = StaticBean.scriptRecordRepository.findFirstByScriptRecordId(recordId)
        if (recordEntity.userId != uid) {
            return OutResponse(
                "000001",
                "非法请求，不是你的记录信息！",
                ""
            )
        }
        if (recordEntity.completed == true) {// 是否重复提交结束
            return OutResponse(
                "000000",
                "所有任务已经全部完成，无需重复提交",
                recordEntity
            )
        } else {
            return OutResponse(
                "000000",
                "完成所有任务成功",
                ScriptRecordModel().completeAllTask(recordEntity)
            )
        }
    }

    // 241227-开始一个新剧本
    @PostMapping("startNewScript")
    @NeedToken
    fun startNewScript(
        request: HttpServletRequest,
        @RequestAttribute uid: String,
        @RequestBody info: String
    ): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        val scriptId = infoObject.getString("scriptId")
        // 创建剧本记录
        val scriptRecordEntity = ScriptRecordModel().createOneRecord(scriptId, uid)
        // 返回
        return OutResponse(
            "000000",
            "",
            scriptRecordEntity
        )
    }


    // 重新开始剧本
    @PostMapping("restartScript")
    @NeedToken
    fun restartScript(
        request: HttpServletRequest,
        @RequestAttribute uid: String,
        @RequestBody info: String
    ): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        val scriptId = infoObject.getString("scriptId")
        // 创建剧本记录
        val scriptRecordEntity = ScriptRecordModel().createOneRecord(scriptId, uid)
        // 返回
        return OutResponse(
            "000000",
            "",
            scriptRecordEntity
        )
    }

    // 获取某个课程的成绩记录列表
    @PostMapping("oneScriptRecordList")
    @NeedAdminToken
    fun getOneScriptRecordList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["scriptRecordId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val scriptId = queryObject.getString("scriptId")
        val account = queryObject.getString("account")
        val name = queryObject.getString("name")
        val email = queryObject.getString("email")
        // 查询
        val criteria =
            Criteria()
        if (scriptId != null) {
            criteria.and("scriptId").regex(name)
        }
        if (name != null) {
            criteria.and("userEntity.name").regex(name)
        }
        if (email != null) {
            criteria.and("userEntity.email").regex(email)
        }
        if (account != null) {
            criteria.and("userEntity.account").regex(account)
        }

        val matchOperation = Aggregation.match(criteria)
        // join
        val lookupUser =
            Aggregation.lookup("user", "userId", "userId", "userEntity")
        val countAggregation = Aggregation.count().`as`("count")
        val sortAggregation = Aggregation.sort(Sort.Direction.DESC, "_id")
        var aggregation = Aggregation.newAggregation(lookupUser, matchOperation, countAggregation)
        val countResult =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "script_record",
                HashMap::class.java
            ).mappedResults
        val totalElements = if (countResult.size == 0) 0 else countResult[0]["count"] as Int
        val pageSize = pageable.pageSize
        val totalPages = totalElements / pageSize
        // 分页 如果有传过来的就用
        var skipNumber = pageable.pageNumber * pageSize.toLong()
        var limitNumber = pageSize.toLong()
        if (queryObject.containsKey("skipNumber")) {
            skipNumber = queryObject.getLong("skipNumber")
        }
        if (queryObject.containsKey("limitNumber")) {
            limitNumber = queryObject.getLong("limitNumber")
        }
        val skipOperation = Aggregation.skip(skipNumber)
        val limitOperation = Aggregation.limit(limitNumber)
        aggregation = Aggregation.newAggregation(
            lookupUser,
            matchOperation,
            skipOperation,
            sortAggregation, limitOperation,
        )
        println(aggregation.toString())
        // 输出
        val list =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "script_record",
                ScriptRecordEntity::class.java
            ).mappedResults
        val returnObject = JSONObject.parseObject(
            """
            {
                "number":${pageable.pageNumber},
                "size":${pageable.pageSize},
                "totalElements":${totalElements},
                "totalPages":${totalPages}
            }
        """.trimIndent()
        )
        returnObject["content"] = list
        return OutResponse(
            "000000",
            "",
            returnObject
        )
    }
}