package com.cdzyhd.jbs.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.annotation.NeedAdminToken
import com.cdzyhd.jbs.annotation.SysOperaLog
import com.cdzyhd.jbs.entity.OrganizationEntity
import com.cdzyhd.jbs.model.OrganizationModel
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(value = ["/v1/organization/"])
class OrganizationController {

    // 获取机构信息
    @GetMapping("")
    fun getOne(@RequestParam organId: String): OutResponse<Any> {
        val organization = StaticBean.organizationRepository.findFirstByOrganId(organId)
        return OutResponse("000000", "", organization)
    }

    // 获取机构列表-不分页
    @PostMapping("list")
    @NeedAdminToken
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.organizationRepository.getList(Document.parse(query))
        )
    }

    // 获取机构列表-分页
    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(
        @RequestBody query: String,
        @PageableDefault(
            value = 20,
            sort = ["createTime"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(
                StaticBean.organizationRepository,
                queryObject.toJSONString(),
                pageable
            )
        )
    }

    // 创建机构
    @PostMapping("")
    @NeedAdminToken
    @SysOperaLog(moduleName = "机构管理", methodName = "创建机构")
    fun createOrganization(
        @RequestParam name: String,
        @RequestParam code: String,
        @RequestParam(required = false) description: String?
    ): OutResponse<Any> {
        val organization = OrganizationModel().createOrganization(name, code, description)
        return OutResponse("000000", "创建成功", organization)
    }

    // 修改机构信息
    @PutMapping("")
    @NeedAdminToken
    @SysOperaLog(moduleName = "机构管理", methodName = "修改机构信息")
    fun updateOrganization(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        val organId = infoObject.getString("organId")
        val organization = OrganizationModel().updateOrganization(organId, infoObject)
        return OutResponse("000000", "修改成功", organization)
    }

    // 启用/禁用机构
    @PutMapping("status")
    @NeedAdminToken
    @SysOperaLog(moduleName = "机构管理", methodName = "修改机构状态")
    fun updateStatus(
        @RequestParam organId: String,
        @RequestParam status: Int
    ): OutResponse<Any> {
        OrganizationModel().updateOrganizationStatus(organId, status)
        return OutResponse("000000", "状态修改成功", "")
    }

    // 删除机构（逻辑删除）
    @DeleteMapping("")
    @NeedAdminToken
    @SysOperaLog(moduleName = "机构管理", methodName = "删除机构")
    fun deleteOrganization(@RequestParam organId: String): OutResponse<Any> {
        OrganizationModel().deleteOrganization(organId)
        return OutResponse("000000", "删除成功", "")
    }

    // 获取所有启用的机构
    @GetMapping("active")
    fun getActiveOrganizations(): OutResponse<Any> {
        val organizations = OrganizationModel().getActiveOrganizations()
        return OutResponse("000000", "", organizations)
    }

    // 更新机构配置
    @PutMapping("config")
    @NeedAdminToken
    @SysOperaLog(moduleName = "机构管理", methodName = "更新机构配置")
    fun updateConfig(
        @RequestParam organId: String,
        @RequestBody config: String
    ): OutResponse<Any> {
        val configObject = JSONObject.parseObject(config)
        OrganizationModel().updateOrganizationConfig(organId, configObject)
        return OutResponse("000000", "配置更新成功", "")
    }
}
