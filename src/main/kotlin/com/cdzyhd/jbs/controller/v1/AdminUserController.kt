package com.cdzyhd.jbs.controller.v1

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.base.common.util.PasswordUtil
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.annotation.NeedAdminToken
import com.cdzyhd.jbs.annotation.NeedToken
import com.cdzyhd.jbs.annotation.SysOperaLog
import com.cdzyhd.jbs.model.AdminUserModel
import com.cdzyhd.jbs.model.TokenModel
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(value = ["/v1/adminUser/"])
class AdminUserController {
    // 获取某个平台的管理员用户信息
    @GetMapping("user")
    @NeedAdminToken
    fun getUserInfo(@RequestAttribute adminUserId: String, @RequestParam loginRole: String): OutResponse<Any> {
        return OutResponse("000000", "获取该用户信息成功", AdminUserModel().getUserInfo(adminUserId, loginRole))
    }

    // 创建管理员用户
    @PostMapping("user")
    @NeedAdminToken
    fun createAdminUser(
        @RequestParam username: String,
        @RequestParam organId: String,
        @RequestParam password: String,
        @RequestBody userInfo: String
    ): OutResponse<Any> {
        val userInfoObject = JSONObject.parseObject(userInfo)
        val adminUser = AdminUserModel().createAdminUser(username, password, organId,userInfoObject)
        return OutResponse("000000", "", adminUser)
    }


    // 管理员登录并获取token
    @GetMapping("token")
    @SysOperaLog(moduleName = "管理员用户模块", methodName = "用户登录")
    fun adminLogin(
        @RequestParam account: String,
        @RequestParam password: String,
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            AdminUserModel().adminLogin(account, password, 1)
        )
    }

    // 管理员-刷新token
    @GetMapping("token/refresh")
    fun tokenRefresh(
        @RequestParam token: String,
        @RequestParam expireDay: Int
    ): OutResponse<Any> {
        return OutResponse("000000", "", TokenModel().refreshAdminToken(token, expireDay))
    }

    // 管理员token验证
    @GetMapping("token/check")
    fun tokenCheck(
        @RequestParam token: String
    ): OutResponse<Any> {
        return OutResponse("000000", "token检测成功", TokenModel().checkAdminToken(token))
    }

    // 删除token
    @DeleteMapping("token")
    @NeedAdminToken
    fun removeToken(@RequestParam token: String): OutResponse<Any> {
        return OutResponse("000000", "", TokenModel().removeAdminToken(token))
    }

    // 用户修改密码
    @PostMapping("changePassword")
    @NeedAdminToken
    @SysOperaLog(moduleName = "管理员用户模块", methodName = "修改密码")
    fun changePassword(
        @RequestAttribute adminUserId: String,
        @RequestBody info: String
    ): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        val role = infoObject.getString("role")
        val newPassword = infoObject.getString("newPassword")
        if (role == "administrator") {
            val user = StaticBean.adminUserRepository.findFirstByAdminUserId(adminUserId)
            user.password = PasswordUtil.generate(newPassword)
            StaticBean.adminUserRepository.save(user)
        }
        return OutResponse(
            "000000",
            "密码修改成功！",
            ""
        )
    }
}