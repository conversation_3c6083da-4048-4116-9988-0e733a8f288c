package com.cdzyhd.jbs.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.annotation.NeedAdminToken
import com.cdzyhd.jbs.annotation.NeedToken
import com.cdzyhd.jbs.entity.ScriptEntity
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping(value = ["/v1/script/"])
class ScriptController {
    // 获取列表-不分页
    @PostMapping("list")
    fun getList(
        @RequestParam(value = "asAdmin", required = false) asAdmin: Boolean,
        @RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val list = StaticBean.scriptRepository.getList(Document.parse(queryObject.toJSONString()))
        if (!asAdmin) {
            list.forEach {
                it.scoreConfig = null
            }
        }
        return OutResponse(
            "000000",
            "",
            list
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestParam(value = "asAdmin", required = false) asAdmin: Boolean,
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["scriptId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val pageList =
            StaticBean.scriptRepository.getPageList(Document.parse(queryObject.toJSONString()), pageable)
        if (!asAdmin) {
            pageList.content.forEach {
                it.scoreConfig = null
            }
        }
        return OutResponse(
            "000000",
            "",
            pageList
        )
    }


    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam scriptId: String,@RequestParam(value = "asAdmin", required = false) asAdmin:Boolean): OutResponse<Any> {
        val script = StaticBean.scriptRepository.findFirstByScriptId(scriptId)
        script.seriesNameVo =
            StaticBean.scriptSeriesRepository.findFirstByScriptSeriesId(script.scriptSeriesId).name
        if(!asAdmin){
            script.scoreConfig = null
        }
        return OutResponse(
            "000000",
            "",
            script
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam scriptId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.scriptRepository, "scriptId", scriptId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.scriptRepository,
                ScriptEntity(),
                "scriptId",
                infoObject
            )
        )
    }
}