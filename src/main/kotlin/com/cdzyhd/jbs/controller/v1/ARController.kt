package com.cdzyhd.jbs.controller.v1

import cn.hutool.crypto.SecureUtil
import cn.hutool.http.HttpRequest
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.jbs.a.SpringUtil
import com.cdzyhd.jbs.config.CommonConfig
import com.cdzyhd.jbs.model.AdminUserModel
import org.springframework.web.bind.annotation.*
import java.security.MessageDigest
import java.util.*
import javax.servlet.http.HttpServletResponse
import kotlin.collections.HashMap


@RestController
@RequestMapping("/v1/ar")
class ARController {
    val commonConfig = SpringUtil.getBeanByClass(CommonConfig::class.java)

    /**
     * 获取easyARtoken
     * 参考 http://help.easyar.cn/EasyAR%20APIKey/api/get-token.html
     */
    @GetMapping("/easyARToken")
    fun getEasyARToken(httpServletResponse: HttpServletResponse): OutResponse<Any> {
        val apiKey = commonConfig.easyAR_APIKey
        val apiSecret = commonConfig.easyAR_APISecret
        val crsId = commonConfig.easyAR_CRSAPPID
        val tokenUrl = "https://uac.easyar.com/token/v2"
        val postParam = JSONObject()
        postParam["timestamp"] = Date().time
        postParam["expires"] = 60 // 过期时间
        postParam["apiKey"] = apiKey
        // 构建签名
        val builder = StringBuilder()
        builder.append("apiKey$apiKey")
        builder.append("expires60")
        builder.append("timestamp${postParam["timestamp"]}")
        postParam["signature"] =
            SecureUtil.sha256(builder.toString() + apiSecret)
        val resultString = HttpRequest.post(tokenUrl)
            .body(postParam.toJSONString())
            .execute().body()
        val resultJson = JSONObject.parseObject(resultString)
        if (resultJson.getInteger("statusCode") == 0) {// 获取成功
            val lastJson = resultJson.getJSONObject("result")
            lastJson.remove("apiKey")
            lastJson["crsAppId"] = crsId
            println("获取成功")
            return OutResponse(
                "000000",
                "",
                lastJson
            )
        } else {
            return OutResponse(
                "000001",
                "获取arToken失败！",
                ""
            )
        }

    }


}