package com.cdzyhd.jbs.controller.v1

import cn.hutool.extra.qrcode.QrCodeUtil
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.annotation.NeedAdminToken
import com.cdzyhd.jbs.entity.FileEntity
import com.cdzyhd.jbs.exception.CommonException
import com.cdzyhd.jbs.model.FileModel
import org.springframework.core.io.ByteArrayResource
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.repository.Query
import org.springframework.data.web.PageableDefault
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile


@RestController
@RequestMapping(value = ["/v1/file/"])
class FileController {
    // 上传单个文件
    @PostMapping("uploadOne")
    @NeedAdminToken
    fun uploadOne(
        @RequestParam(required = true) file: MultipartFile,
        @RequestParam(required = true) name: String,
        @RequestParam(required = true) byteSize: Long,
        @RequestParam(required = true) fileType: String,
        @RequestParam(required = true) lastModified: Long,
        @RequestParam(required = false) info: String
    ): OutResponse<Any> {
        val fileEntity = FileEntity()
        fileEntity.fileType = fileType
        fileEntity.name = name
        fileEntity.byteSize = byteSize
        fileEntity.lastModified = lastModified
        fileEntity.info = JSONObject.parseObject(info)
        FileModel().uploadOne(file, fileEntity)
        return OutResponse(
            "000000",
            "上传文件成功",
            fileEntity
        )
    }

    // 上传图片并生成分享二维码
    @PostMapping("uploadImageAndGenQr", produces = ["image/png"])
    fun uploadImageAndGenQr(
        @RequestParam(required = true) file: MultipartFile,
        @RequestParam(required = true) name: String,
        @RequestParam(required = true) byteSize: Long,
        @RequestParam(required = true) fileType: String,
        @RequestParam(required = true) lastModified: Long,
        @RequestParam(required = false) info: String
    ): ResponseEntity<ByteArrayResource> {
        // 上传文件
        val fileEntity = FileEntity()
        fileEntity.fileType = fileType
        fileEntity.name = name
        fileEntity.byteSize = byteSize
        fileEntity.lastModified = lastModified
        fileEntity.info = JSONObject.parseObject(info)
        fileEntity.purpose = "scoreInfo_qr" // 记录目的是分数信息二维码
        FileModel().uploadOne(file, fileEntity)
        // 生成二维码
        val headers = HttpHeaders()
        headers.add("Content-Disposition", "inline; filename=qrcode.png")
        val byteQrCode =
            QrCodeUtil.generatePng(StaticBean.commonConfig.webUrl + "shareScore?fileId=" + fileEntity.fileId, 800, 800)
        return ResponseEntity
            .status(HttpStatus.OK)
            .headers(headers)
            .body(ByteArrayResource(byteQrCode))
    }

    // 删除一个文件
    @DeleteMapping("/fileOne")
    @NeedAdminToken
    fun deleteFileOne(@RequestParam fileId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            FileModel().deleteFileOne(fileId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.fileRepository,
                FileEntity(),
                "fileId",
                infoObject
            )
        )
    }

    // 获取分享文件详情
    @GetMapping("/shareScoreInfo")
    fun getShareScoreInfo(@RequestParam fileId: String): OutResponse<Any> {
        val fileEntity = StaticBean.fileRepository.findFirstByFileId(fileId)
        if (fileEntity == null) {
            throw CommonException("000001", "未找到该分享ID！")
        }
        if(fileEntity.purpose!="scoreInfo_qr"){
            throw CommonException("000001", "未找到该分享ID！")
        }
        return OutResponse(
            "000000",
            "",
            fileEntity
        )
    }
}