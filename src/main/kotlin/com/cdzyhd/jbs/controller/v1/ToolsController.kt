package com.cdzyhd.jbs.controller.v1

import cn.hutool.extra.qrcode.QrCodeUtil
import cn.hutool.http.HttpUtil
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.jbs.annotation.NeedAdminToken
import com.cdzyhd.jbs.model.QrCodeModel
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.io.ByteArrayInputStream
import java.util.*
import javax.xml.bind.DatatypeConverter


@RestController
@RequestMapping("/v1/tools/")
class ToolsController {

    // 获取ip信息
    /**
     * https://ip.useragentinfo.com/json?ip=
     * 在线获取，可以获取区域
     */
    @GetMapping("ipInfoOnline1")
    @NeedAdminToken
    fun getIpInfoOnline1(@RequestParam("ip", required = true) ip: String): OutResponse<*> {
        val result = HttpUtil.get("https://ip.useragentinfo.com/json?ip=" + ip)
        return OutResponse("000000", "", JSONObject.parseObject(result))
    }

    // 生成二维码-传统方法保存二维码文件到服务器
    @GetMapping("qrCode")
    fun getQrCode(@RequestParam content: String): OutResponse<*> {
        return OutResponse("000000", "", QrCodeModel().generateQrCode(content, 300, 300))
    }

    // 生成二维码-新方法-直接返回图片
    @PostMapping("qrCodeNew",produces = ["image/png"])
    fun getQrCodeNew(@RequestBody body:String): ResponseEntity<ByteArrayResource> {
        val json=JSONObject.parseObject(body)
        val content=json.getString("content")
        val headers = HttpHeaders()
        headers.add("Content-Disposition", "inline; filename=qrcode.png")

        val byteQrCode = QrCodeUtil.generatePng(content, 800,800 )
        return ResponseEntity
            .status(HttpStatus.OK)
            .headers(headers)
            .body(ByteArrayResource(byteQrCode))
    }

    // 识别二维码
    @PostMapping("decodeQrCode")
    fun decodeQrCode(@RequestParam(required = true) file: MultipartFile): OutResponse<*> {
        return OutResponse("000000", "", QrCodeModel().decodeQrCode(file))
    }

    // 识别二维码通过inputStream
    @PostMapping("decodeQrCodeByBase64")
    fun decodeQrCodeByBase64(@RequestBody base64: String): OutResponse<*> {
        val byteArray = DatatypeConverter.parseBase64Binary(base64)
        val inputStream = ByteArrayInputStream(byteArray)
        return OutResponse("000000", "", QrCodeModel().decodeQrCodeByInputStream(inputStream))
    }

    // 获取服务器系统当前时间
    @GetMapping("serverNowDate")
    fun getServerNowDate(): OutResponse<*> {
        return OutResponse("000000", "", Date().time)
    }
}