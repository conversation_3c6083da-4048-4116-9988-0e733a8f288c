package com.cdzyhd.jbs.controller.v1

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.annotation.NeedAdminToken
import com.cdzyhd.jbs.entity.SysLogEntity
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(value = ["/v1/log/"])
class LogController {

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.sysLogRepository.getList(Document.parse(query))
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 15, sort = ["sysLogId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(StaticBean.sysLogRepository, queryObject.toJSONString(), pageable)
        )
    }


    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam sysLogId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.sysLogRepository, "sysLogId", sysLogId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam sysLogId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.sysLogRepository, "sysLogId", sysLogId)
        )
    }

    // 批量删除
    @PostMapping("delete/multiple")
    @NeedAdminToken
    fun deleteMultiple(@RequestBody sysLogIdList: String): OutResponse<Any> {
        val idArr = JSONArray.parseArray(sysLogIdList)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteMultipleById(StaticBean.sysLogRepository, "sysLogId", idArr)
        )
    }

    // 获取某个课程的成绩记录列表
    @PostMapping("logList")
    @NeedAdminToken
    fun getOneCourseScoreList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["sysLogId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val ip = queryObject.getString("ip")
        val moduleName = queryObject.getString("moduleName")
        val methodName = queryObject.getString("methodName")
        val startTime = queryObject.getLong("startTime")
        val endTime = queryObject.getLong("endTime")
        // 查询
        val criteria =
            Criteria()
        if (ip != null) {
            criteria.and("ip").regex(ip)
        }
        if (moduleName != null) {
            criteria.and("moduleName").regex(moduleName)
        }
        if (methodName != null) {
            criteria.and("methodName").regex(methodName)
        }
        if (startTime != null) {
            criteria.andOperator(Criteria("createTime").gte(startTime), Criteria("createTime").lte(endTime))
        }

        val matchOperation = Aggregation.match(criteria)
        // join
        val countAggregation = Aggregation.count().`as`("count")
        val sortAggregation = Aggregation.sort(Sort.Direction.DESC, "_id")
        var aggregation = Aggregation.newAggregation(matchOperation, countAggregation)
        val countResult =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "sysLog",
                HashMap::class.java
            ).mappedResults
        val totalElements = if (countResult.size == 0) 0 else countResult[0]["count"] as Int
        val pageSize = pageable.pageSize
        val totalPages = totalElements / pageSize
        // 分页 如果有传过来的就用
        var skipNumber = pageable.pageNumber * pageSize.toLong()
        var limitNumber = pageSize.toLong()
        if (queryObject.containsKey("skipNumber")) {
            skipNumber = queryObject.getLong("skipNumber")
        }
        if (queryObject.containsKey("limitNumber")) {
            limitNumber = queryObject.getLong("limitNumber")
        }
        val skipOperation = Aggregation.skip(skipNumber)
        val limitOperation = Aggregation.limit(limitNumber)
        aggregation = Aggregation.newAggregation(
            matchOperation,
            skipOperation,
            sortAggregation, limitOperation
        )
        println(aggregation.toString())
        // 输出
        val list =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "sysLog",
                SysLogEntity::class.java
            ).mappedResults
        val returnObject = JSONObject.parseObject(
            """
            {
                "number":${pageable.pageNumber},
                "size":${pageable.pageSize},
                "totalElements":${totalElements},
                "totalPages":${totalPages}
            }
        """.trimIndent()
        )
        returnObject["content"] = list
        return OutResponse(
            "000000",
            "",
            returnObject
        )
    }
}