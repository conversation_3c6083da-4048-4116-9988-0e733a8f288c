package com.cdzyhd.jbs.controller.v1

import com.cdzyhd.base.common.util.ConvertTools
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.annotation.NeedAdminToken
import com.cdzyhd.jbs.model.ConfigModel
import org.springframework.web.bind.annotation.*

/**
 * 配置控制器
 */
@RestController
@RequestMapping("/v1/config")
class ConfigController {
//    // 判断配置是否存在
//    @GetMapping("/hasKey")
//    fun hasKey(@RequestParam key: String): OutResponse<Any> {
//        return OutResponse("000000", "", ConfigModel().hasKey(key))
//    }
//
//    // 获取配置信息
//    @GetMapping
//    fun getConfigInfo(@RequestParam key: String): OutResponse<Any> {
//        return OutResponse("000000", "", ConfigModel().getConfig(key))
//    }

    // 修改或增加配置信息
//    @PutMapping
//    @NeedAdminToken
//    fun editConfigInfo(
//        @RequestParam key: String,
//        @RequestParam expire: Long,
//        @RequestBody value: String
//    ): OutResponse<Any> {
//        if (expire == -1L) {
//            ConfigModel().setConfig(key, value)
//        } else {
//            ConfigModel().setConfig(key, value, expire)
//        }
//        return OutResponse("000000", "", "")
//    }

    // 判断配置是否存在
//    @GetMapping("/has/hasKey")
//    fun hasHashKey(@RequestParam key: String, @RequestParam field: String): OutResponse<Any> {
//        return OutResponse("000000", "", ConfigModel().hasHashKey(key, field))
//    }

    // 获取Hash配置信息
    @GetMapping("/hash")
    @NeedAdminToken
    fun getHashConfigInfo(
        @RequestAttribute organId: String,  // 从token中自动注入
        @RequestParam field: String
    ): OutResponse<Any> {
        // 构建机构专属配置键
        val key = StaticBean.commonConfig.buildOrganRedisKey(organId)
        return OutResponse("000000", "", ConfigModel().getHashConfig(key, field))
    }

    // 修改或增加Hash配置信息
    @PutMapping("/hash")
    @NeedAdminToken
    fun editHashConfigInfo(
        @RequestAttribute organId: String,  // 从token中自动注入
        @RequestParam field: String,
        @RequestBody value: String,
        @RequestParam expire: Long
    ): OutResponse<Any> {
        // 构建机构专属配置键
        val key = StaticBean.commonConfig.buildOrganRedisKey(organId)
        if (expire == -1L) {
            ConfigModel().setHashConfig(key, field, value)
        } else {
            ConfigModel().setHashConfig(key, field, value, expire)
        }

        return OutResponse("000000", "", "")
    }

//    // 修改或增加Hash配置信息
//    @NeedAdminToken
//    @PutMapping("/hash/all")
//    fun editHashConfigInfoAll(
//        @RequestParam key: String,
//        @RequestBody map: String,
//        @RequestParam expire: Long
//    ): OutResponse<Any> {
//        val mapData = ConvertTools.StringToHashMap(map)
//        if (expire == -1L) {
//            ConfigModel().setHashConfigAll(key, mapData)
//        } else {
//            ConfigModel().setHashConfigAll(key, mapData, expire)
//        }
//
//        return OutResponse("000000", "", "")
//    }
//
//    // 设置set元素
//    @NeedAdminToken
//    @PutMapping("/set")
//    fun editSetConfig(
//        @RequestParam key: String,
//        @RequestBody value: String,
//        @RequestParam expire: Long
//    ): OutResponse<Any> {
//        if (expire == -1L) {
//            return OutResponse("000000", "", ConfigModel().setSet(key, value))
//        } else {
//            return OutResponse("000000", "", ConfigModel().setSet(key, value, expire))
//        }
//    }
//
//    // 获取Set的元素列表
//    @NeedAdminToken
//    @GetMapping("/set/members")
//    fun getSetMembers(@RequestParam key: String): OutResponse<Any> {
//        return OutResponse("000000", "", ConfigModel().setMembers(key))
//    }
//
//    //  获取Set的Size
//    @GetMapping("/set/size")
//    fun getSetSize(@RequestParam key: String): OutResponse<Any> {
//        return OutResponse("000000", "", ConfigModel().setSize(key))
//    }
//
//    // 删除Set的元素
//    @NeedAdminToken
//    @DeleteMapping("/set")
//    fun getSetMembers(@RequestParam key: String, @RequestBody value: String): OutResponse<Any> {
//        val myResponse = OutResponse<Any>()
//        return OutResponse("000000", "", ConfigModel().setRemove(key, value))
//    }

}