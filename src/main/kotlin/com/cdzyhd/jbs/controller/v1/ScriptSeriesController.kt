package com.cdzyhd.jbs.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.annotation.NeedAdminToken
import com.cdzyhd.jbs.entity.ScriptSeriesEntity
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping(value = ["/v1/scriptSeries/"])
class ScriptSeriesController {

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.scriptSeriesRepository.getList(Document.parse(query))
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["scriptSeriesId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val pageList =
            StaticBean.scriptSeriesRepository.getPageList(Document.parse(queryObject.toJSONString()), pageable)
        // 遍历获取每个学科的课程数量
        for (scriptSeries in pageList.content) {
            scriptSeries.scriptNumber =
                StaticBean.scriptRepository.countByScriptSeriesId(scriptSeries.scriptSeriesId)
        }
        return OutResponse(
            "000000",
            "",
            pageList
        )
    }


    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam scriptSeriesId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.scriptSeriesRepository, "scriptSeriesId", scriptSeriesId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam scriptSeriesId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.scriptSeriesRepository, "scriptSeriesId", scriptSeriesId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.scriptSeriesRepository,
                ScriptSeriesEntity(),
                "scriptSeriesId",
                infoObject
            )
        )
    }
}