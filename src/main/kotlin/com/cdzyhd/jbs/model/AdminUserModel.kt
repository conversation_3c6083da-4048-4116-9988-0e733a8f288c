package com.cdzyhd.jbs.model

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.PasswordUtil
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.entity.AdminUserEntity
import com.cdzyhd.jbs.exception.CommonException
import io.jsonwebtoken.Claims
import org.bson.Document
import java.util.HashMap

class AdminUserModel {
    // token检测
    fun tokenCheck(token: String, tokenClaims: Claims): Boolean {
        var checkData: Boolean = false
        val platformId = tokenClaims.issuer
        if (platformId.equals("jbs")) {
            checkData = TokenModel().checkToken(token, tokenClaims.audience)
        }
        return checkData
    }

    // 获取某个平台的用户信息
    fun getUserInfo(adminUserId: String, loginRole: String): Any {
        var user = Any()
        if (loginRole == "administrator") {
            user =
                CommonMongoEntityModel.getOneById(
                    StaticBean.adminUserRepository,
                    "adminUserId",
                    adminUserId
                ) as AdminUserEntity
        }


        return user
    }

    // 新建管理员
    fun createAdminUser(
        username: String,
        password: String,
        userInfo: JSONObject
    ): AdminUserEntity {
        // 手机号和用户名不能重复 todo 针对平台
        if (StaticBean.adminUserRepository.existsByUsername(username)) {
            throw CommonException("000001", "用户名已经存在")
        }
        val adminUserEntity = AdminUserEntity()
        adminUserEntity.username = username
        // 生成密码方式 md5 salt
        adminUserEntity.password = PasswordUtil.generate(password)
        // 姓名
        adminUserEntity.realName = userInfo["realName"] as String
        // 角色信息
        adminUserEntity.roles = userInfo["roles"] as JSONObject
        // 其他信息
        adminUserEntity.extraInfo = userInfo["extraInfo"] as JSONObject
        StaticBean.adminUserRepository.save(adminUserEntity)
        return adminUserEntity
    }


    // 管理员登录获取Token
    /**
     * @param account 登录账户 用户名或手机号
     * @param password 登录密码
     */
    fun adminLogin(
        account: String,
        password: String,
        expireDay: Int,
    ): HashMap<String, Any> {
        var loginRole = "administrator"
        var userPassword = ""
        var userId = ""
        var user = Any()
        val queryObject = JSONObject()
        queryObject["username"] = account
        // 先进行管理员登录
        if (!StaticBean.adminUserRepository.existsByUsername(account)) {
        } else {// 管理员登录
            val adminUser = StaticBean.adminUserRepository.findFirstByUsername(account)
            adminUser as AdminUserEntity
            // 如果是已经逻辑删除
            if (adminUser.deleted == 1) {
                throw CommonException("000001", "该账号已被删除")
            }
            userPassword = adminUser.password
            userId = adminUser.adminUserId
            user = adminUser
        }
        // 密码检测
        val passwordCheck = PasswordUtil.verify(password, userPassword)
        if (passwordCheck) {
            // 生成token
            val result = HashMap<String, Any>()
            result["token"] = TokenModel().genAdminTokenAndSave(userId, expireDay)
            // todo 隐藏不必要信息
            result["user"] = user
            // 角色
            result["loginRole"] = loginRole
            return result
        } else {
            throw CommonException("000001", "账户或密码错误")
        }
    }


    // 退出登录
    fun adminLogout(token: String, platformId: String): Boolean {
        return TokenModel().removeAdminToken(token)
    }

    // 新增或修改管理员
    fun addOrEdit(userInfo: JSONObject): AdminUserEntity {
        // 手机号不能重复 todo 针对平台
        val query = """
            {
                "adminUserId":{
                    "${'$'}ne":"${userInfo.getString("adminUserId")}"
                },
                "phoneNumber":"${userInfo.getString("phoneNumber")}"
            }
        """.trimIndent()
        val queryList = StaticBean.adminUserRepository.getList(Document.parse(query))
        if (queryList.size > 0) {
            throw CommonException("000001", "已存在相同手机号")
        }
        return CommonMongoEntityModel.addOrEdit(
            StaticBean.adminUserRepository,
            AdminUserEntity(),
            "adminUserId",
            userInfo
        ) as AdminUserEntity
    }
}