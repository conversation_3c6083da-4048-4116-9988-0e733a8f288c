package com.cdzyhd.jbs.model
import cn.hutool.log.StaticLog
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.jbs.a.SpringUtil
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.config.FileConfig
import com.cdzyhd.jbs.entity.FileEntity
import com.cdzyhd.jbs.exception.CommonException
import org.apache.commons.io.IOUtils
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.io.IOException
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.text.SimpleDateFormat
import java.util.*

// 文件model
class FileModel {
    val fileConfig = SpringUtil.getBeanByClass(FileConfig::class.java)

    // 上传单个文件
    fun uploadOne(file: MultipartFile, fileEntity: FileEntity): FileEntity {
        // 生成存储文件名 就是file实体的id
        var fileNameNew = fileEntity.fileId
        // 获取文件名后缀
        val fileNameOrigin = file.originalFilename.toString()
        val len: Int = fileNameOrigin.split(".").size
        val fileSuffix = fileNameOrigin.split(".")[len - 1]
        fileNameNew += ".$fileSuffix" // 等于新文件名加上后缀
        // 判断是否存在当前日期文件夹 如果不存在就创建
        val sdf = SimpleDateFormat("yyyyMMdd")
        val dateFolder = sdf.format(Date())
        val pathFolderString = fileConfig.storageFolder + dateFolder
        val pathFolder = File(pathFolderString)
        if (!pathFolder.exists() && !pathFolder.isDirectory) {
            pathFolder.mkdirs()
        }
        val pathFileString = fileConfig.storageFolder + dateFolder + "/" + fileNameNew
        try {
            val pathSave: Path = Paths.get(pathFileString)
            // 保存文件到磁盘 最终存储位置 /disk/220506/id.suffix
//            Files.write(pathSave, file.bytes)
            try {
                file.inputStream.use { inputStream ->
                    Files.newOutputStream(pathSave).use { outputStream ->
                        IOUtils.copy(inputStream, outputStream)
                    }
                }
            } catch (e: IOException) {
                e.printStackTrace()
                throw CommonException("000001", "上传文件到服务器失败！")
            }
        } catch (e: Exception) {
            StaticLog.error("上传文件错误 {}", e.message)
            throw CommonException("000001", "上传文件到服务器失败！")
        }
        // 存储位置
        fileEntity.location = "$dateFolder/$fileNameNew"
        // 存储实体
        StaticBean.fileRepository.save(fileEntity)
        // 返回
        return fileEntity
    }

    // 删除单个文件
    fun deleteFileOne(fileId: String): Boolean {
        val fileOne = StaticBean.fileRepository.findFirstByFileId(fileId)
        if (fileOne == null) {
            throw CommonException("000001", "未找到该文件记录！")
        }
        // 删除存储文件
        val file = File(fileConfig.storageFolder + fileOne.location)
        if (file.delete()) {
            // 删除数据库记录
            CommonMongoEntityModel.deleteOneById(StaticBean.fileRepository, "fileId", fileId)
        } else {
            throw CommonException("000001", "删除该文件失败！")
        }
        return true
    }
}