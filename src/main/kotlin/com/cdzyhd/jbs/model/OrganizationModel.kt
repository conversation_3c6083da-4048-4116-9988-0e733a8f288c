package com.cdzyhd.jbs.model

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.entity.OrganizationEntity
import com.cdzyhd.jbs.exception.CommonException

class OrganizationModel {
    
    // 创建机构
    fun createOrganization(name: String, code: String, description: String? = null): OrganizationEntity {
        // 检查机构代码是否已存在
        if (StaticBean.organizationRepository.existsByCode(code)) {
            throw CommonException("000001", "机构代码已存在")
        }
        
        val organization = OrganizationEntity()
        organization.name = name
        organization.code = code
        organization.description = description
        organization.status = 1
        organization.createTime = System.currentTimeMillis()
        organization.deleted = 0
        
        StaticBean.organizationRepository.save(organization)
        return organization
    }
    
    // 获取机构信息
    fun getOrganizationInfo(organId: String): OrganizationEntity? {
        return StaticBean.organizationRepository.findFirstByOrganId(organId)
    }
    
    // 根据代码获取机构信息
    fun getOrganizationByCode(code: String): OrganizationEntity? {
        return StaticBean.organizationRepository.findFirstByCode(code)
    }
    
    // 验证机构状态
    fun validateOrganizationStatus(organId: String): Boolean {
        val organization = getOrganizationInfo(organId)
        return organization != null && organization.status == 1 && organization.deleted == 0
    }
    
    // 机构配置管理
    fun updateOrganizationConfig(organId: String, config: JSONObject) {
        val organization = getOrganizationInfo(organId)
            ?: throw CommonException("000001", "机构不存在")
        
        organization.configInfo = config
        StaticBean.organizationRepository.save(organization)
    }
    
    // 更新机构信息
    fun updateOrganization(organId: String, updateInfo: JSONObject): OrganizationEntity {
        val organization = getOrganizationInfo(organId)
            ?: throw CommonException("000001", "机构不存在")
        
        // 如果要更新代码，检查新代码是否已存在
        if (updateInfo.containsKey("code")) {
            val newCode = updateInfo.getString("code")
            if (newCode != organization.code && StaticBean.organizationRepository.existsByCode(newCode)) {
                throw CommonException("000001", "机构代码已存在")
            }
        }
        
        return CommonMongoEntityModel.addOrEdit(
            StaticBean.organizationRepository,
            OrganizationEntity(),
            "organId",
            updateInfo
        ) as OrganizationEntity
    }
    
    // 启用/禁用机构
    fun updateOrganizationStatus(organId: String, status: Int) {
        val organization = getOrganizationInfo(organId)
            ?: throw CommonException("000001", "机构不存在")
        
        organization.status = status
        StaticBean.organizationRepository.save(organization)
    }
    
    // 删除机构（逻辑删除）
    fun deleteOrganization(organId: String) {
        val organization = getOrganizationInfo(organId)
            ?: throw CommonException("000001", "机构不存在")
        
        organization.deleted = 1
        StaticBean.organizationRepository.save(organization)
    }
    
    // 获取所有启用的机构
    fun getActiveOrganizations(): List<OrganizationEntity> {
        return StaticBean.organizationRepository.findByStatus(1)
    }
}
