package com.cdzyhd.jbs.model;


import com.cdzyhd.jbs.a.SpringUtil;
import com.cdzyhd.jbs.service.RedisService;

import java.util.Map;
import java.util.Set;

public class ConfigModel {
    private RedisService redisService = SpringUtil.getBeanByClass(RedisService.class);

    // 判断配置是否存在-String
    public Boolean hasKey(String key) {
        return redisService.hasKey(key);
    }

    // 获取配置信息-String
    public Object getConfig(String key) {
        return redisService.get(key);
    }

    // 设置配置信息 不过期
    public void setConfig(String key, String value) {
        redisService.set(key, value);
    }

    // 设置配置信息 过期
    public void setConfig(String key, String value, long expire) {
        redisService.set(key, value, expire);
    }

    // 判断配置是否存在-Hash
    public Boolean hasHashKey(String key, String field) {
        return redisService.hasHashKey(key, field);
    }

    // 获取Hash配置信息
    public Object getHashConfig(String key, String field) {
        return redisService.hget(key, field);
    }

    // 设置Hash配置信息 不过期
    public void setHashConfig(String key, String field, String value) {
        redisService.hset(key, field, value);
    }

    // 设置Hash配置信息 过期
    public void setHashConfig(String key, String field, String value, long expire) {
        redisService.hset(key, field, value, expire);
    }

    // 设置Hash配置信息-All 不过期
    public void setHashConfigAll(String key, Map<String, Object> mapData) {
        redisService.hsetAll(key, mapData);
    }

    // 设置Hash配置信息-All 过期
    public void setHashConfigAll(String key, Map<String, Object> mapData, long expire) {
        redisService.hsetAll(key, mapData, expire);
    }

    // 设置Set配置 不过期
    public Long setSet(String key, String value) {
        return redisService.sset(key, value);
    }

    // 设置Set配置 过期
    public Long setSet(String key, String value, long expire) {
        return redisService.sset(key, value, expire);
    }

    // 删除Set的元素
    public Long setRemove(String key, String value) {
        return redisService.sremove(key, value);
    }

    // 获取Set的元素列表
    public Set<Object> setMembers(String key) {
        return redisService.smembers(key);
    }

    // 获取Set的size
    public Long setSize(String key) {
        return redisService.ssize(key);
    }
}