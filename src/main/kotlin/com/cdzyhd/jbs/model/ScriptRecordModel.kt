package com.cdzyhd.jbs.model

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.entity.ScriptRecordEntity
import com.cdzyhd.jbs.exception.CommonException
import java.util.*

class ScriptRecordModel {
    // 创建一个新的剧本记录时验证激活状态
    fun createOneRecord(scriptId: String, userId: String): ScriptRecordEntity {
//        // 验证剧本是否已激活 241227 暂时不启用激活
//        if (!UserModel().isScriptActivated(userId, scriptId)) {
//            throw CommonException("script_not_activated", "您尚未激活该剧本,请先激活后再使用")
//        }
        
        val scriptRecordEntity = ScriptRecordEntity()
        scriptRecordEntity.scriptId = scriptId
        scriptRecordEntity.userId = userId
        scriptRecordEntity.startTime = Date().time
        scriptRecordEntity.nowTaskId = "S1"
        scriptRecordEntity.recordInfo = JSONObject()
        StaticBean.scriptRecordRepository.save(scriptRecordEntity)
        
        // 用户剧本记录数量加一
        val userEntity = StaticBean.userRepository.findFirstByUserId(userId)
        userEntity.scriptRecordNumber += 1
        StaticBean.userRepository.save(userEntity)
        return scriptRecordEntity
    }

    // 获取某个用户某个记录信息
    fun getUserOneRecordInfo(scriptRecordEntity: ScriptRecordEntity): ScriptRecordEntity {
        // todo 直接返回，后面可能要做处理
        return scriptRecordEntity
    }

    // 完成所有任务
    fun completeAllTask(recordEntity: ScriptRecordEntity): ScriptRecordEntity {
        // 计算用时
        recordEntity.endTime = Date().time
        // 250108-改成完成每一关时计算总用时
        //recordEntity.usedTime = (recordEntity.endTime - recordEntity.startTime) / 1000 // 总用时秒
        // 标记剧本完成
        recordEntity.completed = true
        recordEntity.nowTaskId = "none"
        recordEntity.nextTaskId = "none"
        // 计算得分
        this.calRecordScore(recordEntity)
        StaticBean.scriptRecordRepository.save(recordEntity)
        return recordEntity
    }

    /**
     * 计算某个剧本记录得分
     * 总分100分
     * 任务用时（10分）：任务总用时在50分钟以内，得10分；超过50分钟，每超过5分钟扣1分，直到扣完为止。
     * 线索收集（30分）：线索每缺一条计扣5分，直到扣完为止。
     * 决策选择（60分）：决策每错一次计扣10分，直到扣完为止。
     * 241230 改为从数据库中获取
     *
     * {
     *     "time":{
     *         "reasonable": 3000,// 合理时间秒
     *         "overTime":300,// 每超过几秒
     *         "minusScore":1.0 // 没超过5分钟扣分
     *     },
     *     "clue":{
     *         "score":30.0,// 总分
     *         "minusScore":5.0,// 每缺一条扣5分
     *         "number":27 // 总线索数量
     *     },
     *     "decision":{
     *         "score":60.0,// 总决策分
     *         "minusScore":10.0,// 错一条扣10分
     *         "answers":{
     *             "T1":[[1]],
     *             "T3":[[1],[1],[0]],
     *             "T4":[[2],[1]],
     *             "T5":[[2],[0]],
     *             "T6":[[1]],
     *             "T8":[[1],[0]]
     *         }
     *     }
     * }
     */
    fun calRecordScore(recordEntity: ScriptRecordEntity) {
        val scriptEntity=StaticBean.scriptRepository.findFirstByScriptId(recordEntity.scriptId)
        val scoreConfig=scriptEntity.scoreConfig
        var totalScore = 0.0
        val scoreInfo = JSONObject()
        // 用时计算
        var timeScore = 0.0
        val timeObject = scoreConfig.getJSONObject("time") // 时间分计算配置
        if (recordEntity.usedTime <= timeObject.getInteger("reasonable")) {
            // 在合理时间内，得满分
            timeScore = timeObject.getDouble("score")
        } else {
            // 超过合理时间，每5分钟（300秒）扣1分
            val exceedTime = recordEntity.usedTime - timeObject.getInteger("reasonable")
            val minusPoints = ((exceedTime / timeObject.getInteger("overTime")).toInt())*timeObject.getDouble("minusScore") // 每超过规定秒扣规定分
            timeScore = maxOf(0.0, timeObject.getDouble("score") - minusPoints)
        }
        totalScore += timeScore
        scoreInfo["timeScore"] = timeScore
        // 计算线索
        var clueObject=scoreConfig.getJSONObject("clue")// 线索分计算配置
        var totalClueScore = clueObject.getDouble("score")
        val totalClueNumber = clueObject.getInteger("number")
        val lessClueNumber = totalClueNumber - recordEntity.clueNumber
        totalClueScore = totalClueScore - lessClueNumber * clueObject.getDouble("minusScore")
        if (totalClueScore < 0) {
            totalClueScore = 0.0
        }
        totalScore += totalClueScore
        scoreInfo["clueScore"] = totalClueScore
        // 决策计算
        val decisionObject = scoreConfig.getJSONObject("decision")
        var totalDecisionScore = decisionObject.getDouble("score")
        val recordInfo = recordEntity.recordInfo
        
        // 正确答案配置，格式：{"任务ID": [[答案1], [答案2], ...]}
        // 例如：{"A2":[[1]], "C2":[[1],[2],[1]]} 表示A2任务有1道单选题，C2任务有3道题
        val rightDecisionAnswer = decisionObject.getJSONObject("answers")
        
        val decisionMinusScore = decisionObject.getDouble("minusScore")
        
        // 遍历所有需要判断的任务
        for (taskName in rightDecisionAnswer.keys) {
            val rightAnswers = rightDecisionAnswer.getJSONArray(taskName)
            
            // 检查用户是否完成了该任务
            if (!recordInfo.containsKey(taskName)) {
                // 任务未完成，扣除该任务所有题目的分数
                totalDecisionScore -= decisionMinusScore * rightAnswers.size
                continue
            }
            
            // 获取用户该任务的答案列表
            val userTaskInfo = recordInfo.getJSONObject(taskName)
            val userAnswers = userTaskInfo.getJSONArray("selectQuestionAnswerList")
            if(userAnswers==null || userAnswers.size==0){// 如果答案为空，直接扣分
                totalDecisionScore -= decisionMinusScore * rightAnswers.size
                continue
            }

            // 遍历该任务的所有题目
            for (questionIndex in 0 until rightAnswers.size) {
                // 如果用户答案数量不足，扣分并继续下一题
                if (questionIndex >= userAnswers.size) {
                    totalDecisionScore -= decisionMinusScore
                    continue
                }
                
                // 获取正确答案和用户答案
                val rightAnswerObj = rightAnswers[questionIndex]
                // 将 ArrayList 转换为 JSONArray
                val rightAnswer = JSONArray.parseArray(JSON.toJSONString(rightAnswerObj))
                val userAnswer = userAnswers[questionIndex]
                val userAnswerArr = JSONArray.parseArray(JSON.toJSONString(userAnswer))
                
                // 如果用户提交空答案，直接判错
                if (userAnswerArr.isEmpty()) {
                    totalDecisionScore -= decisionMinusScore
                    continue
                }
                
                // 比对答案（以正确答案为准判断题型）
                val isCorrect = if (rightAnswer.size > 1) {
                    // 多选题：排序后比较
                    rightAnswer.sortBy { it.toString() }
                    userAnswerArr.sortBy { it.toString() }
                    rightAnswer.toString() == userAnswerArr.toString()
                } else {
                    // 单选题：如果用户提交多个答案，直接判错
                    if (userAnswerArr.size > 1) {
                        false
                    } else {
                        rightAnswer.toString() == userAnswerArr.toString()
                    }
                }
                
                // 答案不正确则扣分
                if (!isCorrect) {
                    totalDecisionScore -= decisionMinusScore
                }
            }
            
            // 如果用户答案数量超出，额外扣分
            if (userAnswers.size > rightAnswers.size) {
                totalDecisionScore -= decisionMinusScore * (userAnswers.size - rightAnswers.size)
            }
        }
        
        // 确保分数不会为负
        totalDecisionScore = maxOf(0.0, totalDecisionScore)
        
        totalScore += totalDecisionScore
        scoreInfo["decisionScore"] = totalDecisionScore
        recordEntity.scoreInfo = scoreInfo
        recordEntity.score = totalScore
    }
}