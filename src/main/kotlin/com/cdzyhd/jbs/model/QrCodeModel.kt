package com.cdzyhd.jbs.model

import cn.hutool.core.io.FileUtil
import cn.hutool.extra.qrcode.QrCodeUtil
import cn.hutool.log.StaticLog
import com.cdzyhd.jbs.a.SpringUtil
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.config.FileConfig
import com.cdzyhd.jbs.entity.FileEntity
import com.cdzyhd.jbs.exception.CommonException
import org.springframework.web.multipart.MultipartFile
import java.io.File
import java.io.InputStream
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.text.SimpleDateFormat
import java.util.*

// 二维码model
class QrCodeModel {
    val fileConfig = SpringUtil.getBeanByClass(FileConfig::class.java)

    // 生成二维码并存储到目录中
    fun generateQrCode(content: String, width: Int, height: Int): FileEntity {
        val fileEntity = FileEntity()
        fileEntity.fileType = "image/jpg"
        fileEntity.name = "qrcode-$content"
        fileEntity.lastModified = Date().time
        // 生成存储文件名 就是file实体的id
        val fileNameNew = fileEntity.fileId + ".png"
        // 判断是否存在当前日期文件夹 如果不存在就创建
        val sdf = SimpleDateFormat("yyyyMMdd")
        val dateFolder = sdf.format(Date())
        val pathFolderString = fileConfig.storageFolder + dateFolder
        val pathFolder = File(pathFolderString)
        if (!pathFolder.exists() && !pathFolder.isDirectory) {
            pathFolder.mkdirs()
        }
        val pathFileString = fileConfig.storageFolder + dateFolder + "/" + fileNameNew
        try {
            val pathSave: Path = Paths.get(pathFileString)
            // 保存文件到磁盘 最终存储位置 /disk/220506/id.suffix
            val byteQrCode = QrCodeUtil.generatePng(content, width, height)
            fileEntity.byteSize = byteQrCode.size.toLong()
            Files.write(pathSave, byteQrCode)
        } catch (e: Exception) {
            StaticLog.error("上传文件错误 {}", e.message)
            throw CommonException("000001", "上传文件到服务器失败！")
        }
        // 存储位置
        fileEntity.location = "$dateFolder/$fileNameNew"
        // 存储实体
        StaticBean.fileRepository.save(fileEntity)
        // 返回
        return fileEntity
    }

    // 识别二维码 todo 识别的性能影响和要求
    fun decodeQrCode(file: MultipartFile): String {
        val result = QrCodeUtil.decode(file.inputStream)
        return result ?: "notMatch"
    }

    // 识别二维码通过inputStream todo 识别的性能影响和要求
    fun decodeQrCodeByInputStream(inputStream: InputStream): String {
        val result = QrCodeUtil.decode(inputStream)
        return result ?: "notMatch"
    }
}