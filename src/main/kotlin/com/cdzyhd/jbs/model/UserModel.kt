package com.cdzyhd.jbs.model

import cn.hutool.core.util.RandomUtil
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.DateTimeTools
import com.cdzyhd.base.common.util.PasswordUtil
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.entity.UserEntity
import com.cdzyhd.jbs.exception.CommonException
import com.cdzyhd.jbs.util.EmailTools
import com.cdzyhd.jbs.util.ImportExcelUtil
import com.cdzyhd.jbs.util.SnowflakeIdWorker
import com.cdzyhd.jbs.vo.ChangePasswordVo
import com.cdzyhd.jbs.vo.RegAccountVo
import org.apache.commons.collections4.CollectionUtils
import org.springframework.web.multipart.MultipartFile
import java.util.*
import javax.mail.Message
import javax.mail.Session
import javax.mail.Transport
import javax.mail.internet.InternetAddress
import javax.mail.internet.MimeMessage
import javax.mail.internet.MimeUtility
import com.github.houbb.sensitive.word.bs.SensitiveWordBs

class UserModel {
    // 获取个人中心用户信息
    fun getUserIndexInfo(uid: String): UserEntity? {
        return StaticBean.userRepository.findFirstByUserId(uid)
    }

    // 修改登录密码
    fun changePassword(userId: String, changePasswordVo: ChangePasswordVo): Boolean {
        val user = StaticBean.userRepository.findFirstByUserId(userId)
        val passwordCheck = PasswordUtil.verify(changePasswordVo.oldPassword, user.password)
        if (!passwordCheck) {
            throw CommonException("oldPassword_wrong", "原密码错误！")
        }
        user.password = PasswordUtil.generate(changePasswordVo.newPassword)
        StaticBean.userRepository.save(user)
        return true
    }

    // 登录平台-邮箱登录 todo 安全-登录频率限制
    fun loginInPlatformByEmail(email: String, password: String): HashMap<String, Any> {
        val result = HashMap<String, Any>()
        val userFind = StaticBean.userRepository.findFirstByEmail(email)
        if (userFind == null) {
            throw CommonException("account_not_exist", "账号不存在！")
        }
        if (userFind.deleted == 1) {
            throw CommonException("account_disabled", "该账号已被禁用！")
        }
        // 检测密码是否正确
        val passwordCheck = PasswordUtil.verify(password, userFind.password)
        if (passwordCheck) {

        } else {
            throw CommonException("username_or_password_error", "账户或密码错误")
        }
        // 生成token todo 修改过期天数 250117 改为10年，保证不过期
        result["token"] = TokenModel().genTokenAndSave(userFind.userId, userFind.role, 3650)
        // todo 隐藏不必要信息
        result["userInfo"] = userFind
        return result
    }

    // 登录平台-学号登录 todo 安全-登录频率限制
    fun loginInPlatformByAccount(account: String, password: String): HashMap<String, Any> {
        val result = HashMap<String, Any>()
        val userFind = StaticBean.userRepository.findFirstByAccount(account)
        if (userFind == null) {
            throw CommonException("account_not_exist", "用户名不存在！")
        }
        if (userFind.deleted == 1) {
            throw CommonException("account_disabled", "该账号已被禁用！")
        }
        // 检测密码是否正确
        val passwordCheck = PasswordUtil.verify(password, userFind.password)
        if (passwordCheck) {

        } else {
            throw CommonException("username_or_password_error", "账户或密码错误")
        }
        // 生成token todo 修改过期天数 250117 改为10年，保证不过期
        result["token"] = TokenModel().genTokenAndSave(userFind.userId, userFind.role, 3650)
        // todo 隐藏不必要信息
        result["userInfo"] = userFind
        return result
    }

    // 账号注册
    /**
     * 暂时通过邮箱注册，考虑学校情况学号、工号
     */
    fun regAccount(regAccountPo: RegAccountVo): UserEntity {
        // todo-安全 注册频率限制
        // 检测邮箱验证码是否正确
        if (!StaticBean.redisService.hasKey("jbs_jxsr_regEmail_${regAccountPo.email}")) {
            throw CommonException("000001", "邮箱验证码已失效或不存在！")
        }
        val redisCode = StaticBean.redisService.get("jbs_jxsr_regEmail_${regAccountPo.email}") as String
        if (redisCode != regAccountPo.emailCode) {
            throw CommonException("000001", "邮箱验证码不正确！")
        }
        StaticBean.redisService.set("jbs_jxsr_bindEmail_${regAccountPo.email}", "", 1) // 立即过期
        // todo 安全-后端进行各项验证 用户名、手机号、邮箱规则
        if (StaticBean.userRepository.existsByEmail(regAccountPo.email)) {
            throw CommonException("000001", "该邮箱已被绑定！")
        }
        val user = UserEntity()
        user.password = PasswordUtil.generate(regAccountPo.password);
        user.email = regAccountPo.email
        user.sex = regAccountPo.sex
        StaticBean.userRepository.save(user)
        return user
    }

    // 发送注册邮箱邮件
    fun sendRegEmail(email: String): Boolean {
        // todo 安全 发送频率限制
        try {
            val properties = Properties()
            // todo 邮件配置存储到配置文件
            properties["mail.transport.protocol"] = "smtp" // 连接协议
            properties["mail.smtp.host"] = "smtp.qq.com" // 主机名
            properties["mail.smtp.port"] = 465 // 端口号
            properties["mail.smtp.auth"] = "true"
            properties["mail.smtp.ssl.enable"] = "true" // 设置是否使用ssl安全连接 ---一般都使用
            properties["mail.debug"] = "true" // 设置是否显示debug信息 true 会在控制台显示相关信息
            // 得到回话对象
            val session: Session = Session.getInstance(properties)
            // 获取邮件对象
            val message = MimeMessage(session)
            // 设置发件人邮箱地址
            message.setFrom(InternetAddress("<EMAIL>"))
            // 设置收件人邮箱地址
            message.setRecipients(Message.RecipientType.TO, arrayOf<InternetAddress>(InternetAddress(email)))

            // 设置邮件标题 todo 平台名称写到配置文件
            val subject = "AR红色数字剧坊-注册验证码"
            val encodedSubject: String = MimeUtility.encodeText(subject, MimeUtility.mimeCharset("gb2312"), null)
            message.setSubject(encodedSubject)
            // 生成redis标记
            val code = RandomUtil.randomNumbers(6)
            // redis记录邮箱验证码 10分钟内有效
            RedisConfigModel().setConfig("jbs_jxsr_regEmail_${email}", code, 600L)
            // redis发送邮箱倒计时记录,1分钟内有效
            RedisConfigModel().setConfig("jbs_jxsr_regEmailRecord_${email}", code, 60L)
            // redis记录
            // 设置邮件内容
            val dateTime: String = DateTimeTools.dateFormat(Date(), "YYYY-MM-dd HH:mm")
            var text = ""
            text =
                "您正在进行注册账号操作，您的验证码是：<span style='color:#fff'>$code</span>。该验证码10分钟内有效，请及时处理。"
            text += "<div style='margin-left:20px;'></div>"
            val messageText: String = EmailTools().buildEmailHtml(text, dateTime)
            message.setContent(messageText, "text/html;charset=utf-8")
            // 得到邮差对象
            val transport: Transport = session.getTransport()
            // 连接自己的邮箱账户
            transport.connect("<EMAIL>", "whojncmgdqqxiceg") // 密码为QQ邮箱开通的stmp服务后得到的客户端授权码
            // 发送邮件
            transport.sendMessage(message, message.getAllRecipients())
            transport.close()
        } catch (e: Exception) {
            throw CommonException("000001", "邮件发送失败！")
        }
        return true
    }

    // 发送重置密箱邮件
    fun sendForgetEmail(email: String): Boolean {
        // todo 安全 发送频率限制
        try {
            val properties = Properties()
            // todo 邮件配置存储到配置文件
            properties["mail.transport.protocol"] = "smtp" // 连接协议
            properties["mail.smtp.host"] = "smtp.qq.com" // 主机名
            properties["mail.smtp.port"] = 465 // 端口号
            properties["mail.smtp.auth"] = "true"
            properties["mail.smtp.ssl.enable"] = "true" // 设置是否使用ssl安全连接 ---一般都使用
            properties["mail.debug"] = "true" // 设置是否显示debug信息 true 会在控制台显示相关信息
            // 得到回话对象
            val session: Session = Session.getInstance(properties)
            // 获取邮件对象
            val message = MimeMessage(session)
            // 设置发件人邮箱地址
            message.setFrom(InternetAddress("<EMAIL>"))
            // 设置收件人邮箱地址
            message.setRecipients(Message.RecipientType.TO, arrayOf<InternetAddress>(InternetAddress(email)))

            // 设置邮件标题 todo 平台名称写到配置文件
            val subject = "AR红色数字剧坊-重置密码"
            val encodedSubject: String = MimeUtility.encodeText(subject, MimeUtility.mimeCharset("gb2312"), null)
            message.setSubject(encodedSubject)
            val userEntity = StaticBean.userRepository.findFirstByEmail(email)
            // 生成redis标记
            val code = SnowflakeIdWorker(1, 0).nextId()
            // redis记录邮箱验证码 10分钟内有效
            RedisConfigModel().setConfig("jbs_jxsr_forgetEmail_${code}", userEntity.userId, 600L)
            // redis发送邮箱倒计时记录,1分钟内有效
            RedisConfigModel().setConfig("jbs_jxsr_forgetEmailRecord_${email}", code, 60L)
            // redis记录
            // 设置邮件内容
            val dateTime: String = DateTimeTools.dateFormat(Date(), "YYYY-MM-dd HH:mm")
            var text = ""
            val apiUrl = StaticBean.commonConfig.apiUrl
            text =
                "您正在进行重置密码操作，点击<a style='color:#fff;text-decoration:underline;padding: 0px 4px;' href='$apiUrl/v1/user/restPasswordByEmail/$code'>重置密码</a>，你的密码将重置为：<span style='color:#fff'>A1@b2@c3(区分大小写)</span>。该链接10分钟内有效，请及时处理。重置后，请尽快登录系统更改密码。"
            text += "<div style='margin-left:20px;'></div>"
            val messageText: String = EmailTools().buildEmailHtml(text, dateTime)
            message.setContent(messageText, "text/html;charset=utf-8")
            // 得到邮差对象
            val transport: Transport = session.getTransport()
            // 连接自己的邮箱账户
            transport.connect("<EMAIL>", "whojncmgdqqxiceg") // 密码为QQ邮箱开通的stmp服务后得到的客户端授权码
            // 发送邮件
            transport.sendMessage(message, message.getAllRecipients())
            transport.close()
        } catch (e: Exception) {
            throw CommonException("000001", "邮件发送失败！")
        }
        return true
    }

    // 通过userId获取用户信息
    fun getInfoByUserId(userId: String): UserEntity {
        val userFind = StaticBean.userRepository.findFirstByUserId(userId)
        return userFind
    }

    // 批量导入学生-班级外 todo 修改
    fun importAccounts(multipartFile: MultipartFile) {
        val arrayLists = ImportExcelUtil.readExcel(multipartFile)
        val userList = ArrayList<UserEntity>()
        val userModel = UserModel()
        if (CollectionUtils.isNotEmpty(arrayLists)) {
            val headList = arrayLists[0]
            val fieldNames: MutableMap<Int, String> = HashMap()
            val fieldIndex: MutableMap<String, Int> = HashMap()
            run {
                var i = 0
                val length = headList!!.size
                while (i < length) {
                    val fieldText = headList[i] as String
                    println("列头 $i: '$fieldText'") // 打印列头
                    if ("*账号" == fieldText) {
                        fieldNames[i] = "account"
                        fieldIndex["account"] = i
                    } else if ("*邮箱" == fieldText) {
                        fieldNames[i] = "email"
                        fieldIndex["email"] = i
                    } else if ("*性别" == fieldText) {
                        fieldNames[i] = "sex"
                        fieldIndex["sex"] = i
                    }
                    i++
                }
            }
            println("字段索引映射: $fieldIndex") // 打印映射结果

            val totalNum = arrayLists.size
            for (i in 1 until totalNum) {
                val num = i + 1
                val objects = arrayLists[i]
                if (objects!!.size != 1) {
                    if (objects.isNotEmpty()) {
                        println("原始数据大小: ${objects.size}")
                        println("原始数据内容: ${objects.joinToString()}")
                        
                        // 获取列索引
                        val accountIndex = fieldIndex["account"]!!
                        val emailIndex = fieldIndex["email"]!!
                        val sexIndex = fieldIndex["sex"]!!
                        
                        // 确保数组长度足够
                        val paddedObjects = if (objects.size <= maxOf(accountIndex, emailIndex, sexIndex)) {
                            println("需要填充数据，从 ${objects.size} 扩展到 ${maxOf(accountIndex, emailIndex, sexIndex) + 1}")
                            objects.toMutableList().apply {
                                while (size <= maxOf(accountIndex, emailIndex, sexIndex)) add("")
                            }
                        } else {
                            objects
                        }
                        
                        println("处理后数据大小: ${paddedObjects.size}")
                        println("处理后数据内容: ${paddedObjects.joinToString()}")
                        
                        // 获取实际值，确保使用正确的索引
                        val accountValue = if (accountIndex < paddedObjects.size) 
                            (paddedObjects[accountIndex] as? String)?.trim() else ""
                        val emailValue = if (emailIndex < paddedObjects.size) 
                            (paddedObjects[emailIndex] as? String)?.trim() else ""
                        val sexValue = if (sexIndex < paddedObjects.size) 
                            (paddedObjects[sexIndex] as? String)?.trim() else ""
                        
                        println("解析后的值 - 账号:'$accountValue', 邮箱:'$emailValue', 性别:'$sexValue'")
                        
                        // 检查账号和邮箱至少填写一个
                        if (accountValue.isNullOrEmpty() && emailValue.isNullOrEmpty()) {
                            throw CommonException("000001", "导入失败。您的表格第${num}行账号和邮箱至少填写一个")
                        }
                        
                        val student = UserEntity()
                        
                        // 处理账号
                        if (!accountValue.isNullOrEmpty()) {
                            if (accountValue.length > 30) {
                                throw CommonException("000001", "导入失败。您的表格第${num}行账号，字数不应超过30字")
                            }
                            // 判断账号是否已存在
                            if (StaticBean.userRepository.existsByAccount(accountValue)) {
                                throw CommonException("000001", "导入失败。您的表格第${num}行账号${accountValue}已存在！")
                            }
                            student.account = accountValue
                        }
                        
                        // 处理邮箱
                        if (!emailValue.isNullOrEmpty()) {
                            // 检查邮箱格式
                            if (!emailValue.matches(Regex(".+@.+"))) {
                                throw CommonException("000001", "导入失败。您的表格第${num}行邮箱格式不正确")
                            }
                            // 判断邮箱是否已存在
                            if (StaticBean.userRepository.existsByEmail(emailValue)) {
                                throw CommonException("000001", "导入失败。您的表格第${num}行邮箱${emailValue}已存在！")
                            }
                            student.email = emailValue
                        }
                        
                        // 处理性别
                        if (sexValue.isNullOrEmpty()) {
                            throw CommonException("000001", "导入失败。您的表格第${num}行性别未填写")
                        }
                        
                        // 性别只能是男或女
                        if(sexValue != "男" && sexValue != "女") {
                            throw CommonException("000001", "导入失败。您的表格第${num}行性别，只能是男或女")
                        }
                        student.sex = sexValue
                        
                        // 加入待添加列表
                        userList.add(student)
                    }
                }
                println("$num/$totalNum")
            }
        } else {
            throw CommonException("000001", "数据表为空")
        }
        if (userList.isNotEmpty()) {
            // 已完成首次表格检查，进行添加
            for (student in userList) {
                // 执行在班级中新增学生
                this.addUser(student)
            }
        } else {
            throw CommonException("000001", "数据表为空")
        }
    }


    // 批量增加用户
    private fun addUser(excelUser: UserEntity) {
        val user = UserEntity()
        user.account = excelUser.account
        user.email = excelUser.email
        user.sex = excelUser.sex
        StaticBean.userRepository.save(user)
    }

    // 用户修改自己的昵称
    fun changeNickname(userId: String, newNickname: String) {
        // 1. 检查昵称长度
        if (newNickname.length < 2 || newNickname.length > 15) {
            throw CommonException("invalid_nickname", "昵称长度必须在2-15个字符之间")
        }

        // 2. 检查本月修改次数限制
        val redisConfig = RedisConfigModel()
        // 生成包含年月的 Redis key
        val currentMonth = DateTimeTools.dateFormat(Date(), "yyMM")  // 获取当前年月，如"2410"
        val key = "nickname_change_${userId}_${currentMonth}"

        // 获取本月已修改次数
        var changeCount = 0
        val countStr = redisConfig.getConfig(key)
        if (countStr != null) {
            changeCount = countStr as Int
        }

        if (changeCount >= 3) {
            throw CommonException("change_limit_exceeded", "您本月修改昵称次数已达上限(3次),请下月再试")
        }

        // 3. 敏感词检测
        val sensitiveWordBs = initSensitiveWordBs()
        // 检查是否包含敏感词
        if (sensitiveWordBs.contains(newNickname)) {
            // 获取所有包含的敏感词
            val sensitiveWords = sensitiveWordBs.findAll(newNickname)
            throw CommonException(
                "sensitive_nickname",
                "昵称包含敏感词: ${sensitiveWords.joinToString()}, 请修改后再试"
            )
        }

        // 4. 更新用户昵称
        val user = StaticBean.userRepository.findFirstByUserId(userId)
            ?: throw CommonException("user_not_found", "用户不存在")
        user.nickname = newNickname
        StaticBean.userRepository.save(user)

        // 更新Redis中的修改次数，设置过期时间为本月底，就实现了月底自动失效，下个月重新计算修改昵称次数功能。
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        val remainingSeconds = (calendar.timeInMillis - System.currentTimeMillis()) / 1000
        redisConfig.setConfig(key, (changeCount + 1).toString(), remainingSeconds)
    }

    // 在类中添加初始化敏感词库的方法
    private fun initSensitiveWordBs(): SensitiveWordBs {
        return SensitiveWordBs.newInstance()
            .init() // 使用默认词库
//            .addWord("ai") // 添加自定义敏感词
    }

    // 激活剧本
    fun activateScript(userId: String, scriptId: String, orderId: String) {
        val user = StaticBean.userRepository.findFirstByUserId(userId)
            ?: throw CommonException("user_not_found", "用户不存在")
        
        // 获取订单信息
        val order = StaticBean.orderRepository.findFirstByOrderId(orderId)
            ?: throw CommonException("order_not_found", "订单不存在")
        
        // 验证订单状态
        if (order.status != 1) { // 1-已支付
            throw CommonException("invalid_order", "订单未支付或状态异常")
        }
        
        // 检查是否已激活
        if (user.activatedScripts == null) {
            user.activatedScripts = JSONArray()
        }
        
        if (!user.activatedScripts.contains(scriptId)) {
            user.activatedScripts.add(scriptId)
            
            // 记录激活信息
            if (user.scriptActivationRecords == null) {
                user.scriptActivationRecords = JSONObject()
            }
            
            val activationRecord = JSONObject()
            activationRecord.put("activationTime", Date().time)
            activationRecord.put("orderId", orderId)
            activationRecord.put("payAmount", order.payAmount)
            
            user.scriptActivationRecords.put(scriptId, activationRecord)
            
            StaticBean.userRepository.save(user)
        }
    }

    // 检查剧本是否已激活
    fun isScriptActivated(userId: String, scriptId: String): Boolean {
        val user = StaticBean.userRepository.findFirstByUserId(userId)
            ?: throw CommonException("user_not_found", "用户不存在")
        
        return user.activatedScripts?.contains(scriptId) ?: false
    }

    // 获取用户所有已激活的剧本
    fun getActivatedScripts(userId: String): JSONArray {
        val user = StaticBean.userRepository.findFirstByUserId(userId)
            ?: throw CommonException("user_not_found", "用户不存在")
        
        return user.activatedScripts ?: JSONArray()
    }
}