package com.cdzyhd.jbs.model

import com.cdzyhd.jbs.a.SpringUtil
import com.cdzyhd.jbs.service.RedisService

class RedisConfigModel {
    private val redisService: RedisService = SpringUtil.getBeanByClass(RedisService::class.java)

    // 判断配置是否存在-String
    fun hasKey(key: String): Boolean {
        return redisService.hasKey(key)
    }

    // 获取配置信息-String
    fun getConfig(key: String): Any {
        return redisService.get(key)
    }

    // 设置配置信息 不过期
    fun setConfig(key: String, value: String) {
        redisService.set(key, value)
    }

    // 设置配置信息 过期
    fun setConfig(key: String, value: String, expire: Long) {
        redisService.set(key, value, expire)
    }

    // 判断配置是否存在-Hash
    fun hasHashKey(key: String, field: String): Boolean {
        return redisService.hasHashKey(key, field)
    }

    // 获取Hash配置信息
    fun getHashConfig(key: String, field: String): Any {
        return redisService.hget(key, field)
    }

    // 设置Hash配置信息 不过期
    fun setHashConfig(key: String, field: String, value: String) {
        redisService.hset(key, field, value)
    }

    // 设置Hash配置信息 过期
    fun setHashConfig(key: String, field: String, value: String, expire: Long) {
        redisService.hset(key, field, value, expire)
    }

    // 设置Hash配置信息-All 不过期
    fun setHashConfigAll(key: String, mapData: Map<String, Any>) {
        redisService.hsetAll(key, mapData)
    }

    // 设置Hash配置信息-All 过期
    fun setHashConfigAll(key: String, mapData: Map<String, Any>, expire: Long) {
        redisService.hsetAll(key, mapData, expire)
    }

    // 设置Set配置 不过期
    fun setSet(key: String, value: String): Long {
        return redisService.sset(key, value)
    }

    // 设置Set配置 过期
    fun setSet(key: String, value: String, expire: Long): Long {
        return redisService.sset(key, value, expire)
    }

    // 删除Set的元素
    fun setRemove(key: String, value: String): Long {
        return redisService.sremove(key, value)
    }

    // 获取Set的元素列表
    fun setMembers(key: String): Set<Any> {
        return redisService.smembers(key)
    }

    // 获取Set的size
    fun setSize(key: String): Long {
        return redisService.ssize(key)
    }
}