package com.cdzyhd.jbs.model

import cn.hutool.http.HttpUtil
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.ConvertTools
import com.cdzyhd.jbs.a.SpringUtil
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.model.weixin.JsSdk
import java.io.UnsupportedEncodingException
import java.lang.IllegalArgumentException
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.util.*

class WeiXinModel {

    // 公众号开发-url配置参数
    @Throws(Exception::class)
    fun gzhUrlConfig(url: String, platform: String): JsSdk {
        // 获取access_token
        val access_token: String = this.getAccessToken(platform)
        // 有效期2小时，存入redis，过期再重新取
        var ticket = ""
        // 从redis中获取
        val redisKey = "weChat_JsTicket_$platform"
        if (StaticBean.redisService.hasKey(redisKey)) {
            // redis中存在 从redis中获取
            ticket = StaticBean.redisService.get(redisKey).toString()
        } else {
            // 获取ticket
            val requestUrl = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?"
            val params = "access_token=$access_token&type=jsapi"
            val ticketJson = HttpUtil.get(requestUrl + params)
            System.out.println(ticketJson)
            ticket = JSONObject.parseObject(ticketJson).getString("ticket")
            // 存入redis中
            StaticBean.redisService.set(redisKey, ticket, 7000)
        }
        // 参数构造
        val jsSdk = JsSdk()
        jsSdk.appid = SpringUtil.getConfigInfo("applications.$platform.wechat.app_id")
        jsSdk.url = url
        val nonce_str = UUID.randomUUID().toString().substring(0, 20)
        jsSdk.noncestr = nonce_str
        val timestamp = (System.currentTimeMillis() / 1000).toString()
        jsSdk.timestamp = timestamp
        var signature: String? = ""
        //注意这里参数名必须全部小写，且必须有序
        val string1 = "jsapi_ticket=" + ticket +
                "&noncestr=" + nonce_str +
                "&timestamp=" + timestamp +
                "&url=" + url
        try {
            val crypt = MessageDigest.getInstance("SHA-1")
            crypt.reset()
            crypt.update(string1.toByteArray(charset("UTF-8")))
            signature = byteToHex(crypt.digest())
            jsSdk.signature = signature
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
        } catch (e: UnsupportedEncodingException) {
            e.printStackTrace()
        }
        return jsSdk
    }

    // 字节转16进制
    private fun byteToHex(hash: ByteArray): String {
        val formatter = Formatter()
        for (b in hash) {
            formatter.format("%02x", b)
        }
        val result = formatter.toString()
        formatter.close()
        return result
    }

    /**
     * 公众号-小程序-获取accessToken
     *
     * @param platform_id 平台编号
     */
    @Throws(java.lang.Exception::class)
    fun getAccessToken(platform_id: String): String {
        val appId = SpringUtil.getConfigInfo("applications.$platform_id.wechat.app_id")
        val appSecret = SpringUtil.getConfigInfo("applications.$platform_id.wechat.app_secret")
        // 从redis中获取
        val redisKey = "weChat_accessToken_$platform_id"
        return if (StaticBean.redisService.hasKey(redisKey)) {
            // redis中存在 从redis中获取
            StaticBean.redisService.get(redisKey).toString()
        } else {
            // 从微信服务器获取
            val url =
                "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=$appId&secret=$appSecret"
            val accessTokenStr = HttpUtil.get(url)
            val accessTokenMap = ConvertTools.StringToHashMap(accessTokenStr)
            if (accessTokenMap.containsKey("access_token")) {
                val accessToken = accessTokenMap["access_token"].toString()
                val expireTime = accessTokenMap["expires_in"].toString().toInt() - 500 // 为了稳妥调用 减去500秒
                // 存入redis中
                StaticBean.redisService.set(redisKey, accessToken, expireTime.toLong())
                accessToken
            } else {
                throw IllegalArgumentException("获取accessToken失败，返回信息：$accessTokenStr")
            }
        }
    }

}