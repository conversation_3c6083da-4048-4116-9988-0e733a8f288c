package com.cdzyhd.jbs.model

import cn.hutool.http.HttpUtil
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.jbs.a.StaticBean
import com.cdzyhd.jbs.config.OrderPushRetryConfig
import com.cdzyhd.jbs.entity.OrderEntity
import java.util.*
import org.apache.commons.codec.digest.DigestUtils
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Service
import com.cdzyhd.jbs.entity.OrderPushTask
import org.springframework.data.redis.core.StringRedisTemplate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.transaction.annotation.Transactional
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler
import com.alibaba.fastjson.JSONArray
import com.cdzyhd.jbs.exception.CommonException

// 订单model
@Service
class OrderModel(
    private val objectMapper: ObjectMapper,
    private val redisTemplate: StringRedisTemplate,
    private val retryConfig: OrderPushRetryConfig,
    @Qualifier("orderPushScheduler")
    private val taskScheduler: ThreadPoolTaskScheduler
) {
    private val logger: Logger = LoggerFactory.getLogger(OrderModel::class.java)

    // 推送订单
    fun postOrderToUrl(order: OrderEntity): OutResponse<Any> {
        // 创建推送任务
        val task = OrderPushTask(
            taskId = UUID.randomUUID().toString(),
            order = order,
            nextExecuteTime = LocalDateTime.now(),
            status = OrderPushTask.TaskStatus.PENDING
        )

        // 保存任务到Redis并执行首次推送
        return executeOrderPushTask(task)
    }

    // 执行推送任务
    private fun executeOrderPushTask(task: OrderPushTask): OutResponse<Any> {
        try {
            // 更新任务状态为执行中
            task.status = OrderPushTask.TaskStatus.PROCESSING
            saveTaskToRedis(task)

            // 执行原有的推送逻辑
            val order = task.order
            val orderInfo = JSONObject().apply {
                // 必填字段
                put("organId", StaticBean.commonConfig.organId)
                put("scriptId", order.scriptId)
                put("appId", order.appId)
                put("userId", order.userId)
                put("totalAmount", order.totalAmount)
                put("payAmount", order.payAmount)
                put("originalOrderId", order.orderId)
                put("status", order.status)
                

                // 可选字段
                order.payType?.let { put("payType", it) }
                order.payTime?.let { put("payTime", it) }
                order.remark?.let { put("remark", it) }
                order.otherInfo?.let { put("otherInfo", it) }

                // 添加时间戳
                val timestamp = System.currentTimeMillis().toString()
                put("timestamp", timestamp)

                // 添加随机字符串，增加签名的唯一性
                val nonce = UUID.randomUUID().toString()
                put("nonce", nonce)

                // 生成签名
                val signStr = buildSignString(this, StaticBean.commonConfig.secretKey)
                put("sign", signStr)
            }

            val response = HttpUtil.createPost(StaticBean.commonConfig.zyhdOrderSysUrl)
                .body(orderInfo.toJSONString())
                .header("Content-Type", "application/json")
                .execute()

            return when {
                response.isOk -> {
                    val responseBody = JSONObject.parseObject(response.body())
                    if (responseBody.getString("code") == "000000") {
                        // 推送成功，清理Redis数据
                        task.status = OrderPushTask.TaskStatus.SUCCESS
                        cleanupTaskRedisData(task.taskId)
                        
                        // 更新订单状态
                        order.pushed = true
                        order.pushedTime = System.currentTimeMillis()
                        StaticBean.orderRepository.save(order)
                        OutResponse("000000", "订单推送成功", null)
                    } else {
                        // 推送失败，进入重试队列
                        handlePushFailure(task, "订单推送失败: ${responseBody.getString("msg")}")
                        OutResponse("999999", "订单推送失败，已加入重试队列", null)
                    }
                }

                else -> {
                    // HTTP错误，进入重试队列
                    handlePushFailure(task, "订单推送失败: HTTP ${response.status}")
                    OutResponse("999999", "订单推送失败，已加入重试队列", null)
                }
            }
        } catch (e: Exception) {
            // 异常情况，进入重试队列
            handlePushFailure(task, "订单推送异常: ${e.message}")
            return OutResponse("999999", "订单推送异常，已加入重试队列", null)
        }
    }

    // 处理推送失败的情况
    private fun handlePushFailure(task: OrderPushTask, failReason: String) {
        task.apply {
            status = OrderPushTask.TaskStatus.FAILED
            this.failReason = failReason
            retryCount++

            // 计算下次执行时间
            val retryInterval = if (retryCount <= retryConfig.retryIntervals.size) {
                retryConfig.retryIntervals[retryCount - 1]
            } else {
                // 超过配置的重试次数，清理Redis
                val key = "${retryConfig.redisKeyPrefix}:${task.taskId}"
                redisTemplate.delete(key)
                redisTemplate.opsForZSet().remove(retryConfig.taskQueueKey, task.taskId)
                return
            }

            nextExecuteTime = LocalDateTime.now().plusSeconds(retryInterval)
            updateTime = LocalDateTime.now()
        }

        // 保存任务到Redis
        saveTaskToRedis(task)
        // 将任务添加到重试队列
        addToRetryQueue(task)
    }

    // 保存任务到Redis
    private fun saveTaskToRedis(task: OrderPushTask) {
        val key = "${retryConfig.redisKeyPrefix}:${task.taskId}"
        redisTemplate.opsForValue().set(key, objectMapper.writeValueAsString(task))
    }

    // 添加任务到重试队列
    private fun addToRetryQueue(task: OrderPushTask) {
        val score = task.nextExecuteTime.toEpochSecond(ZoneOffset.UTC).toDouble()
        redisTemplate.opsForZSet().add(
            retryConfig.taskQueueKey,
            task.taskId,
            score
        )
    }

    // 定时扫描重试队列
    @Scheduled(fixedDelay = 5000, initialDelay = 5000)
    @Transactional
    fun scanRetryQueue() {
        try {
            val now = LocalDateTime.now().toEpochSecond(ZoneOffset.UTC)
            
            val taskIds = redisTemplate.opsForZSet()
                .rangeByScore(retryConfig.taskQueueKey, 0.0, now.toDouble())
                ?: return

            logger.debug("Found {} tasks to retry", taskIds.size)
            
            // 使用线程池并发处理任务
            taskIds.forEach { taskId ->
                taskScheduler.execute {
                    try {
                        processRetryTask(taskId)
                    } catch (e: Exception) {
                        logger.error("Failed to process retry task $taskId", e)
                    }
                }
            }
        } catch (e: Exception) {
            logger.error("Error scanning retry queue", e)
        }
    }

    private fun processRetryTask(taskId: String) {
        taskScheduler.execute {
            try {
                val taskKey = "${retryConfig.redisKeyPrefix}:$taskId"
                val taskJson = redisTemplate.opsForValue().get(taskKey) ?: return@execute
                
                val task = objectMapper.readValue(taskJson, OrderPushTask::class.java)
                redisTemplate.opsForZSet().remove(retryConfig.taskQueueKey, taskId)
                executeOrderPushTask(task)
            } catch (e: Exception) {
                logger.error("Failed to process retry task $taskId in thread pool", e)
            }
        }
    }

    // 生成签名
    private fun buildSignString(params: JSONObject, secretKey: String): String {
        // 将JSONObject转换为Map并过滤掉null值
        val sortedParams = params.entries
            .filter { it.value != null }
            .sortedBy { it.key }
            .associate { it.key to it.value }
        
        val stringToSign = sortedParams
            .filter { it.key != "sign" }
            .map { "${it.key}=${it.value}" }
            .joinToString("&")
        
        val finalString = "${stringToSign}&key=${secretKey}"
        
        val sign = DigestUtils.md5Hex(finalString).uppercase()
        
        return sign
    }

    // 可以添加一个方法来手动触发重试
    fun manualRetry(taskId: String) {
        taskScheduler.execute {
            try {
                processRetryTask(taskId)
            } catch (e: Exception) {
                logger.error("Manual retry failed for task $taskId", e)
            }
        }
    }

    // 新增清理Redis数据的辅助方法
    private fun cleanupTaskRedisData(taskId: String) {
        val key = "${retryConfig.redisKeyPrefix}:$taskId"
        redisTemplate.delete(key)
        redisTemplate.opsForZSet().remove(retryConfig.taskQueueKey, taskId)
        logger.debug("Cleaned up Redis data for successful task $taskId")
    }

    // 处理支付成功的订单
    fun handlePaymentSuccess(orderId: String,payAmount: Long) {
        try {
            // 1. 查询订单
            val order = StaticBean.orderRepository.findFirstByOrderId(orderId)
                ?: throw CommonException("order_not_found", "订单不存在")

            // 2. 更新订单状态
            if(order.status==1){
                throw CommonException("order_already_paid", "订单已支付")
            }
            order.status = 1  // 已支付
            order.payTime = Date().time
            if(payAmount!=order.totalAmount){
                throw CommonException("pay_amount_not_match", "支付金额与订单金额不匹配")
            }
            order.payAmount = payAmount // 已支付金额
            StaticBean.orderRepository.save(order)

            // 3. 激活用户的剧本
            UserModel().activateScript(order.userId, order.scriptId,order.orderId)

            // 4. 异步推送订单信息到上游系统
            taskScheduler.execute {
                try {
                    postOrderToUrl(order)
                } catch (e: Exception) {
                    logger.error("异步推送订单失败: ${e.message}", e)
                }
            }
        } catch (e: Exception) {
            logger.error("处理支付成功订单失败: ${e.message}", e)
            throw CommonException("payment_process_failed", "处理支付失败: ${e.message}")
        }
    }

    // 创建激活剧本的订单
    fun createActivationOrder(userId: String, scriptId: String): OrderEntity {
        // 1. 检查用户是否存在
        val user = StaticBean.userRepository.findFirstByUserId(userId)
            ?: throw CommonException("user_not_found", "用户不存在")
        
        // 2. 检查剧本是否存在
        val script = StaticBean.scriptRepository.findFirstByScriptId(scriptId)
            ?: throw CommonException("script_not_found", "剧本不存在")
        
        // 3. 检查是否已激活
        if (user.activatedScripts?.contains(scriptId) == true) {
            throw CommonException("already_activated", "您已激活过该剧本")
        }

         var order = OrderEntity()
        // 3.1 检查是否已存在未完成的激活订单
        val existingOrder = StaticBean.orderRepository.findFirstByUserIdAndScriptIdAndStatusAndDeleted(
            userId,
            scriptId,
            0,  // 待支付状态
            0   // 未删除
        )
        if (existingOrder != null) {
            order = existingOrder
        }
        
        // 4. 创建订单或修改激活订单
        order.userId = userId
        order.scriptId = scriptId
        order.appId = StaticBean.commonConfig.appId  // 从配置文件获取
        order.status = 0  // 待支付
        order.totalAmount = script.activationPrice  // 使用剧本的激活价格
        order.payAmount = 0
        order.createTime = Date().time
        order.updateTime = Date().time
        
        // 5. 保存订单
        StaticBean.orderRepository.save(order)

        // 6. 异步推送订单信息到上游系统
        taskScheduler.execute {
            try {
                postOrderToUrl(order)
            } catch (e: Exception) {
                logger.error("异步推送激活订单失败: ${e.message}", e)
            }
        }
        
        return order
    }
}