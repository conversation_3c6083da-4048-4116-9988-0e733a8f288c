package com.cdzyhd.jbs.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.PasswordUtil;
import com.cdzyhd.jbs.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 用户实体-学生和教师
 *
 * <AUTHOR>
 * @date 2022.7.8
 */
@Document(collection = "user")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UserEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String userId = new SnowflakeIdWorker(1, 0).nextId();

    // 所属机构ID
    public String organId;

    // 角色 todo 暂定
    public String role = "user";

    // 姓名
    public String name;

    // 昵称
    public String nickname;

    // 账号
    public String account;

    // 性别
    public String sex;

    // 绑定的邮箱
    public String email;

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 逻辑删除
    public Integer deleted = 0;

    // 密码 采用md5+salt验证
    @JsonIgnore
    public String password = PasswordUtil.generate("123456");

    // 是否已经登录过，修改密码，绑定邮箱过
    public Boolean hasLogin = false;

    // 手机号 todo 暂时
    public String phone;

    // 备注
    public String remark;

    // 微信公众号-openid
    public String openId;

    // 微信开放平台-unionId
    public String unionId;

    // 参与的剧本数量
    public Integer scriptRecordNumber = 0;

    // 完成的任务数量
    public Integer scriptTaskNumber = 0;

    // 获取的线索数量
    public Integer scriptClueNumber = 0;

    // 参与的决策数量
    public Integer scriptDecisionNumber = 0;

    // 已激活的剧本ID列表
    public JSONArray activatedScripts = new JSONArray();
    
    // 激活记录,格式: {
    //   "scriptId": {
    //     "activationTime": timestamp,
    //     "orderId": "xxx",
    //     "payAmount": 100
    //   }
    // }
    public JSONObject scriptActivationRecords = new JSONObject();
}