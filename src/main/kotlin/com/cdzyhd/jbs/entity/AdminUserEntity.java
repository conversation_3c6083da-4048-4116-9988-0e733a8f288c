package com.cdzyhd.jbs.entity;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.jbs.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "admin_user")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class AdminUserEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    /**
     * 管理员id
     * 使用snowflake生成 w1d0
     */
    public String adminUserId = new SnowflakeIdWorker(1, 0).nextId();

    // 用户名
    public String username;

    // 密码
    /**
     * 采用md5+salt验证
     */
    @JsonIgnore
    public String password;

    // 手机号
    public String phoneNumber;

    // 真实姓名
    public String realName;

    // 每个平台的权限角色
    public JSONObject roles = new JSONObject();

    // 扩展信息
    public JSONObject extraInfo = new JSONObject();

    // 逻辑删除标识
    public Integer deleted = 0;

}
