package com.cdzyhd.jbs.entity;

import com.cdzyhd.jbs.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 帮助实体
 *
 * <AUTHOR>
 * @date 2022.9.8
 */
@Document(collection = "help_question")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class HelpQuestionEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String helpQuestionId = new SnowflakeIdWorker(1, 0).nextId();

    // 所属机构ID
    public String organId;

    // 名称
    public String name;

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 内容
    public String content;

    // 类型
    public String type = "常见问题";
}