package com.cdzyhd.jbs.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 剧本记录
 * 用户进行剧本的记录
 *
 * <AUTHOR>
 * @date 2022.9.6
 */
@Document(collection = "script_record")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ScriptRecordEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String scriptRecordId = new SnowflakeIdWorker(1, 0).nextId();

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 关联的剧本id
    public String scriptId;

    // 关联用户id
    public String userId;

    // 任务得分
    public Double score;

    // 分数信息
    public JSONObject scoreInfo = new JSONObject();

    // 开始时间
    public Long startTime;

    // 结束时间
    public Long endTime;

    // 总用时
    public Long usedTime=0L;

    // 是否已完成此剧本
    public Boolean completed = false;

    // 当前的任务id
    public String nowTaskId;

    // 下一个任务id
    public String nextTaskId;

    // 选择的角色
    public String roleId;

    // 任务信息记录
    public JSONObject recordInfo = new JSONObject();

    // 任务额外信息
    public JSONObject extraInfo = new JSONObject();

    // 获取的线索数量
    public Integer clueNumber = 0;

    // 完成的决策数量
    public Integer decisionNumber = 0;

    // 完成的任务数量
    public Integer taskNumber = 0;

    // vo-关联的剧本信息
    public JSONObject scriptInfo = new JSONObject();

    // vo-管理用户信息
    public JSONArray userEntity;
}