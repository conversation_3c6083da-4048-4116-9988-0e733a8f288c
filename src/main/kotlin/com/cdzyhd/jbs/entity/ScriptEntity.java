package com.cdzyhd.jbs.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 剧本实体
 *
 * <AUTHOR>
 * @date 2022.9.6
 */
@Document(collection = "script")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ScriptEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String scriptId = new SnowflakeIdWorker(1, 0).nextId();

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 剧本名称
    public String name;

    // 头图
    public String avatarUrl;

    // 剧本标签
    public JSONArray tags = new JSONArray();

    // 是否开放
    public Boolean opened = true;

    // 所属系列id
    public String scriptSeriesId;

    // 支持的地点名称
    public String addressText;

    // 支持的地点信息-todo 扩展经纬度信息

    // 剧本简介
    public String shortDesText;

    // 剧本详细介绍文本
    public String desText;

    // 剧本帮助文本
    public String helpText;

    // 剧本激活需要的价格 单位分
    public Long activationPrice;

    // 关联学校

    // 剧本配置信息jsonObject
    public JSONObject configInfo = new JSONObject();

    // 剧本分数计算配置
    public JSONObject scoreConfig = new JSONObject();

    // vo 系列名称
    public String seriesNameVo;
}