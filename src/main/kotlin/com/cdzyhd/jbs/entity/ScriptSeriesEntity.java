package com.cdzyhd.jbs.entity;

import com.cdzyhd.jbs.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 剧本系列
 *
 * <AUTHOR>
 * @date 2022.9.6
 */
@Document(collection = "script_series")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ScriptSeriesEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String scriptSeriesId = new SnowflakeIdWorker(1, 0).nextId();

    // 名称
    public String name;

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 拥有的剧本数量
    public Integer scriptNumber;
}