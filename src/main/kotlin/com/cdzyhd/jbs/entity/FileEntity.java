package com.cdzyhd.jbs.entity;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 文件实体
 *
 * <AUTHOR>
 * @date 2022.7.7
 */
@Document(collection = "file")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class FileEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String fileId = new SnowflakeIdWorker(1, 0).nextId();

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 文件名称
    public String name;

    // 文件最后修改时间
    public Long lastModified;

    // 字节大小
    public Long byteSize;

    // 文件类型 存储前端的file中的type
    public String fileType;

    // 存储位置
    public String location;

    // 用途 scoreInfo_qr 客户端成绩截图二维码分享
    public String purpose;

    // 其他信息
    public JSONObject info;

}