package com.cdzyhd.jbs.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.base.common.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * 订单实体
 *
 * <AUTHOR>
 * @date 2024.3.21
 */
@Document(collection = "order")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class OrderEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // 创建时间
    public Long createTime = new Date().getTime();

    // 更新时间
    public Long updateTime = new Date().getTime();

    // id
    public String orderId = new SnowflakeIdWorker(1, 0).nextId();


    // 关联剧本id
    public String scriptId;

    // 关联appid
    public String appId;

    // 关联独立平台userId
    public String userId;

    // 订单状态：0-待支付，1-已支付，2-已取消，3-已退款，4-已完成
    public Integer status = 0;

    // 订单总金额（分）
    public Long totalAmount;

    // 实付金额（分）
    public Long payAmount;

    // 支付时间
    public Long payTime;

    // 支付方式：1-微信，2-支付宝
    public Integer payType;

    // 订单备注
    public String remark;

    // 支付流水号
    public String paymentNo;

    // 逻辑删除标识：0-未删除，1-已删除
    public Integer deleted = 0;

    // 是否已处理支付成功回调
    public Boolean payNotifyJobDone = false;

    // 是否推送成功 
    public Boolean pushed = false;

    // 推送时间
    public Long pushedTime;

    // 其它信息
    public JSONObject otherInfo = new JSONObject();
} 