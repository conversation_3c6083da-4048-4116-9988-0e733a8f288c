package com.cdzyhd.jbs.entity;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.jbs.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 机构实体
 *
 * <AUTHOR>
 * @date 2024.12.27
 */
@Document(collection = "organization")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class OrganizationEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // 机构ID
    public String organId = new SnowflakeIdWorker(1, 0).nextId();

    // 机构名称
    public String name;

    // 机构代码
    public String code;

    // 机构状态 (0-禁用, 1-启用)
    public Integer status = 1;

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 机构配置信息
    public JSONObject configInfo = new JSONObject();

    // 逻辑删除
    public Integer deleted = 0;

    // 机构描述
    public String description;

    // 联系人
    public String contactPerson;

    // 联系电话
    public String contactPhone;

    // 联系邮箱
    public String contactEmail;
}
