package com.cdzyhd.jbs.aop;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.URLUtil;
import com.cdzyhd.base.common.util.OutResponse;
import com.cdzyhd.jbs.entity.SysLogEntity;
import com.cdzyhd.jbs.event.SysLogEvent;
import com.cdzyhd.jbs.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.useragent.UserAgentUtil;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.util.Objects;

/**
 * @Classname SysLogAspect
 * ①切面注解得到请求数据 -> ②发布监听事件 -> ③异步监听日志入库
 */
@Slf4j
@Aspect
@Component
public class SysLogAspect {

    private ThreadLocal<SysLogEntity> sysLogThreadLocal = new ThreadLocal<>();

    /**
     * 事件发布是由ApplicationContext对象管控的，我们发布事件前需要注入ApplicationContext对象调用publishEvent方法完成事件发布
     **/
    @Autowired
    private ApplicationContext applicationContext;

    /***
     * 定义controller切入点拦截规则，拦截SysLog注解的方法
     */
    @Pointcut("@annotation(com.cdzyhd.jbs.annotation.SysOperaLog)")
    public void sysLogAspect() {

    }

    /***
     * 拦截控制层的操作日志
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Before(value = "sysLogAspect()")
    public void recordLog(JoinPoint joinPoint) throws Throwable {
        SysLogEntity sysLogEntity = new SysLogEntity();
        //将当前实体保存到threadLocal
        sysLogThreadLocal.set(sysLogEntity);
        // 开始时间
        long beginTime = Instant.now().toEpochMilli();
        HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        if (request.getAttribute("adminUserId") != null) {
            sysLogEntity.setUserId(request.getAttribute("adminUserId").toString());
        }
        if (request.getAttribute("uid") != null) {
            sysLogEntity.setUserId(request.getAttribute("uid").toString());
        }
        sysLogEntity.setActionUrl(URLUtil.getPath(request.getRequestURI()));
        sysLogEntity.setStartTime(System.currentTimeMillis());
        String ip = ServletUtil.getClientIP(request);
        sysLogEntity.setIp(ip);
        // todo 获取ip归属地
        sysLogEntity.setRequestMethod(request.getMethod());
        String uaStr = request.getHeader("user-agent");
        sysLogEntity.setBrowser(UserAgentUtil.parse(uaStr).getBrowser().toString());
        sysLogEntity.setOs(UserAgentUtil.parse(uaStr).getOs().toString());
        //访问目标方法的参数 可动态改变参数值
        Object[] args = joinPoint.getArgs();
        //获取执行的方法名
        sysLogEntity.setActionMethod(joinPoint.getSignature().getName());
        // 类名
        sysLogEntity.setClassPath(joinPoint.getTarget().getClass().getName());
        sysLogEntity.setActionMethod(joinPoint.getSignature().getName());
        sysLogEntity.setFinishTime(System.currentTimeMillis());
        // 参数
        sysLogEntity.setParams(args);
        sysLogEntity.setDescription(LogUtil.getControllerMethodDescription(joinPoint).get("description").toString());
        sysLogEntity.setModuleName(LogUtil.getControllerMethodDescription(joinPoint).get("moduleName").toString());
        sysLogEntity.setMethodName(LogUtil.getControllerMethodDescription(joinPoint).get("methodName").toString());
        long endTime = Instant.now().toEpochMilli();
        sysLogEntity.setConsumingTime(endTime - beginTime);
    }

    /**
     * 返回通知
     *
     * @param ret
     * @throws Throwable
     */
    @AfterReturning(returning = "ret", pointcut = "sysLogAspect()")
    public void doAfterReturning(Object ret) {
        //得到当前线程的log对象
        SysLogEntity sysLogEntity = sysLogThreadLocal.get();
        // 处理完请求，返回内容
        OutResponse r = Convert.convert(OutResponse.class, ret);
        if ("000000".equals(r.getCode())) {
            // 正常返回
            sysLogEntity.setType(1);
        } else {
            sysLogEntity.setType(2);
            sysLogEntity.setExDetail(r.getMsg());
        }
        // 发布事件
        applicationContext.publishEvent(new SysLogEvent(sysLogEntity));
        //移除当前log实体
        sysLogThreadLocal.remove();
    }

    /**
     * 异常通知
     *
     * @param e
     */
    @AfterThrowing(pointcut = "sysLogAspect()", throwing = "e")
    public void doAfterThrowable(Throwable e) {
        SysLogEntity sysLogEntity = sysLogThreadLocal.get();
        // 异常
        sysLogEntity.setType(2);
        // 异常对象
        sysLogEntity.setExDetail(LogUtil.getStackTrace(e));
        // 异常信息
        sysLogEntity.setExDesc(e.getMessage());
        // 发布事件
        applicationContext.publishEvent(new SysLogEvent(sysLogEntity));
        //移除当前log实体
        sysLogThreadLocal.remove();
    }

}
