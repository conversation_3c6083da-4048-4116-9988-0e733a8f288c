// import parseTime, formatTime and set to filter
import {date_format} from "@/utils/common";
import enumsIndex from "@/enums/index"

export {parseTime, formatTime} from '@/utils'

/**
 * Show plural label if time is plural number
 * @param {number} time
 * @param {string} label
 * @return {string}
 */
function pluralize(time, label) {
  if (time === 1) {
    return time + label
  }
  return time + label + 's'
}

/**
 * @param {number} time
 */
export function timeAgo(time) {
  const between = Date.now() / 1000 - Number(time)
  if (between < 3600) {
    return pluralize(~~(between / 60), ' minute')
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), ' hour')
  } else {
    return pluralize(~~(between / 86400), ' day')
  }
}

/**
 * Number formatting
 * like 10000 => 10k
 * @param {number} num
 * @param {number} digits
 */
export function numberFormatter(num, digits) {
  const si = [
    {value: 1E18, symbol: 'E'},
    {value: 1E15, symbol: 'P'},
    {value: 1E12, symbol: 'T'},
    {value: 1E9, symbol: 'G'},
    {value: 1E6, symbol: 'M'},
    {value: 1E3, symbol: 'k'}
  ]
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (num / si[i].value).toFixed(digits).replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol
    }
  }
  return num.toString()
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter(num) {
  return (+num || 0).toString().replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

/**
 * Upper case first char
 * @param {String} string
 */
export function uppercaseFirst(string) {
  return string.charAt(0).toUpperCase() + string.slice(1)
}

// 日期格式化
export function dateFormat(value, format) {
  if (value) {
    let formatString = "yyyy-MM-dd HH:mm"
    if (format) {
      formatString = format
    }
    return date_format(value, formatString)
  }
  return ""
}

// 数字格式化
export function numberFormat(value, num) {
  if (value || value === 0) {
    let number = 0
    if (num) {
      number = num
    }
    return value.toFixed(number)
  }
  return ""
}

// 分数格式化
export function scoreFormat(value, num) {
  if (value || value === 0) {
    let number = 0
    if (num) {
      number = num
    }
    return value.toFixed(number)
  }
  return ""
}

// 文件大小格式化
export function byteFormat(value) {
  if (value < 1024) {
    return value + "B"
  }
  if (value > 1024 && value < 1024 * 1024) {
    return (value / 1024).toFixed(2) + "KB"
  } else {
    return (value / 1024 / 1024).toFixed(2) + "MB"
  }
}

// 字符串分行
export function stringBrFilter(value) {
  let arr = value.split("\n")
  let result = ""
  arr.forEach(li => {
    result += `<p>${li}</p>`
  })
  return result
}

// 课程实验分数用时格式化
export function scoreUseTimeFilter(value) {
  if (value < 60) {
    return Number(value).toFixed(1)+"秒"
  } else {
    let min = Math.floor(value / 60)
    let sec = Math.floor(value - min * 60)
    return `${min}分${sec}秒`
  }
}

// 角色名称格式化
export function rolesNameFilter(value) {
  let rolesName = []
  value.forEach(li => {
    rolesName.push(enumsIndex.roles[li])
  })
  return rolesName.join(",")
}
