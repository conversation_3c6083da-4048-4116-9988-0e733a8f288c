!function(){"use strict";var n=function(e){return parseInt(e,10)},r=function(e,t,n){return{major:e,minor:t,patch:n}},o=function(e){var t=/([0-9]+)\.([0-9]+)\.([0-9]+)(?:(\-.+)?)/.exec(e);return t?r(n(t[1]),n(t[2]),n(t[3])):r(0,0,0)},i=function(e,t){var n=e-t;return 0===n?0:0<n?1:-1},e=function(e,t){return-1===function(e,t){var n=i(e.major,t.major);if(0!==n)return n;var r=i(e.minor,t.minor);if(0!==r)return r;var o=i(e.patch,t.patch);return 0!==o?o:0}((n=e)?o([(r=n).majorVersion,r.minorVersion].join(".").split(".").slice(0,3).join(".")):null,o(t));var n,r},S=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t]},g=function(n,r){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return n(r.apply(null,e))}},v=function(e){return function(){return e}},l=function(e){return e};function L(r){for(var o=[],e=1;e<arguments.length;e++)o[e-1]=arguments[e];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=o.concat(e);return r.apply(null,n)}}var t,a,u,c,s,f=function(e){return function(){throw new Error(e)}},d=v(!1),m=v(!0),p=d,h=m,y=function(){return b},b=(c={fold:function(e,t){return e()},is:p,isSome:p,isNone:h,getOr:u=function(e){return e},getOrThunk:a=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){},or:u,orThunk:a,map:y,ap:y,each:function(){},bind:y,flatten:y,exists:p,forall:h,filter:y,equals:t=function(e){return e.isNone()},equals_:t,toArray:function(){return[]},toString:v("none()")},Object.freeze&&Object.freeze(c),c),x=function(n){var e=function(){return n},t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:h,isNone:p,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return x(e(n))},ap:function(e){return e.fold(y,function(e){return x(e(n))})},each:function(e){e(n)},bind:r,flatten:e,exists:r,forall:r,filter:function(e){return e(n)?o:b},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(p,function(e){return t(n,e)})},toArray:function(){return[n]},toString:function(){return"some("+n+")"}};return o},N={some:x,none:y,from:function(e){return null==e?b:x(e)}},T=function(t){return function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&Array.prototype.isPrototypeOf(e)?"array":"object"===t&&String.prototype.isPrototypeOf(e)?"string":t}(e)===t}},E=T("string"),w=T("object"),I=T("array"),C=T("boolean"),O=T("function"),D=T("number"),_=void 0===(s=Array.prototype.indexOf)?function(e,t){return B(e,t)}:function(e,t){return s.call(e,t)},A=function(e,t){return-1<_(e,t)},P=function(e,t){return U(e,t).isSome()},k=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o,e)}return r},M=function(e,t){for(var n=0,r=e.length;n<r;n++)t(e[n],n,e)},R=function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var i=e[r];t(i,r,e)&&n.push(i)}return n},F=function(e,t,n){return M(e,function(e){n=t(n,e)}),n},j=function(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];if(t(o,n,e))return N.some(o)}return N.none()},U=function(e,t){for(var n=0,r=e.length;n<r;n++)if(t(e[n],n,e))return N.some(n);return N.none()},B=function(e,t){for(var n=0,r=e.length;n<r;++n)if(e[n]===t)return n;return-1},Y=Array.prototype.push,H=function(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!Array.prototype.isPrototypeOf(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);Y.apply(t,e[n])}return t},W=function(e,t){var n=k(e,t);return H(n)},q=function(e,t){for(var n=0,r=e.length;n<r;++n)if(!0!==t(e[n],n,e))return!1;return!0},$=Array.prototype.slice,V=(O(Array.from)&&Array.from,function(e){var n=N.none(),t=[],r=function(e){o()?a(e):t.push(e)},o=function(){return n.isSome()},i=function(e){M(e,a)},a=function(t){n.each(function(e){setTimeout(function(){t(e)},0)})};return e(function(e){n=N.some(e),i(t),t=[]}),{get:r,map:function(n){return V(function(t){r(function(e){t(n(e))})})},isReady:o}}),X={nu:V,pure:function(t){return V(function(e){e(t)})}},G=function(t){var e=function(e){var r;t((r=e,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=this;setTimeout(function(){r.apply(n,e)},0)}))},n=function(){return X.nu(e)};return{map:function(r){return G(function(n){e(function(e){var t=r(e);n(t)})})},bind:function(n){return G(function(t){e(function(e){n(e).get(t)})})},anonBind:function(n){return G(function(t){e(function(e){n.get(t)})})},toLazy:n,toCached:function(){var t=null;return G(function(e){null===t&&(t=n()),t.get(e)})},get:e}},z={nu:G,pure:function(t){return G(function(e){e(t)})}},K=function(a,e){return e(function(r){var o=[],i=0;0===a.length?r([]):M(a,function(e,t){var n;e.get((n=t,function(e){o[n]=e,++i>=a.length&&r(o)}))})})},Z=function(e){return K(e,z.nu)},J=function(e,t){var n=k(e,t);return Z(n)},Q=Object.keys,ee=function(e,t){for(var n=Q(e),r=0,o=n.length;r<o;r++){var i=n[r];t(e[i],i,e)}},te=function(e,r){return ne(e,function(e,t,n){return{k:t,v:r(e,t,n)}})},ne=function(r,o){var i={};return ee(r,function(e,t){var n=o(e,t,r);i[n.k]=n.v}),i},re=function(e){return n=function(e){return e},r=[],ee(e,function(e,t){r.push(n(e,t))}),r;var n,r},oe=function(e){return Q(e).length},ie=function(a){if(!I(a))throw new Error("cases must be an array");if(0===a.length)throw new Error("there must be at least one case");var u=[],n={};return M(a,function(e,r){var t=Q(e);if(1!==t.length)throw new Error("one and only one name per case");var o=t[0],i=e[o];if(void 0!==n[o])throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!I(i))throw new Error("case arguments must be an array");u.push(o),n[o]=function(){var e=arguments.length;if(e!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+e);for(var n=new Array(e),t=0;t<n.length;t++)n[t]=arguments[t];return{fold:function(){if(arguments.length!==a.length)throw new Error("Wrong number of arguments to fold. Expected "+a.length+", got "+arguments.length);return arguments[r].apply(null,n)},match:function(e){var t=Q(e);if(u.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+u.join(",")+"\nActual: "+t.join(","));if(!q(u,function(e){return A(t,e)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+u.join(", "));return e[o].apply(null,n)},log:function(e){console.log(e,{constructors:u,constructor:o,params:n})}}}}),n},ae=Object.prototype.hasOwnProperty,ue=function(a){return function(){for(var e=new Array(arguments.length),t=0;t<e.length;t++)e[t]=arguments[t];if(0===e.length)throw new Error("Can't merge zero objects");for(var n={},r=0;r<e.length;r++){var o=e[r];for(var i in o)ae.call(o,i)&&(n[i]=a(n[i],o[i]))}return n}},ce=ue(function(e,t){return w(e)&&w(t)?ce(e,t):t}),se=ue(function(e,t){return t}),fe=ie([{blob:["id","imageresult","objurl"]},{url:["id","url","raw"]}]),le=se(fe,{cata:function(e,t,n){return e.fold(t,n)}});function de(e,t){return pe(document.createElement("canvas"),e,t)}function me(e){return e.getContext("2d")}function pe(e,t,n){return e.width=t,e.height=n,e}var ge={create:de,clone:function(e){var t;return me(t=de(e.width,e.height)).drawImage(e,0,0),t},resize:pe,get2dContext:me,get3dContext:function(e){var t=null;try{t=e.getContext("webgl")||e.getContext("experimental-webgl")}catch(e){}return t||(t=null),t}},ve={getWidth:function(e){return e.naturalWidth||e.width},getHeight:function(e){return e.naturalHeight||e.height}},he=window.Promise?window.Promise:function(){var e=function(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],s(e,n(o,this),n(a,this))},t=e.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(e){setTimeout(e,1)};function n(e,t){return function(){e.apply(t,arguments)}}var r=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)};function i(n){var r=this;null!==this._state?t(function(){var e=r._state?n.onFulfilled:n.onRejected;if(null!==e){var t;try{t=e(r._value)}catch(e){return void n.reject(e)}n.resolve(t)}else(r._state?n.resolve:n.reject)(r._value)}):this._deferreds.push(n)}function o(e){try{if(e===this)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var t=e.then;if("function"==typeof t)return void s(n(t,e),n(o,this),n(a,this))}this._state=!0,this._value=e,u.call(this)}catch(e){a.call(this,e)}}function a(e){this._state=!1,this._value=e,u.call(this)}function u(){for(var e=0,t=this._deferreds.length;e<t;e++)i.call(this,this._deferreds[e]);this._deferreds=null}function c(e,t,n,r){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.resolve=n,this.reject=r}function s(e,t,n){var r=!1;try{e(function(e){r||(r=!0,t(e))},function(e){r||(r=!0,n(e))})}catch(e){if(r)return;r=!0,n(e)}}return e.prototype.catch=function(e){return this.then(null,e)},e.prototype.then=function(n,r){var o=this;return new e(function(e,t){i.call(o,new c(n,r,e,t))})},e.all=function(){var u=Array.prototype.slice.call(1===arguments.length&&r(arguments[0])?arguments[0]:arguments);return new e(function(r,o){if(0===u.length)return r([]);var i=u.length;function a(t,e){try{if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if("function"==typeof n)return void n.call(e,function(e){a(t,e)},o)}u[t]=e,0==--i&&r(u)}catch(e){o(e)}}for(var e=0;e<u.length;e++)a(e,u[e])})},e.resolve=function(t){return t&&"object"==typeof t&&t.constructor===e?t:new e(function(e){e(t)})},e.reject=function(n){return new e(function(e,t){t(n)})},e.race=function(o){return new e(function(e,t){for(var n=0,r=o.length;n<r;n++)o[n].then(e,t)})},e}(),ye="undefined"!=typeof window?window:Function("return this;")(),be=function(e,t){return function(e,t){for(var n=null!=t?t:ye,r=0;r<e.length&&null!=n;++r)n=n[e[r]];return n}(e.split("."),t)},xe=function(e,t){return function(e,t){for(var n,r,o=void 0!==t?t:ye,i=0;i<e.length;++i)n=o,r=e[i],void 0!==n[r]&&null!==n[r]||(n[r]={}),o=n[r];return o}(e.split("."),t)},Te={getOrDie:function(e,t){var n=be(e,t);if(null==n)throw e+" not available on this browser";return n}};function Ee(e,t){return new(Te.getOrDie("Blob"))(e,t)}function we(){return new(Te.getOrDie("FileReader"))}function Ie(e){return new(Te.getOrDie("Uint8Array"))(e)}var Se={atob:function(e){return Te.getOrDie("atob")(e)},requestAnimationFrame:function(e){Te.getOrDie("requestAnimationFrame")(e)}};function Le(u){return new he(function(e,t){var n=URL.createObjectURL(u),r=new Image,o=function(){r.removeEventListener("load",i),r.removeEventListener("error",a)};function i(){o(),e(r)}function a(){o(),t("Unable to load data of type "+u.type+": "+n)}r.addEventListener("load",i),r.addEventListener("error",a),r.src=n,r.complete&&i()})}function Ne(r){return new he(function(e,n){var t=new XMLHttpRequest;t.open("GET",r,!0),t.responseType="blob",t.onload=function(){200==this.status&&e(this.response)},t.onerror=function(){var e,t=this;n(0===this.status?((e=new Error("No access to download image")).code=18,e.name="SecurityError",e):new Error("Error "+t.status+" downloading image"))},t.send()})}function Ce(e){var t=e.split(","),n=/data:([^;]+)/.exec(t[0]);if(!n)return N.none();for(var r=n[1],o=t[1],i=Se.atob(o),a=i.length,u=Math.ceil(a/1024),c=new Array(u),s=0;s<u;++s){for(var f=1024*s,l=Math.min(f+1024,a),d=new Array(l-f),m=f,p=0;m<l;++p,++m)d[p]=i[m].charCodeAt(0);c[s]=Ie(d)}return N.some(Ee(c,{type:r}))}function Oe(n){return new he(function(e,t){Ce(n).fold(function(){t("uri is not base64: "+n)},e)})}function De(n){return new he(function(e){var t=we();t.onloadend=function(){e(t.result)},t.readAsDataURL(n)})}var _e={blobToImage:Le,imageToBlob:function(e){var t=e.src;return 0===t.indexOf("data:")?Oe(t):Ne(t)},blobToArrayBuffer:function(n){return new he(function(e){var t=we();t.onloadend=function(){e(t.result)},t.readAsArrayBuffer(n)})},blobToDataUri:De,blobToBase64:function(e){return De(e).then(function(e){return e.split(",")[1]})},dataUriToBlobSync:Ce,canvasToBlob:function(e,n,r){return n=n||"image/png",HTMLCanvasElement.prototype.toBlob?new he(function(t){e.toBlob(function(e){t(e)},n,r)}):Oe(e.toDataURL(n,r))},canvasToDataURL:function(e,t,n){return t=t||"image/png",e.then(function(e){return e.toDataURL(t,n)})},blobToCanvas:function(e){return Le(e).then(function(e){var t,n;return t=e,URL.revokeObjectURL(t.src),n=ge.create(ve.getWidth(e),ve.getHeight(e)),ge.get2dContext(n).drawImage(e,0,0),n})},uriToBlob:function(e){return 0===e.indexOf("blob:")?Ne(e):0===e.indexOf("data:")?Oe(e):null}};function Ae(e,t,n){var r=t.type;function o(t,n){return e.then(function(e){return _e.canvasToDataURL(e,t,n)})}return{getType:v(r),toBlob:function(){return he.resolve(t)},toDataURL:function(){return n},toBase64:function(){return n.split(",")[1]},toAdjustedBlob:function(t,n){return e.then(function(e){return _e.canvasToBlob(e,t,n)})},toAdjustedDataURL:o,toAdjustedBase64:function(e,t){return o(e,t).then(function(e){return e.split(",")[1]})},toCanvas:function(){return e.then(ge.clone)}}}function Pe(t){return _e.blobToDataUri(t).then(function(e){return Ae(_e.blobToCanvas(t),t,e)})}var ke={fromBlob:Pe,fromCanvas:function(t,e){return _e.canvasToBlob(t,e).then(function(e){return Ae(he.resolve(t),e,t.toDataURL())})},fromImage:function(e){return _e.imageToBlob(e).then(function(e){return Pe(e)})},fromBlobAndUrlSync:function(e,t){return Ae(_e.blobToCanvas(e),e,t)}},Me=function(e){return e.toBlob()},Re={blobToImageResult:function(e){return ke.fromBlob(e)},fromBlobAndUrlSync:function(e,t){return ke.fromBlobAndUrlSync(e,t)},imageToImageResult:function(e){return ke.fromImage(e)},imageResultToBlob:function(e,t,n){return void 0===t&&void 0===n?Me(e):e.toAdjustedBlob(t,n)},imageResultToOriginalBlob:Me,imageResultToDataURL:function(e){return e.toDataURL()}},Fe=function(e){return parseInt(e,10)},je=function(e,t,n){return{major:v(e),minor:v(t),patch:v(n)}},Ue={getTinymceVersion:function(){var e,t,n=[tinymce.majorVersion,tinymce.minorVersion].join(".");return e=n.split(".").slice(0,3).join("."),(t=/([0-9]+)\.([0-9]+)\.([0-9]+)(?:(\-.+)?)/.exec(e))?je(Fe(t[1]),Fe(t[2]),Fe(t[3])):je(0,0,0)}};function Be(c){var s=function(e,t){return v(4===(o=Ue.getTinymceVersion()).major()&&o.minor()<6?e:e+"."+(n=t.toLowerCase(),(r={"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png"}).hasOwnProperty(n)?r[n]:"dat"));var n,r,o};return{importImages:function(e){var t=W(e,function(e){return le.cata(e,function(e,t,n){var r,o,i,a,u=Re.imageResultToDataURL(t);return[(r=e,o=t,i=u,a=n,z.nu(function(t){Re.imageResultToOriginalBlob(o).then(function(e){c.editorUpload.blobCache.add({id:v(r),name:v(r),filename:s(r,e.type),blob:v(e),base64:v(i.split(",")[1]),blobUri:v(a),uri:v(null)}),t(e)})}))]},v([]))});return Z(t)},uploadImages:function(){c.uploadImages()},prepareImages:S,getLocalURL:function(e,t,n){return Re.imageResultToDataURL(t)}}}function Ye(e){var t=!1;return function(){t||(t=!0,e.apply(null,arguments))}}var He={"cement.dialog.paste.title":"Paste Formatting Options","cement.dialog.paste.instructions":"Choose to keep or remove formatting in the pasted content.","cement.dialog.paste.merge":"Keep Formatting","cement.dialog.paste.clean":"Remove Formatting","cement.dialog.flash.title":"Additional step needed to paste images","cement.dialog.flash.trigger-paste":"Your browser requires you to take one more action to paste the images in your content. Please press the below keys to complete the image paste:","cement.dialog.flash.missing":'Adobe Flash is required to import images from Microsoft Office. Install the <a href="http://get.adobe.com/flashplayer/" target="_blank">Adobe Flash Player</a>.',"cement.dialog.flash.press-escape":'Press <span class="ephox-polish-help-kbd">"Close"</span> to paste your content without images.',"loading.wait":"Please wait...","flash.clipboard.no.rtf":tinymce.Env.mac&&tinymce.Env.webkit?'Your browser security settings may be preventing images from being imported. <a href="https://support.ephox.com/entries/59328357-Safari-6-1-and-7-Flash-Sandboxing" style="text-decoration: underline">More information on paste for Safari</a>':"Your browser security settings may be preventing images from being imported.","safari.imagepaste":'Safari does not support direct paste of images. <a href="https://support.ephox.com/entries/88543243-Safari-Direct-paste-of-images-does-not-work" style="text-decoration: underline">More information on image pasting for Safari</a>',"webview.imagepaste":'Safari does not support direct paste of images. <a href="https://support.ephox.com/entries/88543243-Safari-Direct-paste-of-images-does-not-work" style="text-decoration: underline">More information on image pasting for Safari</a>',"error.code.images.not.found":"The images service was not found: (","error.imageupload":"Image failed to upload: (","error.full.stop":").","errors.local.images.disallowed":"Local image paste has been disabled. Local images have been removed from pasted content.","flash.crashed":"Images have not been imported as Adobe Flash appears to have crashed. This may be caused by pasting large documents.","errors.imageimport.failed":"Some images failed to import.","errors.imageimport.unsupported":"Unsupported image type.","errors.imageimport.invalid":"Image is invalid."},We={translate:function(e){return tinymce.translate(He[e])}},qe={showDialog:function(e,t){var n,r={title:"Error",spacing:10,padding:10,items:[{type:"container",html:t}],buttons:[{text:"Ok",onclick:function(){n.close()}}]};n=e.windowManager.open(r)}},$e=function(e){return e.replace(/\./g,"-")},Ve=function(e,t){return e+"-"+t},Xe=function(e){var n=$e(e);return{resolve:function(e){var t=e.split(" ");return k(t,function(e){return Ve(n,e)}).join(" ")}}},Ge={resolve:Xe("ephox-salmon").resolve},ze=Ge.resolve("upload-image-in-progress"),Ke="data-"+Ge.resolve("image-blob"),Ze="data-"+Ge.resolve("image-upload"),Je={uploadInProgress:v(ze),blobId:v(Ke),trackedImage:v(Ze)},Qe=(Node.ATTRIBUTE_NODE,Node.CDATA_SECTION_NODE,Node.COMMENT_NODE),et=Node.DOCUMENT_NODE,tt=(Node.DOCUMENT_TYPE_NODE,Node.DOCUMENT_FRAGMENT_NODE,Node.ELEMENT_NODE),nt=Node.TEXT_NODE,rt=(Node.PROCESSING_INSTRUCTION_NODE,Node.ENTITY_REFERENCE_NODE,Node.ENTITY_NODE,Node.NOTATION_NODE,function(e){return e.dom().nodeName.toLowerCase()}),ot=function(e){return e.dom().nodeType},it=function(t){return function(e){return ot(e)===t}},at=function(e){return ot(e)===Qe||"#comment"===rt(e)},ut=it(tt),ct=it(nt),st=function(e,t,n){if(!(E(n)||C(n)||D(n)))throw console.error("Invalid call to Attr.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")},ft=function(e,t,n){st(e.dom(),t,n)},lt=function(e,t){var n=e.dom();ee(t,function(e,t){st(n,t,e)})},dt=function(e,t){var n=e.dom().getAttribute(t);return null===n?void 0:n},mt=function(e,t){var n=e.dom();return!(!n||!n.hasAttribute)&&n.hasAttribute(t)},pt=function(e,t){e.dom().removeAttribute(t)},gt=function(e,t){var n=dt(e,t);return void 0===n||""===n?[]:n.split(" ")},vt=function(e){return void 0!==e.dom().classList},ht=function(e){return gt(e,"class")},yt=function(e,t){return o=t,i=gt(n=e,r="class").concat([o]),ft(n,r,i.join(" ")),!0;var n,r,o,i},bt=function(e,t){return o=t,0<(i=R(gt(n=e,r="class"),function(e){return e!==o})).length?ft(n,r,i.join(" ")):pt(n,r),!1;var n,r,o,i},xt=function(e,t){vt(e)?e.dom().classList.add(t):yt(e,t)},Tt=function(e,t){var n;vt(e)?e.dom().classList.remove(t):bt(e,t),0===(vt(n=e)?n.dom().classList:ht(n)).length&&pt(n,"class")},Et=function(e,t){return vt(e)&&e.dom().classList.contains(t)},wt=function(e){if(null==e)throw new Error("Node cannot be null or undefined");return{dom:v(e)}},It={fromHtml:function(e,t){var n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||1<n.childNodes.length)throw console.error("HTML does not have a single root node",e),"HTML must have a single root node";return wt(n.childNodes[0])},fromTag:function(e,t){var n=(t||document).createElement(e);return wt(n)},fromText:function(e,t){var n=(t||document).createTextNode(e);return wt(n)},fromDom:wt,fromPoint:function(e,t,n){var r=e.dom();return N.from(r.elementFromPoint(t,n)).map(wt)}},St=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];if(t.length!==n.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+n.length+" arguments");var r={};return M(t,function(e,t){r[e]=v(n[t])}),r}},Lt=function(e){return e.slice(0).sort()},Nt=function(t,e){if(!I(e))throw new Error("The "+t+" fields must be an array. Was: "+e+".");M(e,function(e){if(!E(e))throw new Error("The value "+e+" in the "+t+" fields was not a string.")})},Ct=function(o,i){var n,a=o.concat(i);if(0===a.length)throw new Error("You must specify at least one required or optional field.");return Nt("required",o),Nt("optional",i),n=Lt(a),j(n,function(e,t){return t<n.length-1&&e===n[t+1]}).each(function(e){throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+n.join(", ")+"].")}),function(t){var n=Q(t);q(o,function(e){return A(n,e)})||function(e,t){throw new Error("All required keys ("+Lt(e).join(", ")+") were not specified. Specified keys were: "+Lt(t).join(", ")+".")}(o,n);var e=R(n,function(e){return!A(a,e)});0<e.length&&function(e){throw new Error("Unsupported keys for object: "+Lt(e).join(", "))}(e);var r={};return M(o,function(e){r[e]=v(t[e])}),M(i,function(e){r[e]=v(Object.prototype.hasOwnProperty.call(t,e)?N.some(t[e]):N.none())}),r}},Ot=function(e,t){for(var n=[],r=function(e){return n.push(e),t(e)},o=t(e);(o=o.bind(r)).isSome(););return n},Dt=function(n){var r,o=!1;return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return o||(o=!0,r=n.apply(null,e)),r}},_t=function(){return At(0,0)},At=function(e,t){return{major:e,minor:t}},Pt={nu:At,detect:function(e,t){var n=String(t).toLowerCase();return 0===e.length?_t():function(e,t){var n=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.test(t))return r}}(e,t);if(!n)return{major:0,minor:0};var r=function(e){return Number(t.replace(n,"$"+e))};return At(r(1),r(2))}(e,n)},unknown:_t},kt="Firefox",Mt=function(e,t){return function(){return t===e}},Rt=function(e){var t=e.current;return{current:t,version:e.version,isEdge:Mt("Edge",t),isChrome:Mt("Chrome",t),isIE:Mt("IE",t),isOpera:Mt("Opera",t),isFirefox:Mt(kt,t),isSafari:Mt("Safari",t)}},Ft={unknown:function(){return Rt({current:void 0,version:Pt.unknown()})},nu:Rt,edge:v("Edge"),chrome:v("Chrome"),ie:v("IE"),opera:v("Opera"),firefox:v(kt),safari:v("Safari")},jt="Windows",Ut="Android",Bt="Solaris",Yt="FreeBSD",Ht=function(e,t){return function(){return t===e}},Wt=function(e){var t=e.current;return{current:t,version:e.version,isWindows:Ht(jt,t),isiOS:Ht("iOS",t),isAndroid:Ht(Ut,t),isOSX:Ht("OSX",t),isLinux:Ht("Linux",t),isSolaris:Ht(Bt,t),isFreeBSD:Ht(Yt,t)}},qt={unknown:function(){return Wt({current:void 0,version:Pt.unknown()})},nu:Wt,windows:v(jt),ios:v("iOS"),android:v(Ut),linux:v("Linux"),osx:v("OSX"),solaris:v(Bt),freebsd:v(Yt)},$t=function(e,t){var n=String(t).toLowerCase();return j(e,function(e){return e.search(n)})},Vt=function(e,n){return $t(e,n).map(function(e){var t=Pt.detect(e.versionRegexes,n);return{current:e.name,version:t}})},Xt=function(e,n){return $t(e,n).map(function(e){var t=Pt.detect(e.versionRegexes,n);return{current:e.name,version:t}})},Gt=function(e,t,n){return""===t||!(e.length<t.length)&&e.substr(n,n+t.length)===t},zt=function(e,t){return Qt(e,t)?(n=e,r=t.length,n.substring(r)):e;var n,r},Kt=function(e,t){return en(e,t)?(n=e,r=t.length,n.substring(0,n.length-r)):e;var n,r},Zt=function(e,t){return-1!==e.indexOf(t)},Jt=function(n){return(e=n,""===e?N.none():N.some(e.substr(0,1))).bind(function(t){return(e=n,""===e?N.none():N.some(e.substring(1))).map(function(e){return t.toUpperCase()+e});var e}).getOr(n);var e},Qt=function(e,t){return Gt(e,t,0)},en=function(e,t){return Gt(e,t,e.length-t.length)},tn=function(e){return e.replace(/^\s+|\s+$/g,"")},nn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,rn=function(t){return function(e){return Zt(e,t)}},on=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return Zt(e,"edge/")&&Zt(e,"chrome")&&Zt(e,"safari")&&Zt(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,nn],search:function(e){return Zt(e,"chrome")&&!Zt(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return Zt(e,"msie")||Zt(e,"trident")}},{name:"Opera",versionRegexes:[nn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:rn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:rn("firefox")},{name:"Safari",versionRegexes:[nn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(Zt(e,"safari")||Zt(e,"mobile/"))&&Zt(e,"applewebkit")}}],an=[{name:"Windows",search:rn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return Zt(e,"iphone")||Zt(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:rn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:rn("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:rn("linux"),versionRegexes:[]},{name:"Solaris",search:rn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:rn("freebsd"),versionRegexes:[]}],un={browsers:v(on),oses:v(an)},cn=function(e){var t,n,r,o,i,a,u,c,s,f,l,d=un.browsers(),m=un.oses(),p=Vt(d,e).fold(Ft.unknown,Ft.nu),g=Xt(m,e).fold(qt.unknown,qt.nu);return{browser:p,os:g,deviceType:(n=p,r=e,o=(t=g).isiOS()&&!0===/ipad/i.test(r),i=t.isiOS()&&!o,a=t.isAndroid()&&3===t.version.major,u=t.isAndroid()&&4===t.version.major,c=o||a||u&&!0===/mobile/i.test(r),s=t.isiOS()||t.isAndroid(),f=s&&!c,l=n.isSafari()&&t.isiOS()&&!1===/safari/i.test(r),{isiPad:v(o),isiPhone:v(i),isTablet:v(c),isPhone:v(f),isTouch:v(s),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:v(l)})}},sn={detect:Dt(function(){var e=navigator.userAgent;return cn(e)})},fn=tt,ln=et,dn=function(e,t){var n=e.dom();if(n.nodeType!==fn)return!1;if(void 0!==n.matches)return n.matches(t);if(void 0!==n.msMatchesSelector)return n.msMatchesSelector(t);if(void 0!==n.webkitMatchesSelector)return n.webkitMatchesSelector(t);if(void 0!==n.mozMatchesSelector)return n.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},mn=function(e){return e.nodeType!==fn&&e.nodeType!==ln||0===e.childElementCount},pn=function(e,t){var n=void 0===t?document:t.dom();return mn(n)?[]:k(n.querySelectorAll(e),It.fromDom)},gn=function(e,t){var n=void 0===t?document:t.dom();return mn(n)?N.none():N.from(n.querySelector(e)).map(It.fromDom)},vn=function(e,t){return e.dom()===t.dom()},hn=(sn.detect().browser.isIE(),dn),yn=function(e){return It.fromDom(e.dom().ownerDocument)},bn=function(e){var t=e.dom();return N.from(t.parentNode).map(It.fromDom)},xn=function(e,t){for(var n=O(t)?t:v(!1),r=e.dom(),o=[];null!==r.parentNode&&void 0!==r.parentNode;){var i=r.parentNode,a=It.fromDom(i);if(o.push(a),!0===n(a))break;r=i}return o},Tn=function(e){var t=e.dom();return N.from(t.previousSibling).map(It.fromDom)},En=function(e){var t=e.dom();return N.from(t.nextSibling).map(It.fromDom)},wn=function(e){return t=Ot(e,Tn),(n=$.call(t,0)).reverse(),n;var t,n},In=function(e){var t=e.dom();return k(t.childNodes,It.fromDom)},Sn=function(e){return t=0,n=e.dom().childNodes,N.from(n[t]).map(It.fromDom);var t,n},Ln=function(e){return e.dom().childNodes.length},Nn=(St("element","offset"),function(e,t){var n=(t||document).createElement("div");return n.innerHTML=e,In(It.fromDom(n))}),Cn=function(t,n){bn(t).each(function(e){e.dom().insertBefore(n.dom(),t.dom())})},On=function(e,t){En(e).fold(function(){bn(e).each(function(e){_n(e,t)})},function(e){Cn(e,t)})},Dn=function(t,n){Sn(t).fold(function(){_n(t,n)},function(e){t.dom().insertBefore(n.dom(),e.dom())})},_n=function(e,t){e.dom().appendChild(t.dom())},An=function(e,t){Cn(e,t),_n(t,e)},Pn=function(r,o){M(o,function(e,t){var n=0===t?r:o[t-1];On(n,e)})},kn=function(t,e){M(e,function(e){_n(t,e)})},Mn=function(e){var t=ct(e)?e.dom().parentNode:e.dom();return null!=t&&t.ownerDocument.body.contains(t)},Rn=function(e,t){var n=[];return M(In(e),function(e){t(e)&&(n=n.concat([e])),n=n.concat(Rn(e,t))}),n},Fn=function(e,t){return pn(t,e)},jn=function(e){Tt(e,Je.uploadInProgress())},Un=function(e){for(var t=0;t<e.undoManager.data.length;t++){var n=e.undoManager.data[t].content,r=It.fromTag("div");kn(r,Nn(n));var o=Fn(r,"."+Je.uploadInProgress());M(o,jn),e.undoManager.data[t].content=r.dom().innerHTML}},Bn=function(e,t,n){for(var r=0;r<e.undoManager.data.length;r++)e.undoManager.data[r].content=e.undoManager.data[r].content.split(t.objurl()).join(n.location)},Yn=function(){return Te.getOrDie("URL")},Hn=function(e){return Yn().createObjectURL(e)},Wn=function(e){Yn().revokeObjectURL(e)},qn=St("id","imageresult","objurl");function $n(){var o={},n=function(e){Wn(e.objurl())};return{add:function(e,t,n){var r=qn(e,t,n);return o[e]=r},get:function(e){return N.from(o[e])},remove:function(e){var t=o[e];delete o[e],void 0!==t&&n(t)},lookupByData:function(t){return function(e,t){for(var n=Q(e),r=0,o=n.length;r<o;r++){var i=n[r],a=e[i];if(t(a,i,e))return N.some(a)}return N.none()}(o,function(e){return Re.imageResultToDataURL(e.imageresult())===t})},destroy:function(){ee(o,n),o={}}}}function Vn(e){var n=St.apply(null,e),r=[];return{bind:function(e){if(void 0===e)throw"Event bind error: undefined handler";r.push(e)},unbind:function(t){r=R(r,function(e){return e!==t})},trigger:function(){var t=n.apply(null,arguments);M(r,function(e){e(t)})}}}var Xn={create:function(e){return{registry:te(e,function(e){return{bind:e.bind,unbind:e.unbind}}),trigger:te(e,function(e){return e.trigger})}}},Gn=Je.trackedImage(),zn=function(e,t){return Fn(e,"img["+Gn+'="'+t+'"]')},Kn=function(e){return Fn(e,"img:not(["+Gn+"])["+Je.blobId()+"]")};function Zn(){var o=[],i=[],e=Xn.create({complete:Vn(["response"])}),a=function(){e.trigger.complete(i),i=[]},u=function(){return 0<o.length};return{findById:zn,findAll:Kn,register:function(e,t){ft(e,Gn,t),o.push(t)},report:function(e,t,r){var n;M(t,function(e){var t,n;pt(e,Gn),t=r,n=e,i.push({success:t,element:n.dom()})}),n=e,o=R(o,function(e,t){return e!==n}),!1===u()&&a()},inProgress:u,isActive:function(e){return A(o,e)},events:e.registry}}var Jn=function(n){return{is:function(e){return n===e},isValue:m,isError:d,getOr:v(n),getOrThunk:v(n),getOrDie:v(n),or:function(e){return Jn(n)},orThunk:function(e){return Jn(n)},fold:function(e,t){return t(n)},map:function(e){return Jn(e(n))},each:function(e){e(n)},bind:function(e){return e(n)},exists:function(e){return e(n)},forall:function(e){return e(n)},toOption:function(){return N.some(n)}}},Qn=function(n){return{is:d,isValue:d,isError:m,getOr:l,getOrThunk:function(e){return e()},getOrDie:function(){return f(String(n))()},or:function(e){return e},orThunk:function(e){return e()},fold:function(e,t){return e(n)},map:function(e){return Qn(n)},each:S,bind:function(e){return Qn(n)},exists:d,forall:m,toOption:N.none}},er={value:Jn,error:Qn},tr=function(t,e){M(e,function(e){xt(t,e)})},nr=function(e){return vt(e)?function(e){for(var t=e.dom().classList,n=new Array(t.length),r=0;r<t.length;r++)n[r]=t.item(r);return n}(e):ht(e)},rr=function(t){return function(e){return Et(e,t)}},or=function(e,t,n){for(var r=e.dom(),o=O(n)?n:v(!1);r.parentNode;){r=r.parentNode;var i=It.fromDom(r);if(t(i))return N.some(i);if(o(i))break}return N.none()},ir=function(e,t){return j(e.dom().childNodes,g(t,It.fromDom)).map(It.fromDom)},ar=function(e,r){var o=function(e){for(var t=0;t<e.childNodes.length;t++){if(r(It.fromDom(e.childNodes[t])))return N.some(It.fromDom(e.childNodes[t]));var n=o(e.childNodes[t]);if(n.isSome())return n}return N.none()};return o(e.dom())},ur=function(e){return gn(e)},cr=function(e,t,n){return or(e,function(e){return dn(e,t)},n)},sr=function(e,t){return gn(t,e)},fr=function(e,t,n){return r=cr,a=n,dn(o=e,i=t)?N.some(o):O(a)&&a(o)?N.none():r(o,i,a);var r,o,i,a},lr=St("image","blobInfo"),dr=ie([{failure:["error"]},{success:["result","images","blob"]}]),mr=function(e,t,n,r,o){var i=Re.imageResultToDataURL(n),a=e.lookupByData(i).getOrThunk(function(){return e.add(t,n,r)});return ft(o,Je.blobId(),a.id()),lr(o,a)},pr=function(t,n){return t.get(n).fold(function(){return er.error("Internal error with blob cache")},function(e){return t.remove(n),er.value(e)})},gr=function(e,t,n){var r=e.isActive(t);return e.register(n,t),xt(n,Je.uploadInProgress()),r?N.none():N.some(t)},vr=function(e,n,a,r,u,t,c){var s=function(){console.error("Internal error with blob cache",u),c(dr.failure({status:v(666)}))};e.upload(t,u,function(e){var t,i=n.findById(r,u);M(i,(t=Je.uploadInProgress(),function(e){Tt(e,t)})),e.fold(function(e){c(dr.failure(e))},function(t){var e,n,r,o;(e=a,n=i,r=u,o=t,M(n,function(e){ft(e,"src",o.location),pt(e,Je.blobId())}),pr(e,r)).fold(s,function(e){c(dr.success(t,i,e))})}),n.report(u,i,e.isValue())})},hr=function(o,i,e){return W(e,function(e){return le.cata(e,function(t,n,r){return sr(i,'img[src="'+r+'"]').fold(function(){return[er.error("Image that was just inserted could not be found: "+r)]},function(e){return[er.value(mr(o,t,n,r,e))]})},v([]))})},yr=function(e,o,t){var n=e.findAll(t);return e.inProgress()?[]:k(n,function(e){return t=o,r=dt(n=e,Je.blobId()),t.get(r).fold(function(){return er.error(r)},function(e){return er.value(lr(n,e))});var t,n,r})},br=ie([{get:[]},{post:[]},{put:[]},{del:[]}]),xr={get:br.get,post:br.post,put:br.put,del:br.del},Tr=function(e,t,n,r){var o=e.bind(function(e){return e.match({file:function(){return N.none()},form:function(){return N.some("application/x-www-form-urlencoded; charset=UTF-8")},json:function(){return N.some("application/json")},plain:function(){return N.some("text/plain")},html:function(){return N.some("text/html")}})}),i=n.match({none:N.none,xhr:v(N.some(!0))}),a=t.match({json:N.none,blob:v(N.some("blob")),xml:v(N.some("document")),html:v(N.some("document")),text:v(N.some("text"))}),u=t.match({json:v("application/json, text/javascript"),blob:v("application/octet-stream"),text:v("text/plain"),html:v("text/html"),xml:v("application/xml, text/xml")})+", */*; q=0.01",c=r;return{contentType:v(o),credentials:v(i),responseType:v(a),accept:v(u),headers:v(c)}},Er=function(n,e){e.contentType().each(function(e){n.setRequestHeader("Content-Type",e)});var t=e.accept();n.setRequestHeader("Accept",t),e.credentials().each(function(e){n.withCredentials=e}),e.responseType().each(function(e){n.responseType=e});var r=e.headers();ee(r,function(e,t){E(t)||E(e)?n.setRequestHeader(t,e):console.error("Request header data was not a string: ",t," -> ",e)})},wr=function(t){return z.nu(function(n){var e=we();e.onload=function(e){var t=e.target;n(t.result)},e.readAsText(t)})},Ir=function(){return Te.getOrDie("JSON")},Sr={parse:function(e){return Ir().parse(e)},stringify:function(e,t,n){return Ir().stringify(e,t,n)}},Lr=function(e){try{var t=Sr.parse(e);return er.value(t)}catch(e){return er.error("Response was not JSON")}},Nr=Ct(["message","status","responseText"],[]),Cr=function(e){var t=Nr(e);return t.toString=t.message,t},Or={handle:function(n,e,r){var t=function(){return z.pure(r.response)};return e.match({json:function(){return Lr(r.response).fold(t,z.pure)},blob:function(){return e=r,N.from(e.response).map(wr).getOr(z.pure("no response content"));var e},text:t,html:t,xml:t}).map(function(e){var t=0===r.status?"Unknown HTTP error (possible cross-domain request)":'Could not load url "'+n+'": '+r.statusText;return Cr({message:t,status:r.status,responseText:e})})},nu:Cr},Dr=function(){return(Dr=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},_r=function(i){return Dr({},i,{toCached:function(){return _r(i.toCached())},bindFuture:function(t){return _r(i.bind(function(e){return e.fold(function(e){return z.pure(er.error(e))},function(e){return t(e)})}))},bindResult:function(t){return _r(i.map(function(e){return e.bind(t)}))},mapResult:function(t){return _r(i.map(function(e){return e.map(t)}))},foldResult:function(t,n){return i.map(function(e){return e.fold(t,n)})},withTimeout:function(e,o){return _r(z.nu(function(t){var n=!1,r=window.setTimeout(function(){n=!0,t(er.error(o()))},e);i.get(function(e){n||(window.clearTimeout(r),t(e))})}))}})},Ar=function(e){return _r(z.nu(e))},Pr=function(e){return _r(z.pure(er.value(e)))},kr=Ar,Mr=Pr,Rr=function(e){return _r(z.pure(er.error(e)))},Fr=function(e,t){var n,r,o=function(){return Mr(t.response)},i=function(e){return Or.nu({message:e,status:t.status,responseText:t.responseText})};return e.match({json:function(){return Lr(t.response).fold(function(e){return Rr(i(e))},Mr)},blob:o,text:o,html:o,xml:(n="Invalid XML documentManage",r=o,function(){return r().bindResult(function(e){return null===e?er.error(i(n)):er.value(e)})})})},jr=function(i,a,u,c,s,e){var f=void 0!==e?e:{};return kr(function(t){var e=a.match({get:v("get"),put:v("put"),post:v("post"),del:v("delete")}),n=new(Te.getOrDie("XMLHttpRequest"));n.open(e,i,!0);var r=Tr(u,c,s,f);Er(n,r);var o=function(){Or.handle(i,c,n).get(function(e){t(er.error(e))})};n.onload=function(){0!==n.status||Qt(i,"file:")?n.status<100||400<=n.status?o():Fr(c,n).get(t):o()},n.onerror=o,u.fold(function(){n.send()},function(e){var t=e.match({file:l,form:l,json:Sr.stringify,plain:l,html:l});n.send(t)})}).toLazy()},Ur=ie([{file:["data"]},{form:["data"]},{json:["data"]},{plain:["data"]},{html:["data"]}]),Br={file:Ur.file,form:Ur.form,json:Ur.json,plain:Ur.plain,html:Ur.html},Yr=ie([{none:[]},{xhr:[]}]),Hr={none:Yr.none,xhr:Yr.xhr},Wr=ie([{json:[]},{blob:[]},{text:[]},{html:[]},{xml:[]}]),qr={json:Wr.json,blob:Wr.blob,text:Wr.text,html:Wr.html,xml:Wr.xml,cata:function(e,t,n,r,o,i){return e.match({json:t,blob:n,text:r,html:o,xml:i})}},$r=St("message","status","contents"),Vr=["jpg","png","gif","jpeg"],Xr={failureObject:$r,getFilename:function(e,t){return E(e.name)&&!en(e.name,".tmp")?e.name:function(e,t){if(E(e.type)&&Qt(e.type,"image/")){var n=e.type.substr("image/".length);return A(Vr,n)?t+"."+n:t}return t}(e,t)},buildData:function(e,t,n){var r=new(Te.getOrDie("FormData"));return r.append(e,t,n),r}},Gr=function(e){var t="";return""!==e.protocol&&(t+=e.protocol,t+=":"),""!==e.authority&&(t+="//",t+=e.authority),t+=e.path,""!==e.query&&(t+="?",t+=e.query),""!==e.anchor&&(t+="#",t+=e.anchor),t},zr={strictMode:!1,key:["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],q:{name:"queryKey",parser:/(?:^|&)([^&=]*)=?([^&]*)/g},parser:{strict:/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@\/]*)(?::([^:@\/]*))?)?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,loose:/^(?:(?![^:@\/]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*)(?::([^:@\/]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/}},Kr=function(e,t){return function(e,t){for(var r=t,n=r.parser[r.strictMode?"strict":"loose"].exec(e),o={},i=14;i--;)o[r.key[i]]=n[i]||"";return o[r.q.name]={},o[r.key[12]].replace(r.q.parser,function(e,t,n){t&&(o[r.q.name][t]=n)}),o}(e,se(zr,t))},Zr=function(e){return Kt(e,Jr(e))},Jr=function(e){return e.substring(e.lastIndexOf("/"))},Qr=function(e){for(var t=e,n="";""!==t;)if(Qt(t,"../"))t=zt(t,"../");else if(Qt(t,"./"))t=zt(t,"./");else if(Qt(t,"/./"))t="/"+zt(t,"/./");else if("/."===t)t="/";else if(Qt(t,"/../"))t="/"+zt(t,"/../"),n=Zr(n);else if("/.."===t)t="/",n=Zr(n);else if("."===t||".."===t)t="";else{var r=t.match(/(^\/?.*?)(\/|$)/)[1];t=zt(t,r),n+=r}return n},eo=function(e,t,n){if(""!==n&&""===e)return"/"+t;var r=e.substring(e.lastIndexOf("/")+1);return Kt(e,r)+t},to=function(e,t){var n={strictMode:!0},r=Kr(e,n),o=Kr(t,n),i={};return""!==o.protocol?(i.protocol=o.protocol,i.authority=o.authority,i.path=Qr(o.path),i.query=o.query):(""!==o.authority?(i.authority=o.authority,i.path=Qr(o.path),i.query=o.query):(""===o.path?(i.path=r.path,""!==o.query?i.query=o.query:i.query=r.query):(Qt(o.path,"/")?i.path=Qr(o.path):(i.path=eo(r.path,o.path,e.authority),i.path=Qr(i.path)),i.query=o.query),i.authority=r.authority),i.protocol=r.protocol),i.anchor=o.anchor,i},no=function(e,t){var n=to(e,t);return Gr(n)};function ro(d){var e,t,n,r,m=(e=d.url,t=e.lastIndexOf("/"),n=0<t?e.substr(0,t):e,r=void 0===d.basePath?n:d.basePath,en(r,"/")?r:r+"/"),o=function(e,t,f){var n,r,o,i,a,u,l=Xr.getFilename(e,t),c=!0===d.credentials?Hr.xhr():Hr.none(),s=Br.file(Xr.buildData("image",e,l));(n=d.url,r=s,o=qr.text(),i=c,u=xr.post(),jr(n,u,N.some(r),o,i,a)).map(function(e){e.fold(function(e){f(er.error(Xr.failureObject(e.message(),e.status(),e.responseText())))},function(t){var n,e,r,o;try{var i=Sr.parse(t);if(!E(i.location))return e="JSON response did not contain a string location",r=500,o=t,void f(er.error(Xr.failureObject(e,r,o)));n=i.location}catch(e){n=t}var a,u,c,s=(a=l,u=n.split(/\s+/),c=1===u.length&&""!==u[0]?u[0]:a,no(m,c));f(er.value({location:s}))})})};return{upload:function(e,t,n){var r=e.imageresult();Re.imageResultToBlob(r).then(function(e){o(e,t,n)})}}}St("id","filename","blob","base64");var oo=function(e){return ro(e)},io=function(e){return e.getParam("powerpaste_block_drop",!1,"boolean")},ao=function(e){return void 0!==e.settings.images_upload_url},uo=function(r,e){var o,t,i,a,n,u=$n(),c=Zn(),s=(o=r,t=e.url,i=function(){return We.translate("error.code.images.not.found")+t+We.translate("error.full.stop")},a=function(){return We.translate("error.imageupload")+t+We.translate("error.full.stop")},n=function(e){var t=e.status(),n=0===t||400<=t||t<500?i:a;qe.showDialog(o,n())},{instance:function(){return Ye(n)}}),f=oo(e),l=function(){return It.fromDom(r.getBody())},d=function(t,e,n){M(e,function(e){ft(e,"data-mce-src",t.location)}),Bn(r,n,t)};c.events.complete.bind(function(e){Un(r)});var m=function(o,i){gr(c,o.blobInfo().id(),o.image()).each(function(e){var t,n,r;t=e,n=o.blobInfo(),r=i,vr(f,c,u,l(),t,n,function(e){e.fold(function(e){r(e)},d)})})};return{importImages:function(){return z.pure([])},uploadImages:function(e){var t,n,r,o,i;t=s.instance(),n=yr(c,u,l()),M(n,function(e){e.fold(function(e){c.report(e,N.none(),!1)},function(e){m(e,t)})}),r=e,o=s.instance(),i=hr(u,l(),r),M(i,function(e){e.fold(function(e){console.error(e)},function(e){m(e,o)})})},prepareImages:S,getLocalURL:function(e,t,n){return n}}},co=function(o){var e=Be(o);return{importImages:function(){return z.pure([])},uploadImages:S,prepareImages:function(e){M(e,function(e){le.cata(e,function(e,t,n){var r=Re.imageResultToDataURL(t);M(o.dom.select('img[src="'+n+'"]'),function(e){o.dom.setAttrib(e,"src",r)})},S)})},getLocalURL:e.getLocalURL}};function so(e){return void 0!==e.uploadImages?Be(e):function(e){if(ao(e)){var t={url:e.settings.images_upload_url,basePath:e.settings.images_upload_base_path,credentials:e.settings.images_upload_credentials};return uo(e,t)}return co(e)}(e)}var fo,lo,mo={each:tinymce.each,trim:tinymce.trim,bind:function(e,t){return function(){return e.apply(t,arguments)}},extend:function(n){for(var e=[],t=1;t<arguments.length;t++)e[t-1]=arguments[t];return tinymce.each(Array.prototype.slice.call(arguments,1),function(e){for(var t in e)n[t]=e[t]}),n},ephoxGetComputedStyle:function(e){return e.ownerDocument.defaultView?e.ownerDocument.defaultView.getComputedStyle(e,null):e.currentStyle||{}},log:function(e){"undefined"!=typeof console&&console.log&&console.log(e)},compose:function(e){var r=Array.prototype.slice.call(e).reverse();return function(e){for(var t=e,n=0;n<r.length;n++)t=(0,r[n])(t);return t}}},po=function(t,r,e,n){var o,i,a,u,c,s=t.selection,f=t.dom,l=t.getBody();if(c=e.isText(),e.reset(),n.clipboardData&&n.clipboardData.getData("text/html")){n.preventDefault();var d=n.clipboardData.getData("text/html"),m=d.match(/<html[\s\S]+<\/html>/i),p=null===m?d:m[0];return r(p)}if(!f.get("_mcePaste")){if(o=f.add(l,"div",{id:"_mcePaste",class:"mcePaste"},'\ufeff<br _mce_bogus="1">'),u=l!==t.getDoc().body?f.getPos(t.selection.getStart(),l).y:l.scrollTop,f.setStyles(o,{position:"absolute",left:-1e4,top:u,width:1,height:1,overflow:"hidden"}),tinymce.isIE)return(a=f.doc.body.createTextRange()).moveToElementText(o),a.execCommand("Paste"),f.remove(o),"\ufeff"===o.innerHTML?(t.execCommand("mcePasteWord"),void n.preventDefault()):(r(c?o.innerText:o.innerHTML),tinymce.dom.Event.cancel(n));var g=function(e){e.preventDefault()};if(f.bind(t.getDoc(),"mousedown",g),f.bind(t.getDoc(),"keydown",g),tinymce.isGecko&&(a=t.selection.getRng(!0)).startContainer===a.endContainer&&3===a.startContainer.nodeType){var v=f.select("p,h1,h2,h3,h4,h5,h6,pre",o);1===v.length&&f.remove(v.reverse(),!0)}i=t.selection.getRng(),o=o.firstChild,(a=t.getDoc().createRange()).setStart(o,0),a.setEnd(o,1),s.setRng(a),window.setTimeout(function(){var n="",e=f.select("div.mcePaste");mo.each(e,function(e){var t=e.firstChild;t&&"DIV"===t.nodeName&&t.style.marginTop&&t.style.backgroundColor&&f.remove(t,1),mo.each(f.select("div.mcePaste",e),function(e){f.remove(e,1)}),mo.each(f.select("span.Apple-style-span",e),function(e){f.remove(e,1)}),mo.each(f.select("br[_mce_bogus]",e),function(e){f.remove(e)}),n+=e.innerHTML}),mo.each(e,function(e){f.remove(e)}),i&&s.setRng(i),r(n),f.unbind(t.getDoc(),"mousedown",g),f.unbind(t.getDoc(),"keydown",g)},0)}},go={getOnPasteFunction:function(t,n,r){return function(e){po(t,n,r,e)}},getOnKeyDownFunction:function(t,n,r){return function(e){(tinymce.isOpera||0<navigator.userAgent.indexOf("Firefox/2"))&&((tinymce.isMac?e.metaKey:e.ctrlKey)&&86===e.keyCode||e.shiftKey&&45===e.keyCode)&&po(t,n,r,e)}}},vo={insert:function(e,t){var n,r=t.getDoc(),o="ephoxInsertMarker",i=t.selection,a=t.dom;i.setContent('<span id="'+o+'">&nbsp;</span>'),n=a.get(o);for(var u=r.createDocumentFragment();e.firstChild&&!a.isBlock(e.firstChild);)u.appendChild(e.firstChild);for(var c=r.createDocumentFragment();e.lastChild&&!a.isBlock(e.lastChild);)c.appendChild(e.lastChild);if(n.parentNode.insertBefore(u,n),a.insertAfter(c,n),e.firstChild){if(a.isBlock(e.firstChild)){for(;!a.isBlock(n.parentNode)&&n.parentNode!==a.getRoot();)n=a.split(n.parentNode,n);a.is(n.parentNode,"td,th")||n.parentNode===a.getRoot()||(n=a.split(n.parentNode,n))}a.replace(e,n)}else a.remove(n)}},ho={officeStyles:"prompt",htmlStyles:"clean"},yo={strip_class_attributes:"all",retain_style_properties:"none"},bo={strip_class_attributes:"none",retain_style_properties:"valid"},xo=function(e,t,n){var r=function(e,t){if(e&&"string"!=typeof e)return e;switch(e){case"clean":return yo;case"merge":return bo;default:return t}}(e,t);return r=mo.extend(r,{base_64_images:n})},To={create:function(e,t,n){var r=xo(e,yo,n),o=xo(t,bo,n),i=o;return{setWordContent:function(e){i=e?r:o},get:function(e){return i[e]}}}},Eo="startElement",wo="endElement",Io="text",So="comment",Lo=function(o){var i,t,a=0,u=function(){return i};t=function(){return i={},a=0,mo.each(o.attributes,function(e){var t,n=e.nodeName,r=e.value;(!1!==(t=e).specified||"name"===t.nodeName&&""!==t.value)&&null!=r&&(i[n]=r,a++)}),void 0===i.style&&o.style.cssText&&(i.style=o.style.cssText,a++),t=u,i};var c,s,f=function(n){mo.each(t(),function(e,t){n(t,e)})};return{get:function(e){return t()[e]},each:f,filter:function(e){var n,r;c||(s=t),r=e,c=(n=c)&&r?function(e,t){return r(e,n(e,t))}:n||r,t=function(){return t=s,f(function(e,t){var n=c(e,t);null===n?(o.removeAttribute(e),delete i[e],a--):n!==t&&("class"===e?o.className=n:o.setAttribute(e,n),i[e]=n)}),t=u,i}},getAttributes:function(){return t()},getAttributeCount:function(){return t(),a}}},No=function(e){return e.replace(/-(.)/g,function(e,t){return t.toUpperCase()})},Co=!1,Oo=function(i,e,t){var n,r,o,a,u,c,s,f,l,d;switch(i.nodeType){case 1:e?n=wo:(n=Eo,a=Lo(i),u={},c=i,s=function(e,t){u[e]=t},null!=(d=t||c.getAttribute("style"))&&d.split||(d=c.style.cssText),mo.each(d.split(";"),function(e){var t=e.indexOf(":");0<t&&((f=mo.trim(e.substring(0,t))).toUpperCase()===f&&(f=f.toLowerCase()),f=f.replace(/([A-Z])/g,function(e,t){return"-"+t.toLowerCase()}),l=mo.trim(e.substring(t+1)),Co||(Co=0===f.indexOf("mso-")),s(f,l))}),Co||(l=c.style["mso-list"])&&s("mso-list",l)),r="HTML"!==i.scopeName&&i.scopeName&&i.tagName&&i.tagName.indexOf(":")<=0?(i.scopeName+":"+i.tagName).toUpperCase():i.tagName;break;case 3:n=Io,o=i.nodeValue;break;case 8:n=So,o=i.nodeValue;break;default:mo.log("WARNING: Unsupported node type encountered: "+i.nodeType)}var m=function(){return n},p=function(e){n===Eo&&a.filter(e)};return{getNode:function(){return a&&a.getAttributes(),i},tag:function(){return r},type:m,text:function(){return o},toString:function(){return"Type: "+n+", Tag: "+r+" Text: "+o},getAttribute:function(e){return a.get(e.toLowerCase())},filterAttributes:p,filterStyles:function(r){if(m()===Eo){var o="";mo.each(u,function(e,t){var n=r(t,e);null===n?(i.style.removeProperty?i.style.removeProperty(No(t)):i.style.removeAttribute(No(t)),delete u[t]):(o+=t+": "+n+"; ",u[t]=n)}),o=o||null,p(function(e,t){return"style"===e?o:t}),i.style.cssText=o}},getAttributeCount:function(){return a.getAttributeCount()},attributes:function(e){a.each(e)},getStyle:function(e){return u[e]},styles:function(n){mo.each(u,function(e,t){n(t,e)})},getComputedStyle:function(){return mo.ephoxGetComputedStyle(i)},isWhitespace:function(){return n===Io&&/^[\s\u00A0]*$/.test(o)}}},Do=function(e,t){return Oo(t.createElement(e),!0)},_o=Do("HTML",window.document),Ao={START_ELEMENT_TYPE:Eo,END_ELEMENT_TYPE:wo,TEXT_TYPE:Io,COMMENT_TYPE:So,FINISHED:_o,token:Oo,createStartElement:function(e,t,n,r){var o=r.createElement(e),i="";return mo.each(t,function(e,t){o.setAttribute(t,e)}),mo.each(n,function(e,t){i+=t+":"+e+";",o.style[No(t)]=e}),Oo(o,!1,""!==i?i:null)},createEndElement:Do,createComment:function(e,t){return Oo(t.createComment(e),!1)},createText:function(e,t){return Oo(t.createTextNode(e))}},Po=function(i){var a=i.createDocumentFragment(),u=function(e){a.appendChild(e)};return{dom:a,receive:function(e){var t,n,r,o;switch(e.type()){case Ao.START_ELEMENT_TYPE:o=e.getNode().cloneNode(!1),u(r=o),a=r;break;case Ao.TEXT_TYPE:t=e,n=i.createTextNode(t.text()),u(n);break;case Ao.END_ELEMENT_TYPE:a=a.parentNode;break;case Ao.COMMENT_TYPE:break;default:throw{message:"Unsupported token type: "+e.type()}}}}},ko=function(e,o){var i;o=o||window.document,i=o.createElement("div"),o.body.appendChild(i),i.style.position="absolute",i.style.left="-10000px",i.innerHTML=e;var a=i.firstChild||Ao.FINISHED,u=[],c=!1;return{hasNext:function(){return void 0!==a},next:function(){var e,t,n=a,r=c;return!c&&a.firstChild?(u.push(a),a=a.firstChild):c||1!==a.nodeType?a.nextSibling?(a=a.nextSibling,c=!1):(a=u.pop(),c=!0):c=!0,n===Ao.FINISHED||a||(o.body.removeChild(i),a=Ao.FINISHED),t=r,(e=n)===Ao.FINISHED?e:e?Ao.token(e,t):void 0}}},Mo=function(p,g){return function(t,e,n){var r,o,i,a=!1,u=function(){g&&g(m),a=!1,o=[],i=[]},c=function(e){mo.each(e,function(e){t.receive(e)})},s=function(e){a?i.push(e):t.receive(e)},f=function(){l(),c(i),u()},l=function(){mo.each(r,function(e){s(e)}),d()},d=function(){r=[]},m={document:n||window.document,settings:e||{},emit:s,receive:function(e){g&&o.push(e),p(m,e),e===Ao.FINISHED&&f()},startTransaction:function(){a=!0},rollback:function(){c(o),u()},commit:f,defer:function(e){(r=r||[]).push(e)},hasDeferred:function(){return r&&0<r.length},emitDeferred:l,dropDeferred:d};return u(),m}},Ro=Mo,Fo=function(n){return Mo(function(e,t){t.filterAttributes(mo.bind(n,e)),e.emit(t)})},jo=/^(P|H[1-6]|T[DH]|LI|DIV|BLOCKQUOTE|PRE|ADDRESS|FIELDSET|DD|DT|CENTER)$/,Uo=function(){return null},Bo=!1,Yo=Ro(function(e,t){var n,r=function(){Bo||(e.emit(Ao.createStartElement("P",{},{},e.document)),Bo=!0)};switch(t.type()){case Ao.TEXT_TYPE:r(),e.emit(t);break;case Ao.END_ELEMENT_TYPE:Bo&&(n=t,jo.test(n.tag())||t===Ao.FINISHED)?(e.emit(Ao.createEndElement("P",e.document)),Bo=!1):"BR"===t.tag()&&e.emit(t);break;case Ao.START_ELEMENT_TYPE:"BR"===t.tag()?(t.filterAttributes(Uo),t.filterStyles(Uo),e.emit(t)):"IMG"===t.tag()&&t.getAttribute("alt")&&(r(),e.emit(Ao.createText(t.getAttribute("alt"),e.document)))}t===Ao.FINISHED&&e.emit(t)}),Ho=function(e){var t=e;return 65279===t.charCodeAt(t.length-1)?t.substring(0,t.length-1):e},Wo=[Ho],qo=tinymce.isIE&&9<=document.documentMode?[function(e){return e.replace(/<BR><BR>/g,"<br>")},function(e){return e.replace(/<br>/g," ")},function(e){return e.replace(/<br><br>/g,"<BR><BR>")},function(e){return/<(h[1-6r]|p|div|address|pre|form|table|tbody|thead|tfoot|th|tr|td|li|ol|ul|caption|blockquote|center|dl|dt|dd|dir|fieldset)/.test(e)?e.replace(/(?:<br>&nbsp;[\s\r\n]+|<br>)*(<\/?(h[1-6r]|p|div|address|pre|form|table|tbody|thead|tfoot|th|tr|td|li|ol|ul|caption|blockquote|center|dl|dt|dd|dir|fieldset)[^>]*>)(?:<br>&nbsp;[\s\r\n]+|<br>)*/g,"$1"):e}].concat(Wo):Wo,$o={all:mo.compose(qo),textOnly:Ho},Vo=/^(mso-.*|tab-stops|tab-interval|language|text-underline|text-effect|text-line-through|font-color|horiz-align|list-image-[0-9]+|separator-image|table-border-color-(dark|light)|vert-align|vnd\..*)$/,Xo=Ro(function(e,t){var r,n=e.settings.get("retain_style_properties");t.filterStyles((r=n,function(e,t){var n=!1;switch(r){case"all":case"*":n=!0;break;case"valid":n=!Vo.test(e);break;case void 0:case"none":n="list-style-type"===e;break;default:n=0<=(","+r+",").indexOf(","+e+",")}return n?t:null})),e.emit(t)}),Go=Ro(function(e,t){e.seenList||(e.inferring?"LI"===t.tag()&&(t.type()===Ao.START_ELEMENT_TYPE?e.inferring++:(e.inferring--,e.inferring||(e.needsClosing=!0))):("OL"===t.tag()||"UL"===t.tag()?e.seenList=!0:"LI"===t.tag()&&(e.inferring=1,e.needsClosing||e.emit(Ao.createStartElement("UL",{},{},e.document))),!e.needsClosing||e.inferring||t.isWhitespace()||(e.needsClosing=!1,e.emit(Ao.createEndElement("UL",e.document))))),e.emit(t)}),zo=Fo(function(e,t){return"name"===e||"id"===e?null:t}),Ko=Fo(function(e,t){if("class"===e)switch(this.settings.get("strip_class_attributes")){case"mso":return 0===t.indexOf("Mso")?null:t;case"none":return t;default:return null}return t}),Zo=function(){if(0<navigator.userAgent.indexOf("Gecko")&&navigator.userAgent.indexOf("WebKit")<0)return!1;var e=document.createElement("div");try{e.innerHTML='<p style="mso-list: Ignore;">&nbsp;</p>'}catch(e){return!1}return"Ignore"===Ao.token(e.firstChild).getStyle("mso-list")}(),Jo=function(e,t){return e.type()===Ao.START_ELEMENT_TYPE?0===e.getAttributeCount()||t&&1===e.getAttributeCount()&&null!==e.getAttribute("style")&&void 0!==e.getAttribute("style"):e.type()===Ao.END_ELEMENT_TYPE},Qo=Zo,ei=function(e){return"A"===e.tag()||"SPAN"===e.tag()},ti=function(e){var t=e.getStyle("mso-list");return t&&"skip"!==t},ni=[],ri=[],oi=!1,ii=function(e,t){var n,r,o=1;for(n=t+1;n<e;n++)if((r=ni[n])&&"SPAN"===r.tag())if(r.type()===Ao.START_ELEMENT_TYPE)o++;else if(r.type()===Ao.END_ELEMENT_TYPE&&0==--o)return void(ni[n]=null)},ai=function(e,t){if(ni.push(t),ri=ri||[],t.type()===Ao.START_ELEMENT_TYPE)ri.push(t);else if(t.type()===Ao.END_ELEMENT_TYPE&&(ri.pop(),0===ri.length))return void function(e){if(oi){var t=void 0,n=ni.length,r=void 0;for(r=0;r<n;r++)(t=ni[r])&&(t.type()===Ao.START_ELEMENT_TYPE&&"SPAN"===t.tag()&&Jo(t)?ii(n,r):e.emit(t))}ni=[],oi=!(ri=[])}(e)},ui=Ro(function(e,t){var n=function(e){return!(0<=",FONT,EM,STRONG,SAMP,ACRONYM,CITE,CODE,DFN,KBD,TT,B,I,U,S,SUB,SUP,INS,DEL,VAR,SPAN,".indexOf(","+e.tag()+",")&&Jo(e,!0))};0===(ni=ni||[]).length?t.type()===Ao.START_ELEMENT_TYPE?n(t)?e.emit(t):ai(e,t):e.emit(t):(oi||(oi=n(t)),ai(e,t))}),ci=Fo(function(e,t){return"style"===e&&""===t?null:t}),si=Fo(function(e,t){return"lang"===e?null:t}),fi=Ro(function(e,t){if("IMG"===t.tag()){if(t.type()===Ao.END_ELEMENT_TYPE&&e.skipEnd)return void(e.skipEnd=!1);if(t.type()===Ao.START_ELEMENT_TYPE){if(/^file:/.test(t.getAttribute("src")))return void(e.skipEnd=!0);if(e.settings.get("base_64_images")&&/^data:image\/.*;base64/.test(t.getAttribute("src")))return void(e.skipEnd=!0)}}e.emit(t)}),li=Ro(function(e,t){"META"!==t.tag()&&"LINK"!==t.tag()&&e.emit(t)}),di=function(e){return!Jo(e)&&!/^OLE_LINK/.test(e.getAttribute("name"))},mi=[],pi=Ro(function(e,t){var n;t.type()===Ao.START_ELEMENT_TYPE&&"A"===t.tag()?(mi.push(t),di(t)&&e.defer(t)):t.type()===Ao.END_ELEMENT_TYPE&&"A"===t.tag()?(n=mi.pop(),di(n)&&e.defer(t),0===mi.length&&e.emitDeferred()):e.hasDeferred()?e.defer(t):e.emit(t)}),gi=!1,vi=[Ro(function(e,t){"SCRIPT"===t.tag()?gi=t.type()===Ao.START_ELEMENT_TYPE:gi||(t.filterAttributes(function(e,t){return/^on/.test(e)||"language"===e?null:t}),e.emit(t))}),zo,fi,Xo,si,ci,Ko,pi,ui,li,Go],hi=Ro(function(e,n){n.filterAttributes(function(e,t){return"align"===e?null:"UL"!==n.tag()&&"OL"!==n.tag()||"type"!==e?t:null}),e.emit(n)}),yi=Fo(function(e,t){return/^xmlns(:|$)/.test(e)?null:t}),bi=Ro(function(e,t){t.tag&&/^([OVWXP]|U[0-9]+|ST[0-9]+):/.test(t.tag())||e.emit(t)}),xi=Fo(function(e,t){return"href"===e&&(0<=t.indexOf("#_Toc")||0<=t.indexOf("#_mso"))?null:t}),Ti=Fo(function(e,t){return/^v:/.test(e)?null:t}),Ei=[{regex:/^\(?[dc][\.\)]$/,type:{tag:"OL",type:"lower-alpha"}},{regex:/^\(?[DC][\.\)]$/,type:{tag:"OL",type:"upper-alpha"}},{regex:/^\(?M*(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})[\.\)]$/,type:{tag:"OL",type:"upper-roman"}},{regex:/^\(?m*(cm|cd|d?c{0,3})(xc|xl|l?x{0,3})(ix|iv|v?i{0,3})[\.\)]$/,type:{tag:"OL",type:"lower-roman"}},{regex:/^\(?[0-9]+[\.\)]$/,type:{tag:"OL"}},{regex:/^([0-9]+\.)*[0-9]+\.?$/,type:{tag:"OL",variant:"outline"}},{regex:/^\(?[a-z]+[\.\)]$/,type:{tag:"OL",type:"lower-alpha"}},{regex:/^\(?[A-Z]+[\.\)]$/,type:{tag:"OL",type:"upper-alpha"}}],wi={"\u2022":{tag:"UL",type:"disc"},"\xb7":{tag:"UL",type:"disc"},"\xa7":{tag:"UL",type:"square"}},Ii={o:{tag:"UL",type:"circle"},"-":{tag:"UL",type:"disc"},"\u25cf":{tag:"UL",type:"disc"}},Si=function(e,t){var n={tag:e.tag,type:e.type,variant:t};return e.start&&(n.start=e.start),e.type||delete n.type,n},Li=function(e,t,n){return e===t||e&&t&&e.tag===t.tag&&e.type===t.type&&(n||e.variant===t.variant)},Ni={guessListType:function(e,t,n){var r,o,i,a=null;return e&&(r=e.text,o=e.symbolFont),r=mo.trim(r),(a=Ii[r])?a=Si(a,r):o?a=(a=wi[r])?Si(a,r):{tag:"UL",variant:r}:(mo.each(Ei,function(e){if(e.regex.test(r)){if(t&&Li(e.type,t,!0))return(a=e.type).start=parseInt(r,10),!1;a||(a=e.type),a.start=parseInt(r,10)}}),a&&!a.variant&&(i="("===r.charAt(0)?"()":")"===r.charAt(r.length-1)?")":".",a=Si(a,i))),a&&"OL"===a.tag&&n&&("P"!==n.tag()||/^MsoHeading/.test(n.getAttribute("class")))&&(a=null),a},eqListType:Li,checkFont:function(e,t){if(e.type()===Ao.START_ELEMENT_TYPE){var n=e.getStyle("font-family");n?t="Wingdings"===n||"Symbol"===n:/^(P|H[1-6]|DIV)$/.test(e.tag())&&(t=!1)}return t}},Ci=function(e){var t=e.indexOf(".");if(0<=t&&void 0===mo.trim(e.substring(t+1)))return(void 0)[2],!1},Oi=(fo=function(e,t){var n,r=/([^{]+){([^}]+)}/g;for(r.lastIndex=0;null!==(n=r.exec(e));)mo.each(n[1].split(","),Ci(void 0));return!1},lo={},function(e,t){var n,r=e+","+t;return lo.hasOwnProperty(r)?lo[r]:(n=fo.call(null,e,t),lo[r]=n)}),Di=function(e,t){var n,r,o,i=!1,a=function(e){var t=e.style.fontFamily;t&&(i="Wingdings"===t||"Symbol"===t)};if(e.type()===Ao.START_ELEMENT_TYPE&&t.openedTag&&"SPAN"===e.tag()){for(a(n=t.openedTag.getNode()),1<n.childNodes.length&&"A"===n.firstChild.tagName&&""===n.firstChild.textContent&&(n=n.childNodes[1]);n.firstChild&&("SPAN"===n.firstChild.tagName||"A"===n.firstChild.tagName);)a(n=n.firstChild);if(!(n=n.firstChild)||3!==n.nodeType)return n&&"IMG"===n.tagName;if(r=n.value,mo.trim(r)||(r=(n=n.parentNode.nextSibling)?n.value:""),!n||mo.trim(n.parentNode.textContent)!=r)return!1;if(o=Ni.guessListType({text:r,symbolFont:i},null,t.originalToken))return n.nextSibling&&"SPAN"===n.nextSibling.tagName&&/^[\u00A0\s]/.test(n.nextSibling.firstChild.value)&&("P"===t.openedTag.tag()||"UL"===o.tag)}return!1},_i=function(){var a,u;return{guessIndentLevel:function(e,t,n,r){var o,i;return r&&/^([0-9]+\.)+[0-9]+\.?$/.test(r.text)?r.text.replace(/([0-9]+|\.$)/g,"").length+1:(o=u||parseInt(Oi(n,t.getAttribute("class"))),i=function(e,t){var n,r=0;for(n=e.parentNode;null!=n&&n!==t.parentNode;)r+=n.offsetLeft,n=n.offsetParent;return r}(e.getNode(),t.getNode()),o?a?i+=a:0===i&&(i+=a=o):o=48,u=o=Math.min(i,o),Math.max(1,Math.floor(i/o))||1)}}},Ai=function(){var t=!1;return{check:function(e){return t&&e.type()===Ao.TEXT_TYPE?(e.text(),!0):e.type()===Ao.START_ELEMENT_TYPE&&"STYLE"===e.tag()?t=!0:e.type()===Ao.END_ELEMENT_TYPE&&"STYLE"===e.tag()&&!(t=!1)}}},Pi=["disc","circle","square"];function ki(a,u){var i,o=[],c=[],s=0,f=function(e,t){var n={},r={};s++,t&&e.type&&(n={"list-style-type":e.type}),e.start&&1<e.start&&(r={start:e.start}),o.push(e),a.emit(Ao.createStartElement(e.tag,r,n,u)),i=e},l=function(){a.emit(Ao.createEndElement(o.pop().tag,u)),s--,i=o[o.length-1]},d=function(){var e=c?c.pop():"P";"P"!==e&&a.emit(Ao.createEndElement(e,u)),a.emit(Ao.createEndElement("LI",u))},m=function(e,t,n){var r={};if(e){var o=e.getStyle("margin-left");void 0!==o&&(r["margin-left"]=o)}else r["list-style-type"]="none";i&&!Ni.eqListType(i,t)&&(l(),n&&(a.emit(Ao.createStartElement("P",{},{},u)),a.emit(Ao.createText("\xa0",u)),a.emit(Ao.createEndElement("P",u))),f(t,!0)),a.emit(Ao.createStartElement("LI",{},r,u)),e&&"P"!==e.tag()?(c.push(e.tag()),e.filterStyles(function(){return null}),a.emit(e)):c.push("P")};return{openList:f,closelist:l,closeAllLists:function(){for(;0<s;)d(),l();a.commit()},closeItem:d,openLI:m,openItem:function(e,t,n,r){if(n){for(s||(s=0);e<s;)d(),l();var o,i;if(i=e,"UL"===(o=n).tag&&Pi[i-1]===o.type&&(o={tag:"UL"}),n=o,s===e)d(),m(t,n,r);else for(1<e&&0<c.length&&"P"!==c[c.length-1]&&(a.emit(Ao.createEndElement(c[c.length-1],u)),c[c.length-1]="P");s<e;)f(n,s===e-1),m(s===e?t:void 0,n)}},getCurrentListType:function(){return i},getCurrentLevel:function(){return s}}}var Mi=function(e,t){mo.log("Unexpected token in list conversion: "+t.toString()),e.rollback()},Ri=function(e,t,n){n.type()===Ao.TEXT_TYPE&&""===mo.trim(n.text())?e.defer(n):t.skippedPara||n.type()!==Ao.START_ELEMENT_TYPE||"P"!==n.tag()||ti(n)?ji(e,t,n):(t.openedTag=n,e.defer(n),t.nextFilter=Fi)},Fi=function(e,t,n){n.type()!==Ao.START_ELEMENT_TYPE||"SPAN"!==n.tag()||0!==t.spanCount.length||!Qo&&Di(n,t)||ti(n)?n.type()===Ao.END_ELEMENT_TYPE?"SPAN"===n.tag()?(e.defer(n),t.spanCount.pop()):"P"===n.tag()?(e.defer(n),t.skippedPara=!0,t.openedTag=null,t.nextFilter=Ri):(t.nextFilter=ji,t.nextFilter(e,t,n)):n.isWhitespace()?e.defer(n):(t.nextFilter=ji,t.nextFilter(e,t,n)):(e.defer(n),t.spanCount.push(n))},ji=function(e,t,n){var r=function(){t.emitter.closeAllLists(),e.emitDeferred(),t.openedTag=null,e.emit(n),t.nextFilter=ji};if(n.type()===Ao.START_ELEMENT_TYPE&&ti(n)&&"LI"!==n.tag()){var o=/ level([0-9]+)/.exec(n.getStyle("mso-list"));o&&o[1]?(t.itemLevel=parseInt(o[1],10)+t.styleLevelAdjust,t.nextFilter===ji?e.emitDeferred():e.dropDeferred(),t.nextFilter=Bi,e.startTransaction(),t.originalToken=n,t.commentMode=!1):r()}else!Qo&&(n.type()===Ao.COMMENT_TYPE&&"[if !supportLists]"===n.text()||Di(n,e))?(n.type()===Ao.START_ELEMENT_TYPE&&"SPAN"===n.tag()&&t.spanCount.push(n),t.nextFilter=Bi,e.startTransaction(),t.originalToken=t.openedTag,t.commentMode=!0,t.openedTag=null,e.dropDeferred()):n.type()===Ao.END_ELEMENT_TYPE&&ei(n)?(e.defer(n),t.spanCount.pop()):n.type()===Ao.START_ELEMENT_TYPE?ei(n)?(e.defer(n),t.spanCount.push(n)):(t.openedTag&&(t.emitter.closeAllLists(),e.emitDeferred()),t.openedTag=n,e.defer(n)):r()},Ui=function(e,t,n){n.type()===Ao.END_ELEMENT_TYPE&&t.originalToken.tag()===n.tag()&&(t.nextFilter=Ri,t.styleLevelAdjust=-1),e.emit(n)},Bi=function(e,t,n){if(n.type()===Ao.START_ELEMENT_TYPE&&"Ignore"===n.getStyle("mso-list")&&(t.nextFilter=Yi),n.type()===Ao.START_ELEMENT_TYPE&&"SPAN"===n.tag())t.spanCount.push(n),(t.commentMode&&""===n.getAttribute("style")||null===n.getAttribute("style"))&&(t.nextFilter=Yi);else if("A"===n.tag())n.type()===Ao.START_ELEMENT_TYPE?t.spanCount.push(n):t.spanCount.pop();else if(n.type()===Ao.TEXT_TYPE)if(t.commentMode)t.nextFilter=Yi,t.nextFilter(e,t,n);else{var r=t.originalToken,o=t.spanCount;t.emitter.closeAllLists(),e.emit(r),mo.each(o,mo.bind(e.emit,e)),e.emit(n),e.commit(),t.originalToken=r,t.nextFilter=Ui}else(t.commentMode||n.type()!==Ao.COMMENT_TYPE)&&Mi(e,n)},Yi=function(e,t,n){n.type()===Ao.TEXT_TYPE?n.isWhitespace()||(t.nextFilter=Hi,t.bulletInfo={text:n.text(),symbolFont:t.symbolFont}):ei(n)?n.type()===Ao.START_ELEMENT_TYPE?t.spanCount.push(n):t.spanCount.pop():n.type()===Ao.START_ELEMENT_TYPE&&"IMG"===n.tag()?(t.nextFilter=Hi,t.bulletInfo={text:"\u2202",symbolFont:!0}):Mi(e,n)},Hi=function(e,t,n){n.type()===Ao.START_ELEMENT_TYPE&&ei(n)?(t.spanCount.push(n),t.nextFilter=Wi):n.type()===Ao.END_ELEMENT_TYPE&&ei(n)?(t.spanCount.pop(),t.nextFilter=qi):n.type()===Ao.END_ELEMENT_TYPE&&"IMG"===n.tag()||Mi(e,n)},Wi=function(e,t,n){n.type()===Ao.END_ELEMENT_TYPE&&(ei(n)&&t.spanCount.pop(),t.nextFilter=qi)},qi=function(o,i,a){var e=function(e){var t,n,r;if(i.nextFilter=$i,i.commentMode&&(i.itemLevel=i.indentGuesser.guessIndentLevel(a,i.originalToken,i.styles.styles,i.bulletInfo)),i.listType=Ni.guessListType(i.bulletInfo,(t=i.emitter.getCurrentListType(),n=i.emitter.getCurrentLevel(),r=i.itemLevel,n===r?t:null),i.originalToken),i.listType){for(i.emitter.openItem(i.itemLevel,i.originalToken,i.listType,i.skippedPara),o.emitDeferred();0<i.spanCount.length;)o.emit(i.spanCount.shift());e&&o.emit(a)}else mo.log("Unknown list type: "+i.bulletInfo.text+" Symbol font? "+i.bulletInfo.symbolFont),o.rollback()};a.type()===Ao.TEXT_TYPE||a.type()===Ao.START_ELEMENT_TYPE?e(!0):a.type()===Ao.COMMENT_TYPE?e("[endif]"!==a.text()):a.type()===Ao.END_ELEMENT_TYPE?ei(a)&&i.spanCount.pop():Mi(o,a)},$i=function(e,t,n){n.type()===Ao.END_ELEMENT_TYPE&&n.tag()===t.originalToken.tag()?(t.nextFilter=Ri,t.skippedPara=!1):e.emit(n)},Vi={initial:ji},Xi={},Gi=function(e){Xi.nextFilter=Vi.initial,Xi.itemLevel=0,Xi.originalToken=null,Xi.commentMode=!1,Xi.openedTag=null,Xi.symbolFont=!1,Xi.listType=null,Xi.indentGuesser=_i(),Xi.emitter=ki(e,e.document),Xi.styles=Ai(),Xi.spanCount=[],Xi.skippedPara=!1,Xi.styleLevelAdjust=0,Xi.bulletInfo=void 0};Gi({});var zi=[bi,Ro(function(e,t){Xi.styles.check(t)||(Xi.symbolFont=Ni.checkFont(t,Xi.symbolFont),Xi.nextFilter(e,Xi,t))},function(e){Gi(e)}),xi,Ti,yi,hi],Ki=function(e,t,n,r){for(var o=Po(n),i=ko(e,n),a=function(e,t,n,r){var o,i=t;for(o=e.length-1;0<=o;o--)i=e[o](i,n,r);return i}(r,o,t,n);i.hasNext();)a.receive(i.next());return o.dom},Zi=function(e){return 0<=e.indexOf("<o:p>")||0<=e.indexOf("p.MsoNormal, li.MsoNormal, div.MsoNormal")||0<=e.indexOf("MsoListParagraphCxSpFirst")||0<=e.indexOf("<w:WordDocument>")},Ji={filter:function(e,t,n){var r=$o.all(e),o=Zi(r);t.setWordContent(o);var i=vi;return o&&(i=zi.concat(vi)),Ki(r,t,n,i)},filterPlainText:function(e,t,n){var r=$o.textOnly(e);return Ki(r,t,n,[Yo])},isWordContent:Zi};function Qi(l,d){return{showDialog:function(o){var e,t,n,r,i,a,u=l.settings.powerpaste_word_import||ho.officeStyles,c=l.settings.powerpaste_html_import||ho.htmlStyles,s=Ji.isWordContent(o)?u:c,f=function(e){var t={content:o};l.fire("PastePreProcess",{content:t,internal:!1});var n=To.create(e,e,!0),r=Ji.filter(t.content,n,l.getDoc());l.fire("PastePostProcess",{node:r,internal:!1}),l.undoManager.transact(function(){vo.insert(r,l)})};"clean"===(a=s)||"merge"===a?f(s):(t=d("cement.dialog.paste.clean"),n=d("cement.dialog.paste.merge"),r=[{text:t,ariaLabel:t,onclick:function(){e.close(),f("clean")}},{text:n,ariaLabel:n,onclick:function(){e.close(),f("merge")}}],i={title:d("cement.dialog.paste.title"),spacing:10,padding:10,items:[{type:"container",html:d("cement.dialog.paste.instructions")}],buttons:r},e=l.windowManager.open(i),setTimeout(function(){e&&e.getEl().focus()},1))}}}var ea=function(e){return _e.blobToDataUri(e)},ta=function(e){return _e.dataUriToBlobSync(e)},na=function(e){return N.from(_e.uriToBlob(e))},ra=0,oa=function(e){var t=(new Date).getTime();return e+"_"+Math.floor(1e9*Math.random())+ ++ra+String(t)},ia=sn.detect(),aa=function(e){var t=Hn(e);return ua(e,t)},ua=function(i,a){return z.nu(function(o){ea(i).then(function(e){var t=Re.fromBlobAndUrlSync(i,e),n=oa("image"),r=le.blob(n,t,a);o(r)})})},ca=function(e){return 1===e.length&&A(e,"Files")},sa=function(e){return!A(e,"text/_moz_htmlcontext")},fa=function(e){return A(e,"Files")},la=function(e){return!0},da={multiple:function(e){return 0===e.length?z.pure([]):J(e,aa)},toFiles:function(e){return e.raw().target.files||e.raw().dataTransfer.files},isFiles:ia.browser.isChrome()||ia.browser.isSafari()||ia.browser.isOpera()?fa:ia.browser.isFirefox()?sa:ia.browser.isIE()?ca:la,fromImages:function(e){var t=k(e,function(e){var t=oa("image");return le.url(t,dt(e,"src"),e)});return z.pure(t)},single:aa,singleWithUrl:ua},ma={multiple:function(e){return da.multiple(e)},single:function(e){return da.single(e)},singleWithUrl:function(e,t){return da.singleWithUrl(e,t)}};function pa(u,e,t,r){var c,s=/^image\/(jpe?g|png|gif|bmp)$/i;u.on("dragstart dragend",function(e){c="dragstart"===e.type}),u.on("dragover dragend dragleave",function(e){c||e.preventDefault()});var f=function(e,t){return t in e&&0<e[t].length},l=function(e){var t=e["text/plain"];return!!t&&0===t.indexOf("file://")},d=function(e){ma.multiple(e).get(function(e){var t=k(e,function(e){var t=It.fromTag("img"),n=le.cata(e,r.getLocalURL,function(e,t,n){return t});return ft(t,"src",n),t.dom().outerHTML}).join("");u.insertContent(t,{merge:!1!==u.settings.paste_merge_formats}),r.uploadImages(e)})};u.on("drop",function(e){if(!c){if(tinymce.dom.RangeUtils&&tinymce.dom.RangeUtils.getCaretRangeFromPoint){var t=tinymce.dom.RangeUtils.getCaretRangeFromPoint(e.clientX,e.clientY,u.getDoc());t&&u.selection.setRng(t)}var n=(a=(i=e).target.files||i.dataTransfer.files,R(a,function(e){return s.test(e.type)}));if(0<n.length)return d(n),void e.preventDefault();var r=function(e){var t={};if(e){if(e.getData){var n=e.getData("Text");n&&0<n.length&&(t["text/plain"]=n)}if(e.types)for(var r=0;r<e.types.length;r++){var o=e.types[r];t[o]=e.getData(o)}}return t}(e.dataTransfer);l(o=r)||!f(o,"text/html")&&!f(o,"text/plain")||(Qi(u,We.translate).showDialog(r["text/html"]||r["text/plain"]),e.preventDefault())}var o,i,a})}var ga=["officeStyles","htmlStyles","isWord","isGoogleDocs","proxyBin","isInternal","backgroundAssets"],va=Ct([],ga),ha={nu:va,merge:function(e,n){var r={};return M(ga,function(t){n[t]().or(e[t]()).each(function(e){r[t]=e})}),va(r)}},ya=ie([{error:["message"]},{paste:["elements","correlated"]},{cancel:[]},{incomplete:["elements","correlated","message"]}]),ba=function(e,t,n,r,o){return e.fold(t,n,r,o)},xa={error:ya.error,paste:ya.paste,cancel:ya.cancel,incomplete:ya.incomplete,cata:ba,carry:function(e,r){return ba(e,N.none,N.none,N.none,function(e,t,n){return ba(r,N.none,function(e,t){return N.some(ya.incomplete(e,t,n))},N.none,N.none)}).getOr(r)}},Ta=Ct(["response","bundle"],[]),Ea=function(t){return wa(function(e){e(Ta(t))})},wa=function(t){var e=function(e){t(e)},o=wa;return{get:e,map:function(r){return o(function(n){e(function(e){var t=r(e);n(t)})})},bind:function(n){return o(function(t){e(function(e){n(e).get(t)})})}}},Ia={call:function(e,t){e(Ta(t))},sync:wa,pure:Ea,pass:function(e){return Ea({response:e.response(),bundle:e.bundle()})},done:Ta,error:function(e){return Ea({response:xa.error(e),bundle:ha.nu({})})},initial:function(){return Ea({response:xa.paste([],[]),bundle:ha.nu({})})},cancel:function(){return Ea({response:xa.cancel(),bundle:ha.nu({})})}},Sa=function(e,t){for(var n=0;n<e.length;n++){var r=t(e[n],n);if(r.isSome())return r}return N.none()},La=St("steps","input","label","capture"),Na={choose:function(e,t,n){var r;return(r=n,Sa(e,function(t){return t.getAvailable(r).map(function(e){return La(t.steps(),e,t.label(),t.capture())})})).getOrThunk(function(){var e=t.getAvailable(n);return La(t.steps(),e,t.label(),t.capture())})},run:function(e,a){return F(e,function(e,i){return e.bind(function(e){var r,t,n,o;return r=e,t=function(){return i(a,e)},n=L(Ia.pass,r),o=function(){return t().map(function(e){var t=ha.merge(r.bundle(),e.bundle()),n=xa.carry(r.response(),e.response());return Ia.done({response:n,bundle:t})})},xa.cata(r.response(),n,o,n,o)})},Ia.initial())}},Ca=function(e){e.dom().textContent="",M(In(e),function(e){Oa(e)})},Oa=function(e){var t=e.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},Da=function(e){var t,n=In(e);0<n.length&&(t=e,M(n,function(e){Cn(t,e)})),Oa(e)},_a=function(e){return t=e,n=!1,It.fromDom(t.dom().cloneNode(n));var t,n},Aa=function(e){return void 0!==e.style},Pa=function(e,t,n){if(!E(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);Aa(e)&&e.style.setProperty(t,n)},ka=function(e,t,n){var r=e.dom();Pa(r,t,n)},Ma=function(e,t){var n=e.dom();ee(t,function(e,t){Pa(n,t,e)})},Ra=function(e,t){var n=e.dom(),r=window.getComputedStyle(n).getPropertyValue(t),o=""!==r||Mn(e)?r:Fa(n,t);return null===o?void 0:o},Fa=function(e,t){return Aa(e)?e.style.getPropertyValue(t):""},ja=function(e,t){var n=e.dom(),r=Fa(n,t);return N.from(r).filter(function(e){return 0<e.length})},Ua=function(e,t){var n,r,o=e.dom();r=t,Aa(n=o)&&n.style.removeProperty(r),mt(e,"style")&&""===tn(dt(e,"style"))&&pt(e,"style")},Ba=function(e){return at(e)?(t="v:shape",n=e.dom().nodeValue,r=It.fromTag("div"),o=n.indexOf("]>"),r.dom().innerHTML=n.substr(o+"]>".length),ar(r,function(e){return rt(e)===t})):N.none();var t,n,r,o},Ya=function(e){return Fn(e,".rtf-data-image")},Ha={local:function(e){if("img"===rt(e)){var t=dt(e,"src");if(null!=t&&Qt(t,"file://")){var n=_a(e),r=t.split(/[\/\\]/),o=r[r.length-1];return ft(n,"data-image-id",o),pt(n,"src"),ft(n,"data-image-type","local"),xt(n,"rtf-data-image"),N.some(n)}return N.none()}return N.none()},vshape:function(e){return Ba(e).map(function(e){var t=dt(e,"o:spid"),n=void 0===t?dt(e,"id"):t,r=It.fromTag("img");return xt(r,"rtf-data-image"),ft(r,"data-image-id",n.substr("_x0000_".length)),ft(r,"data-image-type","code"),Ma(r,{width:Ra(e,"width"),height:Ra(e,"height")}),r})},find:Ya,exists:function(e){return 0<Ya(e).length},scour:Ba},Wa=function(){return/^(mso-.*|tab-stops|tab-interval|language|text-underline|text-effect|text-line-through|font-color|horiz-align|list-image-[0-9]+|separator-image|table-border-color-(dark|light)|vert-align|vnd\..*)$/},qa=function(){return/^(font|em|strong|samp|acronym|cite|code|dfn|kbd|tt|b|i|u|s|sub|sup|ins|del|var|span)$/},$a=ie([{starts:["value","f"]},{pattern:["regex","f"]},{contains:["value","f"]},{exact:["value","f"]},{all:[]},{not:["stringMatch"]}]),Va=function(e,n){return e.fold(function(e,t){return 0===t(n).indexOf(t(e))},function(e,t){return e.test(t(n))},function(e,t){return 0<=t(n).indexOf(t(e))},function(e,t){return t(n)===t(e)},function(){return!0},function(e){return!Va(e,n)})},Xa={starts:$a.starts,pattern:$a.pattern,contains:$a.contains,exact:$a.exact,all:$a.all,not:$a.not,cata:function(e,t,n,r,o,i,a){return e.fold(t,n,r,o,i,a)},matches:Va,caseSensitive:function(e){return e},caseInsensitive:function(e){return e.toLowerCase()}},Ga=function(e,t,n,r){var o=r.name,i=void 0!==r.condition?r.condition:v(!0),a=void 0!==r.value?r.value:Xa.all();return Xa.matches(o,n)&&Xa.matches(a,t)&&i(e)},za=function(e,t){var n=rt(e),r=t.name,o=void 0!==t.condition?t.condition:v(!0);return Xa.matches(r,n)&&o(e)},Ka=function(e,t){var n={};return M(e.dom().attributes,function(e){t(e.value,e.name)||(n[e.name]=e.value)}),n},Za=function(e,t,n){var r,o,i=k(e.dom().attributes,function(e){return e.name});oe(t)!==i.length&&(r=e,o=t,M(i,function(e){pt(r,e)}),ee(o,function(e,t){ft(r,t,e)}))},Ja=(v({}),function(t){var e=Q(t);return k(e,function(e){return e+": "+t[e]}).join("; ")}),Qa=function(r,o){var e=r.dom().style,i={};return M(null==e?[]:e,function(e){var t,n=(t=e,r.dom().style.getPropertyValue(t));o(n,e)||(i[e]=n)}),i},eu=function(n,e,t){ft(n,"style","");var r=oe(e),o=oe(t);if(0===r&&0===o)pt(n,"style");else if(0===r)ft(n,"style",Ja(t));else{ee(e,function(e,t){ka(n,t,e)});var i=dt(n,"style"),a=0<o?Ja(t)+"; ":"";ft(n,"style",a+i)}},tu=function(e,t,n){var r,o,i,a=e.dom().getAttribute("style"),u=(o={},i=null!=(r=a)?r.split(";"):[],M(i,function(e){var t=e.split(":");2===t.length&&(o[tn(t[0])]=tn(t[1]))}),o),c={};return M(t,function(e){var t=u[e];void 0===t||n(t,e)||(c[e]=t)}),c},nu=["mso-list"],ru=function(e,t){var n=tu(e,nu,t),r=Qa(e,t);eu(e,r,n)},ou=function(e,t){var n=Ka(e,t);Za(e,n,{})},iu=ru,au=ou,uu=function(e,t){ru(It.fromDom(e),t)},cu=function(e,r,o){e(o,function(t,n){return P(r,function(e){return Ga(o,t,n,e)})})},su=function(e,t){var r=se({styles:[],attributes:[],classes:[],tags:[]},t),n=Fn(e,"*");M(n,function(n){cu(iu,r.styles,n),cu(au,r.attributes,n),M(r.classes,function(t){var e=mt(n,"class")?nr(n):[];M(e,function(e){Xa.matches(t.name,e)&&Tt(n,e)})})});var o=Fn(e,"*");M(o,function(e){P(r.tags,L(za,e))&&Oa(e)})},fu=function(e,t){var n=se({tags:[]},t),r=Fn(e,"*");M(r,function(e){P(n.tags,L(za,e))&&Da(e)})},lu=function(e,t){var n=se({tags:[]},t),r=Fn(e,"*");M(r,function(t){j(n.tags,L(za,t)).each(function(e){e.mutate(t)})})},du="startElement",mu="endElement",pu="comment",gu=function(e,t,n){var r,o,i,a=It.fromDom(e);switch(e.nodeType){case 1:t?r=mu:(r=du,Ma(a,n||{})),o="HTML"!==e.scopeName&&e.scopeName&&e.tagName&&e.tagName.indexOf(":")<=0?(e.scopeName+":"+e.tagName).toUpperCase():e.tagName;break;case 3:r="text",i=e.nodeValue;break;case 8:r=pu,i=e.nodeValue;break;default:console.log("WARNING: Unsupported node type encountered: "+e.nodeType)}return{getNode:function(){return e},tag:function(){return o},type:function(){return r},text:function(){return i}}},vu=function(e,t){return gu(t.createElement(e),!0)},hu=vu("HTML",window.document),yu={START_ELEMENT_TYPE:du,END_ELEMENT_TYPE:mu,TEXT_TYPE:"text",COMMENT_TYPE:pu,FINISHED:hu,token:gu,createStartElement:function(e,t,n,r){var o=r.createElement(e);return ee(t,function(e,t){o.setAttribute(t,e)}),gu(o,!1,n)},createEndElement:vu,createComment:function(e,t){return gu(t.createComment(e),!1)},createText:function(e,t){return gu(t.createTextNode(e))}},bu=function(i){var a=i.createDocumentFragment(),u=a,c=function(e){a.appendChild(e)};return{dom:u,receive:function(e){var t,n,r,o;switch(e.type()){case yu.START_ELEMENT_TYPE:o=e.getNode().cloneNode(!1),c(r=o),a=r;break;case yu.TEXT_TYPE:t=e,n=i.createTextNode(t.text()),c(n);break;case yu.END_ELEMENT_TYPE:null===(a=a.parentNode)&&(a=u);break;case yu.COMMENT_TYPE:break;default:throw{message:"Unsupported token type: "+e.type()}}},label:"SERIALISER"}},xu=function(e,o){var i;o=o||window.document,i=o.createElement("div"),o.body.appendChild(i),i.style.position="absolute",i.style.left="-10000px",i.innerHTML=e;var a=i.firstChild||yu.FINISHED,u=[],c=!1;return{hasNext:function(){return void 0!==a},next:function(){var e,t,n=a,r=c;return!c&&a.firstChild?(u.push(a),a=a.firstChild):c||1!==a.nodeType?a.nextSibling?(a=a.nextSibling,c=!1):(a=u.pop(),c=!0):c=!0,n===yu.FINISHED||a||(o.body.removeChild(i),a=yu.FINISHED),t=r,(e=n)===yu.FINISHED?e:e?yu.token(e,t):void 0}}},Tu=function(e,t,n){var r,o=n;for(r=t.length-1;0<=r;r--)o=t[r](o,{},e);return o},Eu=function(e,t,n){for(var r=bu(e),o=xu(t,e),i=Tu(e,n,r);o.hasNext();){var a=o.next();i.receive(a)}return r.dom},wu=function(e){return e.dom().innerHTML},Iu=function(e,t){var n=yn(e).dom(),r=It.fromDom(n.createDocumentFragment()),o=Nn(t,n);kn(r,o),Ca(e),_n(e,r)},Su=function(t){return function(e){su(e,t)}},Lu=function(t){return function(e){fu(e,t)}},Nu=function(t){return function(e){lu(e,t)}},Cu=function(o){return function(e){var t=wu(e),n=yn(e),r=Eu(n.dom(),t,o);Ca(e),e.dom().appendChild(r)}},Ou=function(e,t){return 0<=e.indexOf("<o:p>")||t.browser.isEdge()&&0<=e.indexOf('v:shapes="')||t.browser.isEdge()&&0<=e.indexOf("mso-")||0<=e.indexOf("mso-list")||0<=e.indexOf("p.MsoNormal, li.MsoNormal, div.MsoNormal")||0<=e.indexOf("MsoListParagraphCxSpFirst")||0<=e.indexOf("<w:WordDocument>")},Du=function(e,t,n){var r=It.fromTag("div",e.dom());return r.dom().innerHTML=t,M(n,function(e){e(r)}),wu(r)};function _u(a,u,e){return function(t,e,n){var r=function(e){t.receive(e)},o=function(e,t,n){return n=void 0!==n?n:e.type()===yu.END_ELEMENT_TYPE,yu.token(t,n,{})},i={emit:r,emitTokens:function(e){M(e,r)},receive:function(e){a(i,e,o)},document:window.document};return u(i),i}}var Au=function(e,t){var n=It.fromDom(e.getNode());return dt(n,t)},Pu=function(e,t){var n=It.fromDom(e.getNode());return Ra(n,t)},ku=function(e){return e.type()===yu.TEXT_TYPE&&/^[\s\u00A0]*$/.test(e.text())},Mu=function(e,t,n){return e===t||e&&t&&e.tag===t.tag&&e.type===t.type&&(n||e.variant===t.variant)},Ru={guessFrom:function(t,n){return j(t,function(e){return"UL"===e.tag||n&&Mu(e,n,!0)}).orThunk(function(){return 0===(e=t).length?N.none():N.some(e[0]);var e})},eqListType:Mu},Fu=function(e,t){if(void 0===e||void 0===t)throw console.trace(),"brick";e.nextFilter.set(t)},ju=function(e,t,n){t.nextFilter.get()(e,t,n)},Uu=Fu,Bu=ju,Yu=St("level","token","type"),Hu=function(e,n,t,r){var o=t.getCurrentListType(),i=t.getCurrentLevel()==r.level()?o:null;return Ru.guessFrom(r.emblems(),i).filter(function(e){return!("OL"===e.tag&&(!A(["P"],(t=n).tag())||/^MsoHeading/.test(Au(t,"class"))));var t})},Wu=function(e,t){return mt(It.fromDom(t.getNode()),"data-list-level")},qu=function(d){return function(e,t,n){var r,o,i,a,u=(r=It.fromDom(n.getNode()),o=parseInt(dt(r,"data-list-level"),10),i=dt(r,"data-list-emblems"),a=JSON.parse(i),pt(r,"data-list-level"),pt(r,"data-list-emblems"),{level:v(o),emblems:v(a)});u.level(),t.originalToken.set(n);var c,s,f,l=(c=n,s=u,Hu((f=t).listType.get(),c,f.emitter,s).each(f.listType.set),Yu(s.level(),f.originalToken.get(),f.listType.get()));t.emitter.openItem(l.level(),l.token(),l.type()),Uu(t,d.inside())}};function $u(e,t,n){return{pred:e,action:t,label:v(n)}}var Vu=function(e,r){return function(e,t,n){return r(e,t,n)}};function Xu(e,r,t){var o=Vu(0,t),n=function(e,t,n){j(r,function(e){return e.pred(t,n)}).fold(v(o),function(e){var t=e.label();return void 0===t?e.action:Vu(0,e.action)})(e,t,n)};return n.toString=function(){return"Handlers for "+e},n}var Gu,zu,Ku,Zu,Ju,Qu=function(r){return Xu("Inside.List.Item",[$u(function(e,t){return t.type()===yu.END_ELEMENT_TYPE&&e.originalToken.get()&&t.tag()===e.originalToken.get().tag()},function(e,t,n){Uu(t,r.outside())},"Closing open tag")],function(e,t,n){e.emit(n)})},ec=function(r){return Xu("Outside.List.Item",[$u(Wu,qu(r),"Data List ****"),$u(function(e,t){return t.type()===yu.TEXT_TYPE&&ku(t)},function(e,t,n){e.emit(n)},"Whitespace")],function(e,t,n){t.emitter.closeAllLists(),e.emit(n),Uu(t,r.outside())})},tc=St("state","result"),nc=St("state","value"),rc={state:St("level","type","types","items"),value:nc,result:tc},oc=function(e,t){var n=e.items().slice(0),r=void 0!==t&&"P"!==t?N.some(t):N.none();r.fold(function(){n.push("P")},function(e){n.push(e)});var o=rc.state(e.level(),e.type(),e.types(),n);return rc.value(o,r)},ic=function(e){var t=e.items().slice(0);if(0<t.length&&"P"!==t[t.length-1]){var n=t[t.length-1];t[t.length-1]="P";var r=rc.state(e.level(),e.type(),e.types(),t);return rc.value(r,N.some(n))}return rc.value(e,N.none())},ac=function(e,t,n){for(var r=[],o=e;t(o);){var i=n(o);o=i.state(),r=r.concat(i.result())}return rc.result(o,r)},uc=function(e,t,n){return ac(e,function(e){return e.level()<t},n)},cc=function(e,t,n){return ac(e,function(e){return e.level()>t},n)},sc=function(e){var t;return e?void 0!==(t=Pu(e,"margin-left"))&&"0px"!==t?{"margin-left":t}:{}:{"list-style-type":"none"}},fc=function(e,t,n){var r=t.start&&1<t.start?{start:t.start}:{},o=e.level()+1,i=t,a=e.types().concat([t]),u=[L(yu.createStartElement,t.tag,r,n)],c=rc.state(o,i,a,e.items());return rc.result(c,u)},lc=function(e){var t=e.types().slice(0),n=[L(yu.createEndElement,t.pop().tag)],r=e.level()-1,o=t[t.length-1],i=rc.state(r,o,t,e.items());return rc.result(i,n)},dc=fc,mc=function(e,t,n){var r,o,i,a=sc(t),u=e.type()&&!Ru.eqListType(e.type(),n)?(r=n,o=lc(e),i=fc(o.state(),r,r.type?{"list-style-type":r.type}:{}),rc.result(i.state(),o.result().concat(i.result()))):rc.result(e,[]),c=[L(yu.createStartElement,"LI",{},a)],s=oc(u.state(),t&&t.tag()),f=s.value().map(function(e){return uu(t.getNode(),v(!0)),[v(t)]}).getOr([]);return rc.result(s.state(),u.result().concat(c).concat(f))},pc=lc,gc=function(e){var t=L(yu.createEndElement,"LI"),n=ic(e),r=n.value().fold(function(){return[t]},function(e){return[L(yu.createEndElement,e),t]});return rc.result(n.state(),r)},vc=function(e){if(0===e.length)throw"Compose must have at least one element in the list";var t=e[e.length-1],n=W(e,function(e){return e.result()});return rc.result(t.state(),n)},hc=function(e){var t=gc(e),n=pc(t.state());return vc([t,n])},yc=function(e,c,s,f){return uc(e,s,function(e){return n=c,r=s,o=f,i=(t=e).level()===r-1&&n.type?{"list-style-type":n.type}:{},a=dc(t,n,i),u=mc(a.state(),a.state().level()==r?o:void 0,n),vc([a,u]);var t,n,r,o,i,a,u})},bc=function(e,t){return cc(e,t,hc)},xc={openItem:function(e,t,n,r){var o,i,a,u,c,s,f,l,d,m,p,g,v=e.level()>t?bc(e,t):rc.result(e,[]),h=v.state().level()===t?(l=v.state(),d=r,m=n,p=0<l.level()?gc(l):rc.result(l,[]),g=mc(p.state(),m,d),vc([p,g])):(o=v.state(),i=r,u=n,c=1<(a=t)?ic(o):rc.value(o,N.none()),s=c.value().map(function(e){return[L(yu.createEndElement,e)]}).getOr([]),c.state().level(),f=yc(c.state(),i,a,u),rc.result(f.state(),s.concat(f.result())));return vc([v,h])},closeAllLists:bc},Tc=["disc","circle","square"],Ec=function(e,t){return"UL"===e.tag&&Tc[t-1]===e.type&&(e={tag:"UL"}),e},wc=function(e){var t=e,n=function(){return t};return{get:n,set:function(e){t=e},clone:function(){return wc(n())}}},Ic={getCurrentListType:function(){return Sc().getCurrentListType()},getCurrentLevel:function(){return Sc().getCurrentLevel()},closeAllLists:function(){return Sc().closeAllLists.apply(void 0,arguments)},openItem:function(){return Sc().openItem.apply(void 0,arguments)}},Sc=function(){return{getCurrentListType:v({}),getCurrentLevel:v(1),closeAllLists:l,openItem:l}},Lc={inside:function(){return Cc},outside:function(){return Oc}},Nc=(Gu=!1,{check:function(e){return Gu&&e.type()===yu.TEXT_TYPE?(e.text(),!0):e.type()===yu.START_ELEMENT_TYPE&&"STYLE"===e.tag()?Gu=!0:e.type()===yu.END_ELEMENT_TYPE&&"STYLE"===e.tag()&&!(Gu=!1)}}),Cc=Qu(Lc),Oc=ec(Lc),Dc=(Ku=wc(zu=Oc),Zu=wc(null),Ju=wc(null),{reset:function(e){Ku.set(zu),Zu.set(null),Ju.set(null);var n,r,i,a,t=(r=(n=e).document,i=rc.state(0,void 0,[],[]),a=function(e){M(e.result(),function(e){var t=e(r);n.emit(t)})},{closeAllLists:function(){var e=xc.closeAllLists(i,0);i=e.state(),a(e)},openItem:function(e,t,n){if(n){var r=Ec(n,e),o=xc.openItem(i,e,t,r);i=o.state(),a(o)}},getCurrentListType:function(){return i.type()},getCurrentLevel:function(){return i.level()}});Sc=v(t)},nextFilter:Ku,originalToken:Zu,listType:Ju,emitter:Ic}),_c=_u(function(e,t,n){Nc.check(t)||Bu(e,Dc,t)},Dc.reset),Ac=[{regex:/^\(?[dc][\.\)]$/,type:{tag:"OL",type:"lower-alpha"}},{regex:/^\(?[DC][\.\)]$/,type:{tag:"OL",type:"upper-alpha"}},{regex:/^\(?M*(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})[\.\)]$/,type:{tag:"OL",type:"upper-roman"}},{regex:/^\(?m*(cm|cd|d?c{0,3})(xc|xl|l?x{0,3})(ix|iv|v?i{0,3})[\.\)]$/,type:{tag:"OL",type:"lower-roman"}},{regex:/^\(?[0-9]+[\.\)]$/,type:{tag:"OL"}},{regex:/^([0-9]+\.)*[0-9]+\.?$/,type:{tag:"OL",variant:"outline"}},{regex:/^\(?[a-z]+[\.\)]$/,type:{tag:"OL",type:"lower-alpha"}},{regex:/^\(?[A-Z]+[\.\)]$/,type:{tag:"OL",type:"upper-alpha"}}],Pc={"\u2022":{tag:"UL",type:"disc"},"\xb7":{tag:"UL",type:"disc"},"\xa7":{tag:"UL",type:"square"}},kc={o:{tag:"UL",type:"circle"},"-":{tag:"UL",type:"disc"},"\u25cf":{tag:"UL",type:"disc"},"\ufffd":{tag:"UL",type:"circle"}},Mc=function(u,e){var t=kc[u]?[kc[u]]:[],n=e&&Pc[u]?[Pc[u]]:e?[{tag:"UL",variant:u}]:[],r=W(Ac,function(e){return e.regex.test(u)?[se(e.type,(r=u,o=r.split("."),i=function(){if(0===o.length)return r;var e=o[o.length-1];return 0===e.length&&1<o.length?o[o.length-2]:e}(),a=parseInt(i,10),isNaN(a)?{}:{start:a}),{variant:(t=e.type,n=u,void 0!==t.variant?t.variant:"("===n.charAt(0)?"()":")"===n.charAt(n.length-1)?")":".")})]:[];var t,n,r,o,i,a}),o=t.concat(n).concat(r);return k(o,function(e){return void 0!==e.variant?e:se(e,{variant:u})})},Rc=function(e){return e.dom().textContent},Fc=function(e){return tu(e,["mso-list"],v(!1))["mso-list"]},jc=function(e){return ut(e)&&ja(e,"font-family").exists(function(e){return A(["wingdings","symbol"],e.toLowerCase())})},Uc={getMsoList:Fc,extractLevel:function(e){var t=Fc(e),n=/ level([0-9]+)/.exec(t);return n&&n[1]?N.some(parseInt(n[1],10)):N.none()},extractEmblem:function(e,t){var n=Rc(e).trim(),r=Mc(n,t);return 0<r.length?N.some(r):N.none()},extractSymSpan:function(e){return ir(e,jc)},extractMsoIgnore:function(e){return ar(e,function(e){return!!(ut(e)?tu(e,["mso-list"],v(!1)):[])["mso-list"]})},extractCommentSpan:function(e){return ir(e,at).bind(En).filter(function(e){return"span"===rt(e)})},isSymbol:jc,deduceLevel:function(e){return ja(e,"margin-left").bind(function(e){var t=parseInt(e,10);return isNaN(t)?N.none():N.some(Math.max(1,Math.ceil(t/18)))})}},Bc=function(e){for(var t=[];null!==e.nextNode();)t.push(It.fromDom(e.currentNode));return t},Yc=sn.detect().browser,Hc=Yc.isIE()||Yc.isEdge()?function(e){try{return Bc(e)}catch(e){return[]}}:Bc,Wc=v(v(!0)),qc=function(e,t){var n=t.fold(Wc,function(t){return function(e){return t(e.nodeValue)}});n.acceptNode=n;var r=document.createTreeWalker(e.dom(),Te.getOrDie("NodeFilter").SHOW_COMMENT,n,!1);return Hc(r)},$c=function(e,t,n,r){var o;!function(e,t,n){ft(e,"data-list-level",t);var r=JSON.stringify(n);ft(e,"data-list-emblems",r)}(e,t,n),o=qc(e,N.none()),M(o,Oa),M(r,Oa),pt(e,"style"),pt(e,"class")},Vc=function(e){return(r=e,Uc.extractLevel(r).bind(function(n){return Uc.extractSymSpan(r).bind(function(t){return Uc.extractEmblem(t,!0).map(function(e){return{mutate:function(){$c(r,n,e,[t])}}})})})).orThunk(function(){return r=e,Uc.extractLevel(r).bind(function(n){return Uc.extractCommentSpan(r).bind(function(t){return Uc.extractEmblem(t,Uc.isSymbol(t)).map(function(e){return{mutate:function(){$c(r,n,e,[t])}}})})});var r}).orThunk(function(){return r=e,Uc.extractLevel(r).bind(function(n){return Uc.extractCommentSpan(r).bind(function(t){return Uc.extractEmblem(t,Uc.isSymbol(t)).map(function(e){return{mutate:function(){$c(r,n,e,[t])}}})})});var r}).orThunk(function(){return"p"!==rt(r=e)?N.none():Uc.extractLevel(r).bind(function(n){return Uc.extractMsoIgnore(r).bind(function(t){return Uc.extractEmblem(t,!1).map(function(e){return{mutate:function(){$c(r,n,e,[bn(t).getOr(t)])}}})})});var r}).orThunk(function(){return"p"!==rt(r=e)?N.none():Uc.extractMsoIgnore(r).bind(function(e){var n=bn(e).getOr(e),t=Uc.isSymbol(n);return Uc.extractEmblem(e,t).bind(function(t){return Uc.deduceLevel(r).map(function(e){return{mutate:function(){$c(r,e,t,[n])}}})})});var r});var r},Xc={filter:_c,preprocess:Nu({tags:[{name:Xa.pattern(/^(p|h\d+)$/,Xa.caseInsensitive),mutate:function(e){Vc(e).each(function(e){e.mutate()})}}]})},Gc=function(e,t){return ar(e,t).isSome()},zc=function(e){return void 0===e.dom().attributes||null===e.dom().attributes||0===e.dom().attributes.length||1===e.dom().attributes.length&&"style"===e.dom().attributes[0].name},Kc={isNotImage:function(e){return"img"!==rt(e)},hasContent:function(e){return!zc(e)||(n=(t=e).dom().attributes,r=null!=n&&0<n.length,("span"!==rt(t)||r)&&Gc(e,function(e){var t=!zc(e),n=!A(["font","em","strong","samp","acronym","cite","code","dfn","kbd","tt","b","i","u","s","sub","sup","ins","del","var","span"],rt(e));return ct(e)||t||n}));var t,n,r},isList:function(e){return"ol"===rt(e)||"ul"===rt(e)},isLocal:function(e){var t=dt(e,"src");return/^file:/.test(t)},hasNoAttributes:zc,isEmpty:function(e){return 0===wu(e).length}};function Zc(n,r){var t=function(e){return n(e)?N.from(e.dom().nodeValue):N.none()},e=sn.detect().browser,o=e.isIE()&&10===e.version.major?function(e){try{return t(e)}catch(e){return N.none()}}:t;return{get:function(e){if(!n(e))throw new Error("Can only get "+r+" value of a "+r+" node");return o(e).getOr("")},getOption:o,set:function(e,t){if(!n(e))throw new Error("Can only set raw "+r+" value of a "+r+" node");e.dom().nodeValue=t}}}var Jc,Qc,es,ts,ns,rs,os,is=Zc(ct,"text"),as=function(e){return is.get(e)},us=function(e,t){is.set(e,t)},cs=function(e,t){var n=It.fromTag(e);Cn(t,n);var r=t.dom().attributes;M(r,function(e){n.dom().setAttribute(e.name,e.value)});var o=In(t);return kn(n,o),Oa(t),n},ss=function(e){return Tn(e).bind(function(e){return ct(e)&&0===as(e).trim().length?ss(e):"li"===rt(e)?N.some(e):N.none()})},fs={changeTag:cs,addBrTag:function(e){0===wu(e).length&&_n(e,It.fromTag("br"))},properlyNest:function(n){bn(n).each(function(e){var t=rt(e);A(["ol","ul"],t)&&ss(n).fold(function(){var e=It.fromTag("li");ka(e,"list-style-type","none"),An(n,e)},function(e){_n(e,n)})})},fontToSpan:function(e){var o=cs("span",e),i={"font-size":{1:"8pt",2:"10pt",3:"12pt",4:"14pt",5:"18pt",6:"24pt",7:"36pt"}};ee({face:"font-family",size:"font-size",color:"color"},function(e,t){if(mt(o,t)){var n=dt(o,t),r=void 0!==i[e]&&void 0!==i[e][n]?i[e][n]:n;ka(o,e,r),pt(o,t)}})}},ls=function(e,t,n,r){var o=yn(e).dom().createRange();return o.setStart(e.dom(),t),o.setEnd(n.dom(),r),o},ds=Zc(at,"comment"),ms=function(e){return ds.get(e)},ps=Lu({tags:[{name:Xa.pattern(/^([OVWXP]|U[0-9]+|ST[0-9]+):/i,Xa.caseInsensitive)}]}),gs=Su({attributes:[{name:Xa.exact("id",Xa.caseInsensitive),value:Xa.starts("docs-internal-guid",Xa.caseInsensitive)}]}),vs=[Cu([Xc.filter])],hs=Su({attributes:[{name:Xa.pattern(/^v:/,Xa.caseInsensitive)},{name:Xa.exact("href",Xa.caseInsensitive),value:Xa.contains("#_toc",Xa.caseInsensitive)},{name:Xa.exact("href",Xa.caseInsensitive),value:Xa.contains("#_mso",Xa.caseInsensitive)},{name:Xa.pattern(/^xmlns(:|$)/,Xa.caseInsensitive)},{name:Xa.exact("type",Xa.caseInsensitive),condition:Kc.isList}]}),ys=Su({tags:[{name:Xa.exact("script",Xa.caseInsensitive)},{name:Xa.exact("link",Xa.caseInsensitive)},{name:Xa.exact("style",Xa.caseInsensitive),condition:Kc.isEmpty}],attributes:[{name:Xa.starts("on",Xa.caseInsensitive)},{name:Xa.exact('"',Xa.caseInsensitive)},{name:Xa.exact("lang",Xa.caseInsensitive)},{name:Xa.exact("language",Xa.caseInsensitive)}],styles:[{name:Xa.all(),value:Xa.pattern(/OLE_LINK/i,Xa.caseInsensitive)}]}),bs=Su({tags:[{name:Xa.exact("meta",Xa.caseInsensitive)}]}),xs=Su({tags:[{name:Xa.exact("style",Xa.caseInsensitive)}]}),Ts=Su({styles:[{name:Xa.not(Xa.pattern(/width|height|list-style-type/,Xa.caseInsensitive)),condition:function(e){return!Et(e,"ephox-limbo-transform")}},{name:Xa.pattern(/width|height/,Xa.caseInsensitive),condition:Kc.isNotImage}]}),Es=Su({classes:[{name:Xa.not(Xa.exact("rtf-data-image",Xa.caseInsensitive))}]}),ws=Su({styles:[{name:Xa.pattern(Wa(),Xa.caseInsensitive)}]}),Is=Su({classes:[{name:Xa.pattern(/mso/i,Xa.caseInsensitive)}]}),Ss=Lu({tags:[{name:Xa.exact("img",Xa.caseInsensitive),condition:Kc.isLocal},{name:Xa.exact("a",Xa.caseInsensitive),condition:Kc.hasNoAttributes}]}),Ls=Lu({tags:[{name:Xa.exact("a",Xa.caseInsensitive),condition:Kc.hasNoAttributes}]}),Ns=Su({attributes:[{name:Xa.exact("style",Xa.caseInsensitive),value:Xa.exact("",Xa.caseInsensitive),debug:!0}]}),Cs=Su({attributes:[{name:Xa.exact("class",Xa.caseInsensitive),value:Xa.exact("",Xa.caseInsensitive),debug:!0}]}),Os=Lu({tags:[{name:Xa.pattern(qa(),Xa.caseInsensitive),condition:(Jc=Kc.hasContent,function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return!Jc.apply(null,e)})}]}),Ds=Lu({tags:[{name:Xa.exact("p",Xa.caseInsensitive),condition:(Qc="li",function(e){return bn(e).exists(function(e){return rt(e)===Qc&&1===In(e).length})})}]}),_s=Nu({tags:[{name:Xa.exact("p",Xa.caseInsensitive),mutate:fs.addBrTag}]}),As=Nu({tags:[{name:Xa.pattern(/ol|ul/,Xa.caseInsensitive),mutate:fs.properlyNest}]}),Ps=Nu({tags:[{name:Xa.exact("b",Xa.caseInsensitive),mutate:L(fs.changeTag,"strong")},{name:Xa.exact("i",Xa.caseInsensitive),mutate:L(fs.changeTag,"em")},{name:Xa.exact("u",Xa.caseInsensitive),mutate:function(e){var t=fs.changeTag("span",e);xt(t,"ephox-limbo-transform"),ka(t,"text-decoration","underline")}},{name:Xa.exact("s",Xa.caseInsensitive),mutate:L(fs.changeTag,"strike")},{name:Xa.exact("font",Xa.caseInsensitive),mutate:fs.fontToSpan,debug:!0}]}),ks=Su({classes:[{name:Xa.exact("ephox-limbo-transform",Xa.caseInsensitive)}]}),Ms=Su({tags:[{name:Xa.exact("br",Xa.caseInsensitive),condition:rr("Apple-interchange-newline")}]}),Rs={unwrapWordTags:ps,removeWordAttributes:hs,removeGoogleDocsId:gs,parseLists:vs,removeExcess:ys,removeMetaTags:bs,removeStyleTags:xs,cleanStyles:Ts,cleanClasses:Es,cleanupBrowserCruft:Su({styles:[{name:Xa.pattern(/^-/,Xa.caseInsensitive)},{name:Xa.all(),value:Xa.exact("initial",Xa.caseInsensitive)},{name:Xa.exact("background-color",Xa.caseInsensitive),value:Xa.exact("transparent",Xa.caseInsensitive)},{name:Xa.exact("font-style",Xa.caseInsensitive),value:Xa.exact("normal",Xa.caseInsensitive)},{name:Xa.pattern(/font-variant.*/,Xa.caseInsensitive)},{name:Xa.exact("letter-spacing",Xa.caseInsensitive)},{name:Xa.exact("font-weight",Xa.caseInsensitive),value:Xa.pattern(/400|normal/,Xa.caseInsensitive)},{name:Xa.exact("orphans",Xa.caseInsensitive)},{name:Xa.exact("text-decoration",Xa.caseInsensitive),value:Xa.exact("none",Xa.caseInsensitive)},{name:Xa.exact("text-size-adjust",Xa.caseInsensitive)},{name:Xa.exact("text-indent",Xa.caseInsensitive),value:Xa.exact("0px",Xa.caseInsensitive)},{name:Xa.exact("text-transform",Xa.caseInsensitive),value:Xa.exact("none",Xa.caseInsensitive)},{name:Xa.exact("white-space",Xa.caseInsensitive),value:Xa.exact("normal",Xa.caseInsensitive)},{name:Xa.exact("widows",Xa.caseInsensitive)},{name:Xa.exact("word-spacing",Xa.caseInsensitive),value:Xa.exact("0px",Xa.caseInsensitive)},{name:Xa.exact("text-align",Xa.caseInsensitive),value:Xa.pattern(/start|end/,Xa.caseInsensitive)},{name:Xa.exact("font-weight",Xa.caseInsensitive),value:Xa.pattern(/700|bold/,Xa.caseInsensitive),condition:function(e){return/^h\d$/.test(rt(e))}}]}),cleanupBrowserTags:Ms,unwrapConvertedSpace:(ts=(es=function(e,n){return function(t){return e(t).filter(function(e){return ct(t)&&n(Rc(e)," ")}).isSome()}})(Tn,en),ns=es(En,Qt),Nu({tags:[{name:Xa.exact("span",Xa.caseInsensitive),condition:rr("Apple-converted-space"),mutate:function(e){"\xa0"===Rc(e)&&(ts(e)||ns(e)?Da(e):(Cn(e,It.fromText(" ")),Oa(e)))}}]})),mergeStyles:ws,mergeClasses:Is,removeLocalImages:Ss,removeVacantLinks:Ls,removeEmptyStyle:Ns,removeEmptyClass:Cs,pruneInlineTags:Os,unwrapSingleParagraphsInlists:Ds,addPlaceholders:_s,nestedListFixes:As,inlineTagFixes:Ps,cleanupFlags:ks,distillAnchorsFromLocalLinks:(rs=/^file:\/\/\/[^#]+(#[^#]+)$/,Nu({tags:[{name:Xa.exact("a",Xa.caseInsensitive),condition:function(e){var t=dt(e,"href");return!!t&&rs.test(t)},mutate:function(e){var t=dt(e,"href");ft(e,"href",t.replace(rs,"$1"))}}]})),removeLocalLinks:Su({attributes:[{name:Xa.exact("href",Xa.caseInsensitive),value:Xa.starts("file:///",Xa.caseInsensitive),debug:!0}]}),replaceClipboardChangedUrls:Nu({tags:[(os=function(e,n,r){return{name:Xa.exact(e,Xa.caseInsensitive),condition:function(e){return mt(e,n)},mutate:function(e){var t=dt(e,n);ft(e,r,t),pt(e,n)}}})("a","data-ephox-href","href"),os("img","data-ephox-src","src")]}),removeFragmentComments:function(a){var u=["table","thead","tbody","tfoot","th","tr","td","ul","ol","li"],e=Rn(a,at),t=j(e,function(e){return Zt(ms(e),"StartFragment")}),n=j(e,function(e){return Zt(ms(e),"EndFragment")});t.each(function(i){n.each(function(e){for(var t,n=i,r=[],o=(t=ls(i,0,e,0),It.fromDom(t.commonAncestorContainer));void 0!==o&&!vn(o,a);)A(u,rt(o))?n=o:r.push(o),o=bn(o).getOr(void 0);M(r,Da),M(wn(n),Oa)}),Oa(i)}),n.each(Oa)},none:S},Fs=function(e){return e.browser.isIE()&&11<=e.browser.version.major},js=function(i,a){return _u(function(e,t){var r,o,n=(r=t,o=a,i(It.fromDom(r.getNode())).fold(function(){return[r]},function(e){var t=r.type()===yu.END_ELEMENT_TYPE,n=[yu.token(e.dom(),t)];return!t&&o&&n.push(yu.token(e.dom(),!0)),n}));M(n,e.emit)},S)},Us=function(e,t,n){var r,o,i,a,u,c,s,f,l,d,m,p,g,v=(r=e,i=(o=n).browser.isFirefox()||o.browser.isEdge(),a=i?Ha.local:Ha.vshape,u=!i,c=Fs(o)?Rs.none:Cu([js(a,u)]),{annotate:[r?c:Rs.none],local:[i?Rs.none:Rs.removeLocalImages]});return H([(p=e,g=n,Fs(g)||!p?[]:[Xc.preprocess]),v.annotate,[Rs.inlineTagFixes],function(e,t,n){if(!e)return[Rs.none];var r=[Rs.unwrapWordTags],o=Fs(n)?[]:Rs.parseLists;return r.concat(o).concat([Rs.removeWordAttributes])}(e,0,n),[Rs.removeGoogleDocsId],[Rs.nestedListFixes],[Rs.removeExcess],[Rs.removeMetaTags],v.local,(m=t,m?[Rs.mergeStyles,Rs.mergeClasses]:[Rs.cleanStyles,Rs.cleanClasses]),[Rs.distillAnchorsFromLocalLinks,Rs.removeLocalLinks,Rs.removeVacantLinks,Rs.replaceClipboardChangedUrls],[Rs.removeEmptyStyle],[Rs.removeEmptyClass],[Rs.pruneInlineTags],[Rs.cleanupBrowserTags],(l=e,d=t,!l&&d?[Rs.cleanupBrowserCruft]:[]),[Rs.unwrapConvertedSpace],[Rs.addPlaceholders],(s=e,f=n,Fs(f)&&s?[Rs.unwrapSingleParagraphsInlists]:[]),[Rs.cleanupFlags],[Rs.removeStyleTags]])},Bs=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];St("word","pattern");var Ys={point:St("element","offset"),delta:St("element","deltaOffset"),range:St("element","start","finish"),points:St("begin","end"),text:St("element","text")},Hs=v(!1),Ws=v(!0),qs=function(e){return{isBoundary:function(){return e(Ws,Hs,Hs)},fold:e,toText:function(){return e(N.none,N.none,function(e,t){return N.some(e)})},is:function(n){return e(Hs,Hs,function(e,t){return t.eq(e,n)})},len:function(){return e(v(0),v(1),function(e,t){return t.property().getText(e).length})}}},$s=function(r,o){return qs(function(e,t,n){return n(r,o)})},Vs=function(r,o){return qs(function(e,t,n){return e(r,o)})},Xs=function(r,o){return qs(function(e,t,n){return t(r,o)})},Gs=function(e,t,n,r){return e.fold(t,n,r)},zs=function(e,t){var n=U(e,t).getOr(-1);return e.slice(0,n)},Ks=ie([{include:["item"]},{excludeWith:["item"]},{excludeWithout:["item"]}]),Zs={include:Ks.include,excludeWith:Ks.excludeWith,excludeWithout:Ks.excludeWithout,cata:function(e,t,n,r){return e.fold(t,n,r)}},Js=function(e,n){var r=[],o=[];return M(e,function(e){var t=n(e);Zs.cata(t,function(){o.push(e)},function(){0<o.length&&r.push(o),r.push([e]),o=[]},function(){0<o.length&&r.push(o),o=[]})}),0<o.length&&r.push(o),r},Qs={splitby:function(e,t){return Js(e,function(e){return t(e)?Zs.excludeWithout(e):Zs.include(e)})},splitbyAdv:Js},ef=function(e,t){return Qs.splitbyAdv(e,t)},tf=function(e,t){return zs(e,t)},nf=function(e){return t=function(e,t){return t.len()+e},n=0,function(e,t){for(var n=e.length-1;0<=n;n--)t(e[n],n,e)}(e,function(e){n=t(n,e)}),n;var t,n},rf=function(e,t){return tf(e,function(e){return e.is(t)})},of=function(e){return W(e,function(e){return e.fold(v([]),v([]),function(e){return[e]})})},af=function(t,e,n){if(t.property().isText(e))return[$s(e,t)];if(t.property().isEmptyTag(e))return[Xs(e,t)];if(t.property().isElement(e)){var r=t.property().children(e),o=t.property().isBoundary(e)?[Vs(e,t)]:[],i=void 0!==n&&n(e)?[]:W(r,function(e){return af(t,e,n)});return o.concat(i).concat(o)}return[]},uf=function(e,t,n,r,o){var i=af(e,r,o),a=rf(i,t),u=nf(a);return Ys.point(r,u+n)},cf={typed:af,items:function(e,t,n){var r=af(e,t,n),o=function(e,t){return e};return k(r,function(e){return e.fold(o,o,o)})},extractTo:function(t,n,r,e,o){return t.up().predicate(n,e).fold(function(){return Ys.point(n,r)},function(e){return uf(t,n,r,e,o)})},extract:function(t,n,r,o){return t.property().parent(n).fold(function(){return Ys.point(n,r)},function(e){return uf(t,n,r,e,o)})}},sf=function(e,n,t){return F(e,function(t,e){return n(e,t.len).fold(v(t),function(e){return{len:e.finish(),list:t.list.concat([e])}})},{len:void 0!==t?t:0,list:[]}).list},ff=function(e,t){return t>=e.start()&&t<=e.finish()},lf=function(e,t){return U(e,function(e){return e.start()===t})},df=ff,mf=function(o,e,i){var t=lf(o,e),a=lf(o,i);return t.bind(function(e){var t,n,r=a.getOr((n=i,(t=o)[t.length-1]&&t[t.length-1].finish()===n?t.length+1:-1));return-1<r?N.some(o.slice(e,r)):N.none()}).getOr([])},pf=function(e,t){return k(e,function(e){return se(e,{start:v(e.start()+t),finish:v(e.finish()+t)})})},gf=function(e,o,i){return 0===o.length?e:W(e,function(t){var e,n,r=W(o,function(e){return df(t,e)?[e-t.start()]:[]});return 0<r.length?(n=i(e=t,r),pf(n,e.start())):[t]})},vf=function(e,t,n){return sf(e,t,n)},hf=function(e,t,n){return gf(e,t,n)},yf=function(e,t,n){return mf(e,t,n)},bf=function(e,t,n){return cf.typed(e,t,n)},xf=function(t,e,n){var r=W(e,function(e){return bf(t,e,n)}),o=ef(r,function(e){return Gs(e,function(){return Zs.excludeWithout(e)},function(){return Zs.excludeWith(e)},function(){return Zs.include(e)})});return R(o,function(e){return 0<e.length})},Tf=function(e,t,n){return xf(e,t,n)},Ef=function(r,e){if(0===e.length)return[r];var t=F(e,function(e,t){if(0===t)return e;var n=r.substring(e.prev,t);return{prev:t,values:e.values.concat([n])}},{prev:0,values:[]}),n=e[e.length-1];return n<r.length?t.values.concat(r.substring(n)):t.values},wf=function(e,t){return Ef(e,t)},If=function(o,e,t){var n=o.property().getText(e),r=R(wf(n,t),function(e){return 0<e.length});if(r.length<=1)return[Ys.range(e,0,n.length)];o.property().setText(e,r[0]);var i=vf(r.slice(1),function(e,t){var n=o.create().text(e),r=Ys.range(n,t,t+e.length);return N.some(r)},r[0].length),a=k(i,function(e){return e.element()});return o.insert().afterAll(e,a),[Ys.range(e,0,r[0].length)].concat(i)},Sf=function(o,e,t){var n=W(t,function(e){return[e.start(),e.finish()]}),i=hf(e,n,function(e,t){return If(o,e.element(),t)});return k(t,function(e){var t=yf(i,e.start(),e.finish()),n=k(t,function(e){return e.element()}),r=k(n,o.property().getText).join("");return{elements:v(n),word:e.word,exact:v(r)}})},Lf="\\w'\\-\\u0100-\\u017F\\u00C0-\\u00FF\ufeff\\u2018\\u2019";function Nf(e,t,n,r){return{term:function(){return new RegExp(e,r.getOr("g"))},prefix:t,suffix:n}}v(Lf),v("[^\\w'\\-\\u0100-\\u017F\\u00C0-\\u00FF\ufeff\\u2018\\u2019]"),v("[\\w'\\-\\u0100-\\u017F\\u00C0-\\u00FF\ufeff\\u2018\\u2019]");var Cf=function(e){return Nf(e,v(0),v(0),N.none())},Of=function(e){return Cf(e)},Df=function(e,t){for(var n=t.term(),r=[],o=n.exec(e);o;){var i=o.index+t.prefix(o),a=o[0].length-t.prefix(o)-t.suffix(o);r.push({start:v(i),finish:v(i+a)}),n.lastIndex=i+a,o=n.exec(e)}return r},_f=function(n,e){var t,r,o=W(e,function(t){var e=Df(n,t.pattern());return k(e,function(e){return se(t,{start:e.start,finish:e.finish})})});return t=o,(r=Array.prototype.slice.call(t,0)).sort(function(e,t){return e.start()<t.start()?-1:t.start()<e.start()?1:0}),r},Af=function(e,t){return _f(e,t)},Pf=function(a,e,u,t){var n=Tf(a,e,t);return W(n,function(e){var r,t=of(e),n=k(t,a.property().getText).join(""),o=Af(n,u),i=(r=a,vf(t,function(e,t){var n=t+r.property().getText(e).length;return N.from(Ys.range(e,t,n))}));return Sf(a,i,o)})},kf=Pf,Mf=function(e,t,n,r){return kf(e,t,n,r)},Rf={up:v({selector:cr,closest:fr,predicate:or,all:xn}),down:v({selector:Fn,predicate:Rn}),styles:v({get:Ra,getRaw:ja,set:ka,remove:Ua}),attrs:v({get:dt,set:ft,remove:pt,copyTo:function(e,t){var n=F(e.dom().attributes,function(e,t){return e[t.name]=t.value,e},{});lt(t,n)}}),insert:v({before:Cn,after:On,afterAll:Pn,append:_n,appendAll:kn,prepend:Dn,wrap:An}),remove:v({unwrap:Da,remove:Oa}),create:v({nu:It.fromTag,clone:function(e){return It.fromDom(e.dom().cloneNode(!1))},text:It.fromText}),query:v({comparePosition:function(e,t){return e.dom().compareDocumentPosition(t.dom())},prevSibling:Tn,nextSibling:En}),property:v({children:In,name:rt,parent:bn,isText:ct,isComment:at,isElement:ut,getText:as,setText:us,isBoundary:function(e){return!!ut(e)&&("body"===rt(e)||A(Bs,rt(e)))},isEmptyTag:function(e){return!!ut(e)&&A(["br","img","hr","input"],rt(e))}}),eq:vn,is:hn},Ff=function(e,t,n){return Mf(Rf,e,t,n)},jf=function(e){return ur(e).isSome()},Uf=/(?:(?:[A-Za-z]{3,9}:(?:\/\/))(?:[-.~*+=!&;:'%@?^${}(),\w]+@)?[A-Za-z0-9-]+(?:\.[A-Za-z0-9-]+)*|(?:www\.|[-;:&=+$,.\w]+@)[A-Za-z0-9-]+(?:\.[A-Za-z0-9-]+)*)(?::[0-9]+)?(?:\/[-+~=%.()\/\w]*)?(?:\?(?:[-.~*+=!&;:'%@?^${}(),\/\w]+))?(?:#(?:[-.~*+=!&;:'%@?^${}(),\/\w]+))?/g.source,Bf=function(e){return!fr(e,"a",t).isSome();var t},Yf=function(e){var t=e.indexOf("://");return 3<=t&&t<=9},Hf={links:function(e){var t,n,r=(t=e,n=St("word","pattern")("__INTERNAL__",Of(Uf)),Ff(t,[n]));M(r,function(e){var n,t=e.exact();(t.indexOf("@")<0||Yf(t))&&(n=e.elements(),N.from(n[0]).filter(Bf).map(function(e){var t=It.fromTag("a");return Cn(e,t),kn(t,n),ft(t,"href",Rc(t)),t}))})},position:function(e){M(e,function(e){ut(e)&&ja(e,"position").isSome()&&Ua(e,"position")})},list:function(e){var t=R(e,function(e){return"li"===rt(e)});if(0<t.length){var n=wn(t[0]),r=It.fromTag("ul");if(Cn(e[0],r),0<n.length){var o=It.fromTag("li");_n(r,o),kn(o,n)}kn(r,t)}}},Wf=function(e){var t=In(e);M([Hf.links,Hf.position,Hf.list],function(e){e(t)})},qf=function(e,t,n,r,o){Wf(n);var i=wu(n),a=Us(o,r,t);return Du(e,i,a)},$f=Wf,Vf=function(e,t){var n=wu(t);return Du(e,n,[Rs.removeMetaTags,Rs.replaceClipboardChangedUrls])},Xf=function(e,t){var n=wu(t);return Du(e,n,[Rs.removeFragmentComments])},Gf=oa("simple-adt"),zf=function(e){var i=function(e){return Gf+"_"+e},o=function(e,t){return e._simpleAdt===i(t)},a=Q(e),n={};M(a,function(t){n["as"+Jt(t)]=function(e){return o(e,t)?N.some(e.data):N.none()}});var t={};return ee(e,function(r,o){t[o]=function(e){return{_simpleAdt:i(o),data:(t=r,n=e,0===t.length?{}:Ct(t,[])(n)),_simpleAdt_data:e};var t,n}}),ce(n,{constructors:t,match:function(t,n){var e,r=Q(n);if(r.length!==a.length)throw new Error("Partial match");return Sa(r,function(e){return o(t,e)?N.some(n[e]):N.none()}).getOrDie("Must find branch for constructor: "+(0===(e=t._simpleAdt).indexOf(Gf)?e.substring(Gf.length+"_".length):e))(t.data)}})},Kf=zf({getFromFlash:[],disabled:[],fromClipboard:["rtf"]}),Zf={getFromFlash:Kf.constructors.getFromFlash,disabled:Kf.constructors.disabled,fromClipboard:Kf.constructors.fromClipboard,match:Kf.match},Jf=function(e,t){var n=new RegExp(t,"i");return Sa(e,function(e){return null!==e.match(n)?N.some({type:e,flavor:t}):N.none()})},Qf={isValidData:function(e){return void 0!==e&&void 0!==e.types&&null!==e.types},getPreferredFlavor:function(e,t){return Sa(e,function(e){return Jf(t,e)})},getFlavor:Jf},el=zf({event:["nativeEvent"],html:["container"],word:["html","rtf"],text:["text"],images:["images"]}),tl=sn.detect().browser,nl=!(tl.isIE()||tl.isEdge()&&tl.version.major<16),rl=["^image/","file"],ol=function(e){return Zt(e,"<html")&&(Zt(e,'xmlns:o="urn:schemas-microsoft-com:office:office"')||Zt(e,'xmlns:x="urn:schemas-microsoft-com:office:excel"'))},il=function(e){return Zt(e,"<meta")&&Zt(e,'id="docs-internal-guid')},al=function(e){return 0<e.length},ul=function(t,e){return Qf.getFlavor(t.types,e).map(function(e){return t.getData(e.type)}).filter(al)},cl=function(e){return ul(e,"html")},sl=function(e){return cl(e).filter(il)},fl=function(e){return nl?N.some(e.clipboardData).filter(Qf.isValidData):N.none()},ll=function(e){var t=It.fromTag("div");Iu(t,e);var n=Xf(yn(t),t),r=It.fromTag("div");return Iu(r,n),el.constructors.html({container:r})},dl={fromEvent:function(e){var t=function(t){return void 0===t.items?N.none():Qf.getPreferredFlavor(rl,t.types).map(function(e){return el.constructors.images({images:t.items})})},r=function(t){return Sa(t.types,function(e){return"text/plain"===e?N.some(t.getData(e)).map(function(e){return el.constructors.text({text:e})}):N.none()})};return{getWordData:function(){return fl(e).bind(function(n){return(e=n,cl(e).filter(ol)).map(function(e){var t=ul(n,"rtf");return el.constructors.word({html:e,rtf:t.fold(Zf.getFromFlash,function(e){return Zf.fromClipboard({rtf:e})})})});var e})},getGoogleDocsData:function(){return fl(e).bind(sl).map(ll)},getImage:function(){return fl(e).bind(t)},getText:function(){return fl(e).fold(function(){var e=window.clipboardData;return void 0!==e?N.some(el.constructors.text({text:e.getData("text")})):N.none()},r)},getHtml:function(){return fl(e).bind(cl).map(ll)},getOnlyText:function(){return fl(e).bind(function(e){return t=e.types,n="text/plain",1===t.length&&t[0]===n?r(e):N.none();var t,n})},getNative:function(){return el.constructors.event({nativeEvent:e})}}},fromHtml:function(e){return{getWordData:function(){return N.some(el.constructors.word({html:e,rtf:Zf.disabled()}))},getGoogleDocsData:N.none,getImage:N.none,getHtml:N.none,getText:N.none,getNative:f("There is no native event"),getOnlyText:N.none}},fromText:function(e){return{getWordData:N.none,getGoogleDocsData:N.none,getImage:N.none,getHtml:N.none,getText:function(){return N.some(el.constructors.text({text:e}))},getNative:f("There is no native event"),getOnlyText:N.none}}},ml=function(){var e=!1;return{isBlocked:function(){return e},block:function(){e=!0},unblock:function(){e=!1}}},pl=function(e,t){return{control:e,instance:t}},gl={tap:function(e){var t=ml();return pl(t,function(){t.isBlocked()||e.apply(null,arguments)})}},vl=Xe("ephox-sloth").resolve("bin"),hl={bin:v(vl)},yl=sn.detect(),bl=yl.browser.isIE()&&yl.browser.version.major<=10,xl=bl?function(e,t,n){t.control.block(),e.dom().execCommand("paste"),n(),t.control.unblock()}:function(e,t,n){setTimeout(n,1)},Tl={willBlock:v(bl),run:function(e,t,n){return xl(e,t,n)}},El=ie([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),wl={before:El.before,on:El.on,after:El.after,cata:function(e,t,n,r){return e.fold(t,n,r)},getStart:function(e){return e.fold(l,l,l)}},Il=ie([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Sl=St("start","soffset","finish","foffset"),Ll=Il.relative,Nl=function(e,t,n){var r,o,i=e.document.createRange();return r=i,t.fold(function(e){r.setStartBefore(e.dom())},function(e,t){r.setStart(e.dom(),t)},function(e){r.setStartAfter(e.dom())}),o=i,n.fold(function(e){o.setEndBefore(e.dom())},function(e,t){o.setEnd(e.dom(),t)},function(e){o.setEndAfter(e.dom())}),i},Cl=function(e,t,n,r,o){var i=e.document.createRange();return i.setStart(t.dom(),n),i.setEnd(r.dom(),o),i},Ol=ie([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Dl=function(e,t,n){return t(It.fromDom(n.startContainer),n.startOffset,It.fromDom(n.endContainer),n.endOffset)},_l=function(e,t){var o,n,r,i=(o=e,t.match({domRange:function(e){return{ltr:v(e),rtl:N.none}},relative:function(e,t){return{ltr:Dt(function(){return Nl(o,e,t)}),rtl:Dt(function(){return N.some(Nl(o,t,e))})}},exact:function(e,t,n,r){return{ltr:Dt(function(){return Cl(o,e,t,n,r)}),rtl:Dt(function(){return N.some(Cl(o,n,r,e,t))})}}}));return(r=(n=i).ltr()).collapsed?n.rtl().filter(function(e){return!1===e.collapsed}).map(function(e){return Ol.rtl(It.fromDom(e.endContainer),e.endOffset,It.fromDom(e.startContainer),e.startOffset)}).getOrThunk(function(){return Dl(0,Ol.ltr,r)}):Dl(0,Ol.ltr,r)},Al=(document.caretPositionFromPoint||document.caretRangeFromPoint,function(e,t){var n=rt(e);return"input"===n?wl.after(e):A(["br","img"],n)?0===t?wl.before(e):wl.after(e):wl.on(e,t)}),Pl=function(e,t,n,r,o){var i,a,u=Cl(e,t,n,r,o);i=e,a=u,N.from(i.getSelection()).each(function(e){e.removeAllRanges(),e.addRange(a)})},kl=function(e,t,n,r,o){var i,a,u,c,f,s=(i=r,a=o,u=Al(t,n),c=Al(i,a),Ll(u,c));_l(f=e,s).match({ltr:function(e,t,n,r){Pl(f,e,t,n,r)},rtl:function(t,n,r,o){var e,i,a,u,c,s=f.getSelection();if(s.setBaseAndExtent)s.setBaseAndExtent(t.dom(),n,r.dom(),o);else if(s.extend)try{i=t,a=n,u=r,c=o,(e=s).collapse(i.dom(),a),e.extend(u.dom(),c)}catch(e){Pl(f,r,o,t,n)}else Pl(f,r,o,t,n)}})},Ml=function(e){var t,n,r,o,i,a,u=It.fromDom(e.anchorNode),c=It.fromDom(e.focusNode);return t=u,n=e.anchorOffset,r=c,o=e.focusOffset,i=ls(t,n,r,o),a=vn(t,r)&&n===o,i.collapsed&&!a?N.some(Sl(It.fromDom(e.anchorNode),e.anchorOffset,It.fromDom(e.focusNode),e.focusOffset)):function(e){if(0<e.rangeCount){var t=e.getRangeAt(0),n=e.getRangeAt(e.rangeCount-1);return N.some(Sl(It.fromDom(t.startContainer),t.startOffset,It.fromDom(n.endContainer),n.endOffset))}return N.none()}(e)},Rl=function(e){return N.from(e.getSelection()).filter(function(e){return 0<e.rangeCount}).bind(Ml)},Fl=function(e){return k(e,function(e){return e.asset()})};function jl(i,a){var u=Xn.create({cancel:Vn([]),error:Vn(["message"]),insert:Vn(["elements","assets","correlated","isInternal"])}),r=function(e,t,n){var r=Na.choose(i,a,e);r.capture()&&n();var o=k(r.steps(),function(e){return e(t)});Na.run(o,r.input()).get(function(e){var r=e.bundle().isInternal().getOr(!1);xa.cata(e.response(),function(e){u.trigger.error(e)},function(e,t){u.trigger.insert(e,Fl(t),t,r)},function(){u.trigger.cancel()},function(e,t,n){u.trigger.insert(e,Fl(t),t,r),u.trigger.error(n)})})},o=gl.tap(function(n){Rl(n.target.ownerDocument.defaultView).each(function(e){if(!Et(e.start(),hl.bin())){var t=dl.fromEvent(n);Tl.willBlock()&&(o.control.block(),n.preventDefault()),r(t,o.control,function(){n.preventDefault()})}})});return{paste:o.instance,pasteCustom:function(e){var t=gl.tap(S);r(e,t.control,S)},isBlocked:o.control.isBlocked,destroy:S,events:u.registry}}var Ul=St("asset","image"),Bl=function(e,o){return le.cata(e,function(e,t,n,r){return ft(o,"src",n),!0},d)},Yl={assetImage:Ul,createImages:function(e){var a=[],u=[];return M(e,function(i){return le.cata(i,function(e,t,n,r){var o=It.fromTag("img");ft(o,"src",n),a.push(o),u.push(Ul(i,o))},function(e,t,n){console.error("Internal error: Paste operation produced an image URL instead of a Data URI: ",t)})}),xa.paste(a,u)},findImages:function(e,t){var i=[],a=W(e,function(e){return"img"===rt(e)?[e]:Fn(e,"img")});return M(t,function(o){le.cata(o,function(e,t,n,r){M(a,function(e){dt(e,"src")===n&&i.push(Ul(o,e))})},S)}),i},updateSource:Bl,updateSources:function(e,r){var o=[];return M(e,function(e,t){var n=r[t];Bl(e,n)&&o.push(Ul(e,n))}),o},browserBlobs:function(e){return J(e,function(u){return z.nu(function(i){var a=u.dom();Re.imageToImageResult(a).then(function(o){o.toBlob().then(function(e){var t=Qt(a.src,"blob:")?a.src:URL.createObjectURL(e),n=oa("image"),r=le.blob(n,o,t);i(Ul(r,u))})})})})}},Hl=function(o){var i=function(){return z.pure(o)};return le.cata(o.asset(),function(e,t,n){return/tiff$/.test(t.getType())?(r=t,z.nu(function(t){var e=Re.imageResultToBlob(r,"image/png").then(function(e){ma.single(e).map(N.some).get(t)});return e.catch.call(e,function(e){console.warn(e),t(N.none())})})).map(function(e){return e.map(function(e){var t=o.image();return Wn(n),Yl.updateSource(e,t),Yl.assetImage(e,t)}).getOr(o)}):i();var r},i)};function Wl(){return function(e,o){return Ia.sync(function(n){var e=function(){Ia.call(n,{response:o.response(),bundle:o.bundle()})},r=function(e,t){J(e,Hl).get(function(e){Ia.call(n,{response:t(e),bundle:o.bundle()})})};xa.cata(o.response(),e,function(t,e){r(e,function(e){return xa.paste(t,e)})},e,function(t,e,n){r(e,function(e){return xa.incomplete(t,e,n)})})})}}var ql=function(r){return function(n){return function(e,t){return n.block(),r(e,t).map(function(e){return n.unblock(),e})}}},$l=function(e){return v(e)},Vl=function(n){var e,t=be("window.clipboardData.files"),r=void 0!==(e=n).convertURL?e.convertURL:void 0!==e.msConvertURL?e.msConvertURL:void 0;if(void 0!==t&&void 0!==r&&0<t.length){var o=J(t,function(e){var t=Hn(e);return r.apply(n,[e,"specified",t]),ma.singleWithUrl(e,t)});return N.some(o)}return N.none()},Xl=function(){var t=N.none();return{convert:function(e){t=Vl(e)},listen:function(e){return t.fold(function(){return z.nu(function(e){e([])})},function(e){return e}).get(e)},clear:function(){t=N.none()}}},Gl=function(e){var t=It.fromTag("div");return kn(t,e),Fn(t,"img[src]")},zl=function(e){return 0===e.indexOf("data:")&&-1<e.indexOf("base64")},Kl=function(e){return 0===e.indexOf("blob:")},Zl=function(e){var t=dt(e,"src");return zl(t)||Kl(t)},Jl=function(e){return W(Gl(e),function(e){var n,t,r,o,i=dt(e,"src");return zl(i)?(r=e,o=i,ta(o).map(function(e){return Yl.assetImage(ma.single(e),r)})).toArray():Kl(i)?(n=e,t=i,na(t).map(function(e){var t=z.nu(function(t){e.then(function(e){ma.single(e).get(t)})});return Yl.assetImage(t,n)})).toArray():[]})};function Ql(l){return function(e,f){return Ia.sync(function(u){var c=function(){Ia.call(u,{response:f.response(),bundle:f.bundle()})},s=function(e){var t,n,r=R(Gl(e),Zl);M(r,Oa),Ia.call(u,{response:0<r.length?(t=e,n=R(t,function(e){return"img"!==rt(e)||!Zl(e)}),xa.incomplete(n,[],"errors.local.images.disallowed")):f.response(),bundle:f.bundle()})},e=function(e,t,n){var r,o,i,a;!1===l.allowLocalImages?s(e):0===t.length?(o=Jl(r=e),i=J(o,function(e){return e.asset()}),a=k(o,function(e){return e.image()}),i.get(function(e){var t=Yl.updateSources(e,a);Ia.call(u,{response:xa.paste(r,t),bundle:f.bundle()})})):c()};xa.cata(f.response(),c,e,c,e)})}}var ed=function(e,t){if(0===e.length)throw"Zero length content passed to Hex conversion";return Ee([Ie(function(e){for(var t=new Array(e.length/2),n=0;n<e.length;n+=2){var r=e.substr(n,2);t[Math.floor(n/2)]=parseInt(r,16)}return t}(e))],{type:t})},td=ie([{unsupported:["id","message","isEquation"]},{supported:["id","contentType","blob","isEquation"]}]),nd={unsupported:td.unsupported,supported:td.supported,cata:function(e,t,n){return e.fold(t,n)}},rd=function(e,t,n){return t.indexOf(e,n)},od=function(e,t,n,r,o,i){return-1===e||-1===t?N.none():N.some({start:e,end:t,bower:n,regex:r,idRef:o,isEquation:i})},id=function(e,t,n){return e.substring(t,n)},ad=function(e,t){if(-1===t)return t;var n,r,o=0,i=e.length;do{if((n=e.indexOf("{",t))<(r=e.indexOf("}",t))&&-1!==n?(t=n+1,++o):(r<n||n<0)&&-1!==r&&(t=r+1,--o),i<t||-1===r)return-1}while(0<o);return t},ud=function(e,t,n,r,o){var i=id(e,n,r);return od(n,r,i,/[^a-fA-F0-9]([a-fA-F0-9]+)\}$/,"i",o)},cd=function(e,t,n,r,o){var i=id(e,n,r);return od(n,r,i,/([a-fA-F0-9]{64,})(?:\}.*)/,"s",o)},sd=function(e,t){var n=rd("{\\pict{",e,t),r=ad(e,n),o=rd("{\\shp{",e,t),i=ad(e,o),a=rd("{\\mmathPict{",e,t),u=ad(e,a),c=-1!==a&&(a<n&&r<u||a<o&&i<u),s=L(cd,e,t,o,i,c),f=L(ud,e,t,n,r,c);return-1===n&&-1===o?N.none():-1===n?s():-1===o?f():o<n&&r<i?f():n<o&&i<r?s():n<o?f():o<n?s():N.none()},fd=function(e,t){return sd(e,t)},ld=function(e){return 0<=e.indexOf("\\pngblip")?er.value("image/png"):0<=e.indexOf("\\jpegblip")?er.value("image/jpeg"):er.error("errors.imageimport.unsupported")},dd=function(e,t){var n=e.match(t);return n&&n[1]&&n[1].length%2==0?er.value(n[1]):er.error("errors.imageimport.invalid")},md=function(e){var t=e.match(/\\shplid(\d+)/);return null!==t?N.some(t[1]):N.none()},pd=function(e){for(var a=[],t=function(){return e.length},n=function(e){var t,r,o,i,n=(r=(t=e).bower,o=t.regex,i=t.isEquation,md(r).map(function(e){var n=t.idRef+e;return ld(r).fold(function(e){return nd.unsupported(n,e,i)},function(t){return dd(r,o).fold(function(e){return nd.unsupported(n,e,i)},function(e){return nd.supported(n,t,ed(e,t),i)})})}));return a=a.concat(n.toArray()),e.end},r=0;r<e.length;)r=fd(e,r).fold(t,n);return a},gd=function(e){var t=e.replace(/\r/g,"").replace(/\n/g,"");return pd(t)},vd={images:function(e){return gd(e)},toId:function(e){return nd.cata(e,function(e,t,n){return e},function(e,t,n,r){return e})},isEquation:function(e){return nd.cata(e,function(e,t,n){return n},function(e,t,n,r){return r})},toBlob:function(e){return nd.cata(e,function(e,t,n){return er.error(t)},function(e,t,n,r){return er.value(n)})}},hd={convert:function(e,t,n,o){var i=F(t,function(t,n){var r=vd.toId(n),o=vd.isEquation(n);return U(t,function(e){return vd.toId(e)===r&&vd.isEquation(e)===o}).fold(function(){return t.concat([n])},function(e){return vd.toBlob(t[e]).isValue()?t:t.slice(0,e).concat(t.slice(e+1)).concat([n])})},[]),a=[],u=!1,r=W(e,function(t,e){var n=dt(t,"data-image-id"),r="true"===dt(t,"data-ms-equation");return pt(t,"rtf-data-image"),pt(t,"data-image-id"),pt(t,"data-ms-equation"),"unsupported"===n?(u=!0,ft(t,"alt",o("errors.imageimport.unsupported")),[]):j(i,function(e){return vd.toId(e)===n&&vd.isEquation(e)===r}).fold(function(){return console.log("WARNING: unable to find data for image ",t.dom()),[]},function(e){return vd.toBlob(e).fold(function(e){return u=!0,ft(t,"alt",o(e)),[]},function(e){return a.push(t),[e]})})});ma.multiple(r).get(function(e){var t=Yl.updateSources(e,a);n(t,u)})}},yd=function(e){return Fn(e,"[rtf-data-image]")},bd={exists:function(e){return 0<yd(e).length},find:yd};function xd(e){var r=e.translations,u=Xn.create({insert:Vn(["elements","correlated"]),incomplete:Vn(["elements","correlated","message"])});return{events:u.registry,processRtf:function(o,i,e,a){var t=vd.images(e),n=bd.find(o);hd.convert(n,t,function(e,t){var n=In(o),r=e.concat(i);t?u.trigger.incomplete(n,r,"errors.imageimport.failed"):u.trigger.insert(n,r),a()},r)}}}function Td(e,t){var i,a,u,c,s=(i=e,a=t,u=Xn.create({insert:Vn(["elements","correlated"]),incomplete:Vn(["elements","correlated","message"])}),(c=xd(a)).events.incomplete.bind(function(e){u.trigger.incomplete(e.elements(),e.correlated(),e.message())}),c.events.insert.bind(function(e){u.trigger.insert(e.elements(),e.correlated())}),{events:u.registry,gordon:function(n,r){var t=function(e){var t=bd.find(n);M(t,Oa),u.trigger.incomplete(In(n),r,e)},e=function(e){t(e.message())};if(!0===a.allowLocalImages&&!0===a.enableFlashImport){var o=i(a);o.events.response.bind(function(e){c.processRtf(n,r,e.rtf(),e.hide())}),o.events.cancel.bind(function(){var e=bd.find(n);M(e,Oa),u.trigger.insert(In(n),r)}),o.events.failed.bind(e),o.events.error.bind(e),o.open()}else t("errors.local.images.disallowed")}}),f=wc(N.none()),n=function(t){f.get().each(function(e){Ia.call(e,{response:t,bundle:ha.nu({})})})};return s.events.insert.bind(function(e){n(xa.paste(e.elements(),e.correlated()))}),s.events.incomplete.bind(function(e){n(xa.incomplete(e.elements(),e.correlated(),e.message()))}),function(e,t){return Ia.sync(function(o){var i=function(){Ia.call(o,{response:t.response(),bundle:t.bundle()})},e=function(e,t,n){f.set(N.some(o));var r=It.fromTag("div");kn(r,e),bd.exists(r)?s.gordon(r,t,n):i()};xa.cata(t.response(),i,e,i,e)})}}var Ed=function(e){return e.officeStyles().getOr(!0)},wd=function(e){return e.htmlStyles().getOr(!1)},Id=function(e){return e.isWord().getOr(!1)},Sd={proxyBin:function(n){return{handle:function(e,t){return n.proxyBin().fold(function(){return console.error(e),Ia.pure({response:xa.cancel(),bundle:ha.nu({})})},t)}}},backgroundAssets:function(e){return z.nu(function(t){e.backgroundAssets().fold(function(){t([])},function(e){e.listen(t)})})},merging:function(e){var t=Id(e);return t&&Ed(e)||!t&&wd(e)},mergeOffice:Ed,mergeNormal:wd,isWord:Id,isGoogleDocs:function(e){return e.isGoogleDocs().getOr(!1)},isInternal:function(e){return e.isInternal().getOr(!1)}},Ld={resolve:Xe("ephox-cement").resolve};function Nd(s,r){var f=r.translations,l=function(e,t,n){n(N.some(se(t,{officeStyles:e,htmlStyles:e})))};return{get:function(e,t){var n=r[e?"officeStyles":"htmlStyles"];"clean"===n?l(!1,r,t):"merge"===n?l(!0,r,t):function(e,t){var n=It.fromTag("div");xt(n,Ld.resolve("styles-dialog-content"));var r=It.fromTag("p"),o=Nn(f("cement.dialog.paste.instructions"));kn(r,o),_n(n,r);var i={text:f("cement.dialog.paste.clean"),tabindex:0,className:Ld.resolve("clean-styles"),click:function(){u(),l(!1,e,t)}},a={text:f("cement.dialog.paste.merge"),tabindex:1,className:Ld.resolve("merge-styles"),click:function(){u(),l(!0,e,t)}},u=function(){c.destroy()},c=s(!0);c.setTitle(f("cement.dialog.paste.title")),c.setContent(n),c.setButtons([i,a]),c.events.close.bind(function(){t(N.none()),u()}),c.show()}(r,t)},destroy:S}}var Cd,Od,Dd,_d=function(e,t){var i=Nd(e,t);return function(e,r){var t=r.bundle(),o=r.response();return Ia.sync(function(n){i.get(Sd.isWord(t),function(e){var t=e.fold(function(){return{response:xa.cancel(),bundle:r.bundle()}},function(e){return{response:o,bundle:ha.nu({officeStyles:e.officeStyles,htmlStyles:e.htmlStyles})}});Ia.call(n,t)})})}},Ad=_d,Pd=function(r,o){return function(e,t){var n=function(e){return Ia.pure({response:t.response(),bundle:ha.nu({officeStyles:e,htmlStyles:e})})};return Sd.isInternal(t.bundle())?n(!0):Sd.isGoogleDocs(t.bundle())?n(!1):_d(r,o)(e,t)}},kd=function(m,p){return function(e){if(m(e)){var t,n,r,o,i,a,u,c=It.fromDom(e.target),s=function(){e.stopPropagation()},f=function(){e.preventDefault()},l=g(f,s),d=(t=c,n=e.clientX,r=e.clientY,o=s,i=f,a=l,u=e,{target:v(t),x:v(n),y:v(r),stop:o,prevent:i,kill:a,raw:v(u)});p(d)}}},Md=function(e,t,n,r){return o=e,i=t,a=!1,u=kd(n,r),o.dom().addEventListener(i,u,a),{unbind:L(Rd,o,i,u,a)};var o,i,a,u},Rd=function(e,t,n,r){e.dom().removeEventListener(t,n,r)},Fd=v(!0),jd=function(e,t,n){return Md(e,t,Fd,n)},Ud=function(e){return function(e){var t=e.dom();try{var n=t.contentWindow?t.contentWindow.document:t.contentDocument;return null!=n?N.some(It.fromDom(n)):N.none()}catch(e){return console.log("Error reading iframe: ",t),console.log("Error was: "+e),N.none()}}(e).fold(function(){return e},function(e){return e})},Bd=function(e,t){if(!Mn(e))throw"Internal error: attempted to write to an iframe that is not in the DOM";var n=Ud(e).dom();n.open("text/html","replace"),n.writeln(t),n.close()},Yd=function(e){var t=e.dom().styleSheets;return Array.prototype.slice.call(t)},Hd=St("selector","style","raw"),Wd=function(e){var t=e.cssRules;return W(t,function(e){return e.type===CSSRule.IMPORT_RULE?Wd(e.styleSheet):e.type===CSSRule.STYLE_RULE?[function(e){var t=e.selectorText,n=e.style.cssText;if(void 0===n)throw"WARNING: Browser does not support cssText property";return Hd(t,n,e.style)}(e)]:[]})},qd=function(e){return W(e,Wd)},$d={},Vd={exports:$d};Od=$d,Dd=Vd,Cd=void 0,function(e){"object"==typeof Od&&void 0!==Dd?Dd.exports=e():"function"==typeof Cd&&Cd.amd?Cd([],e):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=e()}(function(){return function i(a,u,c){function s(t,e){if(!u[t]){if(!a[t]){var n=!1;if(!e&&n)return n(t,!0);if(f)return f(t,!0);var r=new Error("Cannot find module '"+t+"'");throw r.code="MODULE_NOT_FOUND",r}var o=u[t]={exports:{}};a[t][0].call(o.exports,function(e){return s(a[t][1][e]||e)},o,o.exports,i,a,u,c)}return u[t].exports}for(var f=!1,e=0;e<c.length;e++)s(c[e]);return s}({1:[function(e,t,n){var r,a,o=(r=function(e){var t,n,r,o,i=[];for(r=0,o=(t=e.split(",")).length;r<o;r+=1)0<(n=t[r]).length&&i.push(a(n));return i},a=function(c){var e,t,n,s=c,f={a:0,b:0,c:0},l=[];return e=function(e,t){var n,r,o,i,a,u;if(e.test(s))for(r=0,o=(n=s.match(e)).length;r<o;r+=1)f[t]+=1,i=n[r],a=s.indexOf(i),u=i.length,l.push({selector:c.substr(a,u),type:t,index:a,length:u}),s=s.replace(i,Array(u+1).join(" "))},(t=function(e){var t,n,r,o;if(e.test(s))for(n=0,r=(t=s.match(e)).length;n<r;n+=1)o=t[n],s=s.replace(o,Array(o.length+1).join("A"))})(/\\[0-9A-Fa-f]{6}\s?/g),t(/\\[0-9A-Fa-f]{1,5}\s/g),t(/\\./g),(n=/:not\(([^\)]*)\)/g).test(s)&&(s=s.replace(n,"     $1 ")),function(){var e,t,n,r,o=/{[^]*/gm;if(o.test(s))for(t=0,n=(e=s.match(o)).length;t<n;t+=1)r=e[t],s=s.replace(r,Array(r.length+1).join(" "))}(),e(/(\[[^\]]+\])/g,"b"),e(/(#[^\#\s\+>~\.\[:]+)/g,"a"),e(/(\.[^\s\+>~\.\[:]+)/g,"b"),e(/(::[^\s\+>~\.\[:]+|:first-line|:first-letter|:before|:after)/gi,"c"),e(/(:[\w-]+\([^\)]*\))/gi,"b"),e(/(:[^\s\+>~\.\[:]+)/g,"b"),s=(s=s.replace(/[\*\s\+>~]/g," ")).replace(/[#\.]/g," "),e(/([^\s\+>~\.\[:]+)/g,"c"),l.sort(function(e,t){return e.index-t.index}),{selector:c,specificity:"0,"+f.a.toString()+","+f.b.toString()+","+f.c.toString(),specificityArray:[0,f.a,f.b,f.c],parts:l}},{calculate:r,compare:function(e,t){var n,r,o;if("string"==typeof e){if(-1!==e.indexOf(","))throw"Invalid CSS selector";n=a(e).specificityArray}else{if(!Array.isArray(e))throw"Invalid CSS selector or specificity array";if(4!==e.filter(function(e){return"number"==typeof e}).length)throw"Invalid specificity array";n=e}if("string"==typeof t){if(-1!==t.indexOf(","))throw"Invalid CSS selector";r=a(t).specificityArray}else{if(!Array.isArray(t))throw"Invalid CSS selector or specificity array";if(4!==t.filter(function(e){return"number"==typeof e}).length)throw"Invalid specificity array";r=t}for(o=0;o<4;o+=1){if(n[o]<r[o])return-1;if(n[o]>r[o])return 1}return 0}});void 0!==n&&(n.calculate=o.calculate,n.compare=o.compare)},{}],2:[function(e,t,n){var r=e("specificity");t.exports={boltExport:r}},{specificity:1}]},{},[2])(2)});var Xd=Vd.exports.boltExport,Gd=St("selector","raw"),zd=function(t,e){var n=W(e,function(i){var e=Fn(t,i.selector());return M(e,function(e){var n,r,o,t=(n=i.raw(),r=e,o={},M(n,function(e){if(void 0!==n[e]){var t=r.dom().style;A(t,e)||(o[e]=n[e])}}),o);Ma(e,t)}),e});M(n,function(e){pt(e,"class")})},Kd=function(e,t){var n=function(e){return-1!==e.selector().indexOf(",")},r=W(R(e,n),function(n){var e=n.selector().split(",");return k(e,function(e){var t=e.trim();return Gd(t,n.raw())})}),o=R(e,function(e){return!n(e)}).concat(r);o.sort(function(e,t){return Xd.compare(e.selector(),t.selector())}).reverse(),zd(t,o)},Zd=function(e,t,r){var n=Yd(e),o=qd(n).map(function(e){var t=e.selector(),n=r.hasOwnProperty(t)?r[t]:t;return Gd(n,e.raw())});Kd(o,t)},Jd=function(e,t,n){var r=Yd(e),o=qd(r),i=R(o,function(e){return Qt(e.selector(),n)});Kd(i,t)},Qd={inlineStyles:function(e,t,n){Zd(e,t,n)},inlinePrefixedStyles:function(e,t,n){Jd(e,t,n)}},em={p:"p, li[data-converted-paragraph]"},tm=S,nm=function(f,e){var l=function(n){pt(n,"data-list-level"),pt(n,"data-text-indent-alt"),pt(n,"data-border-margin"),Ua(n,"margin-left"),Ua(n,"text-indent"),ee(function(e){var t={},n=e.dom();if(Aa(n))for(var r=0;r<n.style.length;r++){var o=n.style.item(r);t[o]=n.style[o]}return t}(n),function(e,t){!t.startsWith("border")||"border-image"!==t&&"none"!==e.trim()&&"initial"!==e.trim()||Ua(n,t)})},t=Fn(f,"li[data-converted-paragraph]");if(M(t,function(e){pt(e,"data-converted-paragraph")}),e){var n=Fn(f,"li");M(n,function(e){var t,n,r,o,i,a,u=(t=f,n=It.fromTag("span"),Dn(t,n),r=n,{convertToPx:function(e){var t;return ka(r,"margin-left",e),t=Ra(r,"margin-left"),parseFloat(t.match(/-?\d+\.?\d*/)[0])},destroy:function(){return Oa(r)}}),c=(i=u,a=mt(o=f,"data-tab-interval")?dt(o,"data-tab-interval"):"36pt",i.convertToPx(a)),s=rm(e,c,u).getOr({});l(e),u.destroy(),Ma(e,s)});var r=Fn(f,"ol,ul");M(r,function(t){var e=Fn(t,"li");ja(t,"margin-top").isNone()&&N.from(e[0]).each(function(e){ka(t,"margin-top",Ra(e,"margin-top"))}),ja(t,"margin-bottom").isNone()&&N.from(e[e.length-1]).each(function(e){ka(t,"margin-bottom",Ra(e,"margin-bottom"))})})}pt(f,"data-tab-interval")},rm=function(l,d,m){var p=function(e){return mt(e,"data-list-level")?parseInt(dt(e,"data-list-level"),10):1};return ja(l,"text-indent").bind(function(f){return ja(l,"margin-left").map(function(e){var t=ja(l,"list-style").exists(function(e){return Zt(e,"none")}),n=mt(l,"data-border-margin")?dt(l,"data-border-margin"):"0px",r=t?p(l)+1:p(l),o=m.convertToPx(e)+m.convertToPx(n),i=d*r,a=mt(l,"data-text-indent-alt")?m.convertToPx(dt(l,"data-text-indent-alt")):m.convertToPx(f),u={},c=d/2*-1-a;0<c&&(u["text-indent"]=c+"px");var s=o-i-c;return u["margin-left"]=0<s?s+"px":"0px",u})})},om=function(e,t,n){var r=n.mergeInline();(r?Qd.inlineStyles:tm)(e,t,em),nm(t,r)},im=function(n){var e,r=(e=It.fromDom(document.body),{play:function(i,a,u){var c=It.fromTag("div"),s=It.fromTag("iframe");Ma(c,{display:"none"});var f=jd(s,"load",function(){f.unbind(),Bd(s,i);var e=s.dom().contentWindow.document;if(void 0===e)throw"sandbox iframe load event did not fire correctly";var t=It.fromDom(e),n=e.body;if(void 0===n)throw"sandbox iframe does not have a body";var r=It.fromDom(n),o=a(t,r);Oa(c),setTimeout(L(u,o),0)});_n(c,s),_n(e,c)}});return function(e,t){r.play(e,function(e,t){return om(e,t,{mergeInline:v(n)}),wu(t)},t)}},am=function(e,c,t,s){var n=e.html();return Ia.sync(function(i){t.cleanDocument(n,c).get(function(e){e.fold(function(e){Ia.call(i,{response:xa.error("errors.paste.process.failure"),bundle:ha.nu({})})},function(e){var t,n,a,u,r,o;null==(o=e)||0===o.length?(r=i,Ia.call(r,{response:xa.paste([],[]),bundle:ha.nu({})})):(t=i,n=e,a=s,u=function(e){Ia.call(t,{response:e,bundle:ha.nu({})})},im(c)(n,function(e){var t=Nn(e),n=function(e){u(xa.paste(t,e))},r=It.fromTag("div");kn(r,t);var o=pn("img[src]",r);if(0===o.length)n([]);else if(a)Yl.browserBlobs(o).get(n);else{M(o,Oa);var i=In(r);u(xa.incomplete(i,[],"errors.local.images.disallowed"))}}))})})})},um=function(e){var t=R(e,function(e){return"file"===e.kind&&/image/.test(e.type)}),r=k(t,function(e){return e.getAsFile()});return Ia.sync(function(n){ma.multiple(r).get(function(e){var t=Yl.createImages(e);Ia.call(n,{response:t,bundle:ha.nu({})})})})},cm=sn.detect(),sm=function(e){try{var t=e(),n=null!=t&&0<t.length?Nn(t):[];return er.value(n)}catch(e){return console.error(e),er.error("errors.paste.process.failure")}},fm=function(e){return e.fold(function(e){return Ia.error(e)},function(e){return Ia.pure({response:xa.paste(e,[]),bundle:ha.nu({})})})},lm=function(e,t,n,r){return sm(function(){return qf(e,cm,t,n,r)})},dm=function(e,t,n){var r=lm(e,t,n,!1);return fm(r)},mm=function(e,t){var n=sm(function(){return Vf(e,t)});return fm(n)},pm=function(e,t,n,r,o){return lm(e,t,r,n).fold(function(e){return Ia.error(e)},function(r){return Ia.sync(function(n){o.get(function(e){var t=Yl.findImages(r,e);Ia.call(n,{response:xa.paste(r,t),bundle:ha.nu({})})})})})},gm=function(e,t,n){var r=t.findClipboardTags(In(n)).getOr([]);M(r,Oa);var o=z.nu(function(e){e([])});return pm(e,n,!1,!0,o)},vm=function(e,t,n,r,o){return pm(e,t,r,n,o)},hm=function(e){var t,n=It.fromTag("div");return t=e,n.dom().textContent=t,wu(n)},ym=function(e){var t=e.trim().split(/\n{2,}|(?:\r\n){2,}/),n=k(t,function(e){return e.split(/\n|\r\n/).join("<br />")});return 1===n.length?n[0]:k(n,function(e){return"<p>"+e+"</p>"}).join("")},bm=function(e){var a=el.asText(e).getOrDie("Required text input for Text Handler");return Ia.sync(function(e){var t,n,r,o,i=0<a.text().length?(t=a.text(),n=hm(t),r=ym(n),o=Nn(r),xa.paste(o,[])):xa.cancel();Ia.call(e,{response:i,bundle:ha.nu({})})})},xm=function(e,t,n){return bm(e)},Tm=function(e,o){var t=function(e,t){var n=It.fromTag("div");kn(n,e),$f(n);var r=In(n);return Ia.pure({response:xa.paste(r,t),bundle:o.bundle()})},n=L(Ia.pass,o);return xa.cata(o.response(),n,t,n,t)},Em=function(){return function(e,t){return Ia.error("errors.local.images.disallowed")}},wm=function(){return function(e,t){var n=el.asImages(e).getOrDie("Must have image data for images handler");return um(n.images())}},Im=function(i){return function(e,t){var n=el.asHtml(e).getOrDie("Wrong input type for HTML handler"),r=i.findClipboardTags(In(n.container()));r.each(function(e){M(e,Oa)});var o=r.isSome();return Ia.pure({response:t.response(),bundle:ha.nu({isInternal:o})})}},Sm=function(a,u){return function(e,t){var n=el.asHtml(e).getOrDie("Wrong input type for HTML handler").container(),r=yn(u),o=t.bundle();if(Sd.isInternal(o))return mm(r,n);a(n);var i=Sd.merging(o);return dm(r,n,i)}},Lm=function(u,c){return function(e,t){var a=t.bundle();return Sd.proxyBin(a).handle("There was no proxy bin setup. Ensure you have run proxyStep first.",function(e){var t=Sd.merging(a),n=Sd.isWord(a),r=Sd.isInternal(a),o=Sd.backgroundAssets(a),i=yn(u);return r?gm(i,c,e):vm(i,e,t,n,o)})}},Nm=function(o,i){return function(e,t){var n=el.asWord(e).getOrDie("Wrong input type for Word Import handler"),r=Sd.mergeOffice(t.bundle());return am(n,r,o,i)}},Cm=function(r){return function(e,t){var n=ha.merge(t.bundle(),ha.nu(r));return Ia.pure({response:t.response(),bundle:n})}},Om=function(e,t){return Ia.cancel()},Dm=Ld.resolve("smartpaste-eph-bin"),_m={binStyle:v(Dm)},Am=function(e,t){return Gc(e,function(e){return!!mt(e,"style")&&-1<dt(e,"style").indexOf("mso-")})},Pm=function(e,t){var n=wu(e);return Ou(n,t)},km=function(e,t){var n=e.browser;return(n.isIE()&&11<=n.version.major?Am:Pm)(t,e)},Mm=sn.detect();function Rm(r,f,o,l,i){return function(e,t){var n=el.asEvent(e).getOrDie("Must pass through event type").nativeEvent(),c=i(),s=t.response();return Ia.sync(function(u){var e=r(o);e.events.after.bind(function(e){var t=e.container();if(Mm.browser.isSafari()&&sr(t,'img[src^="webkit-fake-url"]').isSome()){var n=Mm.deviceType.isWebView()?"webview.imagepaste":"safari.imagepaste";Ia.call(u,{response:xa.error(n),bundle:ha.nu({})})}else{f(t),xt(t,_m.binStyle());var r=km(Mm,t),o=In(t),i=l.findClipboardTags(o).isSome(),a=P(o,function(e){return mt(e,"id")&&Qt(dt(e,"id"),"docs-internal-guid")});Ia.call(u,{response:s,bundle:ha.nu({isWord:r,isGoogleDocs:a,isInternal:i,proxyBin:t,backgroundAssets:c})})}}),c.convert(n),e.run()})}}var Fm=function(e,t){return e.isSupported()?t.getWordData():N.none()},jm=function(e){return e.getNative()},Um=function(e){return e.getImage()},Bm=function(e){return e.getHtml()},Ym=function(e){return e.getText()},Hm=function(e){return e.getOnlyText()},Wm=function(e){return e.getGoogleDocsData()},qm=function(e,t,n,r){return{_label:e,label:v(e),getAvailable:t,steps:v(n),capture:v(r)}},$m={wordimport:function(e,t,n,r){return qm("Word Import pasting",L(Fm,e),[$l(Cm({isWord:!0})),$l(Ad(t,n)),$l(Nm(e,n.allowLocalImages)),ql((o=n,i=Td(r,n),a=xd(o),u=wc(N.none()),c=function(t){u.get().each(function(e){Ia.call(e,{response:t,bundle:ha.nu({})})})},a.events.insert.bind(function(e){c(xa.paste(e.elements(),e.correlated()))}),a.events.incomplete.bind(function(e){c(xa.incomplete(e.elements(),e.correlated(),e.message()))}),function(t,r){var e=el.asWord(t).getOrDie("Word input required for rtf data"),n=function(o){return Ia.sync(function(t){var e=function(){Ia.call(t,{response:r.response(),bundle:r.bundle()})},n=function(e,n){u.set(N.some(t));var r=It.fromTag("div");kn(r,e),o.fold(function(){var e,t=bd.find(r);return 0<t.length?function(){M(t,Oa);var e=In(r);c(xa.incomplete(e,n,"errors.local.images.disallowed"))}():(e=In(r),void c(xa.paste(e,n)))},function(e){a.processRtf(r,n,e,S)})};xa.cata(r.response(),e,n,e,n)})};return Zf.match(e.rtf(),{getFromFlash:function(e){return i(t,r)},disabled:function(){return n(N.none())},fromClipboard:function(e){return n(!0===o.allowLocalImages?N.some(e.rtf()):N.none())}})})),$l(Wl())],!0);var o,i,a,u,c},googledocs:function(e,t,n){return qm(" pasting",Wm,[$l(Cm({officeStyles:!1,htmlStyles:!1})),$l(Sm(t,n)),$l(Ql(e)),$l(Wl())],!0)},image:function(e){return qm("Image pasting",Um,[$l(!1===e.allowLocalImages?Em():wm()),$l(Wl())],!0)},nativeHtml:function(e,t,n,r){return qm("Outside of Textbox.io pasting HTML5 API (could be internal)",Bm,[$l(Im(t.intraFlag)),$l(Pd(e,t)),$l(Sm(n,r)),$l(Ql(t)),$l(Wl())],!0)},text:function(){return qm("Plain text pasting",Ym,[$l(xm),$l(Tm)],!0)},onlyText:function(){return qm("Only plain text is available to paste",Hm,[$l(xm),$l(Tm)],!0)},none:function(){return qm("There is no valid way to paste",N.some,[$l(Om)],!1)},fallback:function(e,t,n,r,o,i){return qm("Outside of Textbox.io pasting offscreen (could be internal)",jm,[$l(Rm(r,n,o,t.intraFlag,Xl)),$l(Pd(e,t)),$l(Lm(o,t.intraFlag)),ql(Td(i,t)),$l(Ql(t)),$l(Wl())],!1)}},Vm=function(u){var c=L(xe,u);xe("callbacks",c());var t=function(e,t){var n,r,o,i=c(),a=(r=void 0===(n=i).count?0:n.count,o="callback_"+r,n.count=r+1,o);return i.callbacks[a]=function(){t||s(a),e.apply(null,arguments)},u+".callbacks."+a},s=function(e){var t=e.substring(e.lastIndexOf(".")+1),n=c();void 0!==n.callbacks[t]&&delete n.callbacks[t]};return{ephemeral:function(e){return t(e,!1)},permanent:function(e){return t(e,!0)},unregister:s}},Xm=function(e){e.dom().focus()},Gm={responsive:function(){var a=wc(N.none()),u=Xn.create({crashed:Vn([]),timeout:Vn([])});return{start:function(e,t,n,r){var o=t,i=setInterval(function(){n()?clearInterval(i):o<=0?(u.trigger.timeout(),clearInterval(i)):r()&&(clearInterval(i),u.trigger.crashed()),o--},e);a.set(N.some(i))},stop:function(){a.get().each(function(e){clearInterval(e)})},events:u.registry}}};function zm(n,r,o){var e=!0,t=setInterval(function(){var t,e=n.dom();O(e.PercentLoaded)&&100===e.PercentLoaded()&&(t=e,q(r,function(e){return O(t[e])}))&&(i(),o())},500),i=function(){e&&(clearInterval(t),e=!1)};return{stop:i}}var Km=Vm("ephox.flash"),Zm=sn.detect(),Jm=N.none(),Qm=function(e){return It.fromHtml("<p>"+e("cement.dialog.flash.press-escape")+"</p>")},ep=function(e){var t=It.fromTag("div");xt(t,Ld.resolve("flashbin-helpcopy"));var n=sn.detect().os.isOSX()?["\u2318"]:["Ctrl"],r=It.fromHtml("<p>"+e("cement.dialog.flash.trigger-paste")+"</p>"),o=It.fromHtml('<div><span class="ephox-polish-help-kbd">'+n+'</span> + <span class="ephox-polish-help-kbd">V</span></div>');return xt(o,Ld.resolve("flashbin-helpcopy-kbd")),kn(t,[r,o,Qm(e)]),t},tp=function(e){var t=It.fromTag("div");xt(t,Ld.resolve("flashbin-helpcopy"));var n=It.fromHtml("<p>"+e("cement.dialog.flash.missing")+"</p>");return kn(t,[n,Qm(e)]),t},np=function(e){var t=It.fromTag("div");xt(t,Ld.resolve("flashbin-loading"));var n=It.fromTag("div");xt(n,Ld.resolve("flashbin-loading-spinner"));var r=It.fromTag("p"),o=e("loading.wait");return Iu(r,o),ft(r,"aria-label",o),kn(t,[n,r]),t},rp=sn.detect(),op=function(){try{return void 0!==navigator.plugins["Shockwave Flash"]}catch(e){return!1}},ip=function(e,t,n,r){var o=tp(r);return _n(e,o),{reset:S}},ap=function(e,t,n,r){var o=ep(r),i=np(r);kn(e,[i,o,t.element()]);var a=function(){ka(o,"display","block"),ka(i,"display","none"),n()};return t.events.spin.bind(function(){ka(o,"display","none"),ka(i,"display","block"),Ua(i,"height"),Ua(i,"padding"),n()}),t.events.reset.bind(a),t.events.hide.bind(function(){Ma(i,{height:"0",padding:"0"})}),{reset:a}};function up(c,s){var f=s.translations,l=Xn.create({response:Vn(["rtf","hide"]),cancel:Vn([]),error:Vn(["message"]),failed:Vn(["message"])});return{open:function(){var e=function(r){var t=Xn.create({response:Vn(["rtf"]),spin:Vn([]),cancel:Vn([]),error:Vn(["message"]),reset:Vn([]),hide:Vn([]),failed:Vn(["message"])}),e=!1,n=It.fromTag("div");xt(n,Ld.resolve("flashbin-target"));var o=Gm.responsive();o.events.crashed.bind(function(){t.trigger.failed("flash.crashed")}),o.events.timeout.bind(function(){t.trigger.failed("flash.crashed")});var i=function(){if(d.stop(),!e){e=!0;try{var r=f.dom();ee(s,function(e,t){var n=r[t];if(void 0===n)throw'Flash object does not have the method "'+t+'"';n.call(r,e)}),t.trigger.reset(),v(),m()}catch(e){console.log("Flash dialog - Error during load: ",e)}}},a=Km.permanent(i),u=function(){return!Mn(f)},c=function(){return!f.dom().SetVariable},s={setSpinCallback:Km.permanent(function(){o.start(1e3,10,u,c),t.trigger.spin()}),setPasteCallback:Km.permanent(function(e){o.stop(),setTimeout(function(){""===e?t.trigger.error("flash.crashed"):t.trigger.response(unescape(e))},0)}),setEscapeCallback:Km.permanent(t.trigger.cancel),setErrorCallback:Km.permanent(function(e){o.stop(),t.trigger.error(e)}),setStartPasteCallback:Km.permanent(S)},f=function(){var e=r.replace(/^https?:\/\//,"//"),t='    <param name="allowscriptaccess" value="always">    <param name="wmode" value="opaque">    <param name="FlashVars" value="onLoad='+a+'">';if(Zm.browser.isIE()&&10===Zm.browser.version.major){var n=oa("flash-bin");return It.fromHtml('<object classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000" id="'+n+'"><param name="movie" value="'+e+'">'+t+"</object>")}return It.fromHtml('<object type="application/x-shockwave-flash" data="'+e+'">'+t+"</object>")}(),l=function(){Ma(f,{width:"2px",height:"2px"})};l();var d=zm(f,Q(s),i);_n(n,f);var m=function(){Zm.browser.isFirefox()&&window.getSelection().removeAllRanges(),Xm(f)},p=null,g=function(){xt(n,Ld.resolve("flash-activate")),Ua(f,"height"),Ua(f,"width"),t.trigger.hide()},v=function(){clearTimeout(p),Tt(n,Ld.resolve("flash-activate")),l()},h=function(){ka(n,"display","none"),Jm.each(function(e){M(e,function(e){e.unbind()})})};return{focus:m,element:function(){return n},activate:function(){p=setTimeout(g,3e3),t.trigger.spin(),ka(n,"display","block"),m()},deactivate:h,destroy:function(){h(),M(re(s),function(e){Km.unregister(e)}),Km.unregister(a),d.stop()},events:t.registry}}(s.swf);e.deactivate();var t=It.fromDom(window),n=jd(t,"mouseup",e.focus),r=function(){u()},o=function(){r(),l.trigger.cancel()};e.events.cancel.bind(o),e.events.response.bind(function(e){l.trigger.response(e.rtf(),r)}),e.events.error.bind(function(e){r(),l.trigger.error(e.message())}),e.events.failed.bind(function(e){r(),l.trigger.failed(e.message())});var i=c();i.setTitle(f("cement.dialog.flash.title"));var a=function(e,t,n){var r=It.fromTag("div"),o="flashbin-wrapper-"+(rp.os.isOSX()?"cmd":"ctrl");xt(r,Ld.resolve(o));var i=(op()?ap:ip)(r,e,t,n.translations);return{element:v(r),reset:i.reset}}(e,i.reflow,s);a.reset(),i.setContent(a.element()),i.events.close.bind(o),i.show(),e.activate();var u=function(){n.unbind(),e.destroy(),i.destroy()}},events:l.registry}}function cp(){var o={};return{getOrSetIndexed:function(e,t){return void 0!==o[e]?o[e]:(n=e,r=t(),o[n]=r);var n,r},waitForLoad:function(){var e=re(o);return Z(e)}}}var sp,fp,lp=Vm("ephox.henchman.features"),dp=function(i){return X.nu(function(t){var e=function(){r.unbind(),o.unbind()},n=It.fromTag("script");ft(n,"src",i),ft(n,"type","text/javascript"),ft(n,"async","async"),ft(n,"data-main",lp.ephemeral(function(e){t(er.value(e))}));var r=jd(n,"error",function(){e(),t(er.error("Error loading external script tag "+i))}),o=jd(n,"load",e);_n(It.fromDom(document.head),n)})},mp=function(e,t){var n,r,o,i=t||It.fromDom(document),a=It.fromTag("link",i.dom());return lt(a,{rel:"stylesheet",type:"text/css",href:e}),n=i,r=a,o=It.fromDom(n.dom().head),_n(o,r),a},pp=function(o,i){return X.nu(function(t){var n=function(e){M(r,function(e){e.unbind()}),t(e.fold(function(e){return er.error(e+'Unable to download editor stylesheets from "'+o+'"')},er.value))},e=mp(o),r=[jd(e,"load",function(e){!function(e){try{var t=e.target().dom().sheet.cssRules;return w(t)&&0===t.length}catch(e){}return!1}(e)?i(n):n(er.error(""))}),jd(e,"error",L(n,er.error("")))]})},gp=(sp=cp(),{preload:function(){fp().get(l)},addStylesheet:function(e,t){return sp.getOrSetIndexed(e,function(){return pp(e,t)})},addScript:function(e,t){return sp.getOrSetIndexed(e,function(){return dp(e).map(t)})},waitForLoad:fp=function(){return sp.waitForLoad()}}),vp=function(e,t){return gp.addScript(e,t)},hp=sn.detect(),yp=hp.deviceType.isiOS()||hp.deviceType.isAndroid(),bp=v({isSupported:v(!1),cleanDocument:v(Rr("not supported"))}),xp=yp?bp:function(e){var r=vp(e+"/wordimport.js",l);return r.get(function(e){e.fold(function(e){console.error("Unable to load word import: ",e)},S)}),{isSupported:v(!0),cleanDocument:function(t,n){return r.map(function(e){return e.map(function(e){return e.cleanDocument(t,n)})})}}};function Tp(e,t,n,r){var o=vn(e,n)&&t===r;return{startContainer:v(e),startOffset:v(t),endContainer:v(n),endOffset:v(r),collapsed:v(o)}}var Ep,wp,Ip=["b","i","u","sub","sup","strike"],Sp=function(e){M(In(e),function(e){var t;ut(t=e)&&!t.dom().hasChildNodes()&&A(Ip,rt(t))&&Oa(e)})},Lp=function(e,o){var t=In(e);M(t,function(e){var t,n,r;o(e)&&(n=In(t=e),r=It.fromTag("div",yn(t).dom()),kn(r,n),Cn(t,r),Oa(t))})},Np={consolidate:function(n,e){En(n).filter(e).each(function(e){var t=In(e);kn(n,t),Oa(e)}),Lp(n,e),Sp(n)}},Cp=function(e){return"rtl"===Ra(e,"direction")?"rtl":"ltr"},Op=hl.bin(),Dp=Op+oa(""),_p=(Ep="-100000px",wp="100000px",function(e){return"rtl"===Cp(e)?wp:Ep});function Ap(t,e,n){var r=function(t,e){var n=It.fromTag("div");lt(n,e),lt(n,{contenteditable:"true","aria-hidden":"true"}),Ma(n,{position:"fixed",top:"0px",width:"100px",height:"100px",overflow:"hidden",opacity:"0"}),tr(n,[Op,Dp]);var r=function(e){return Et(e,Dp)};return{attach:function(e){Ca(n),ka(n,"left",_p(e)),_n(e,n)},focus:function(){cr(n,"body").each(function(e){t.toOff(e,n)})},contents:function(){return Np.consolidate(n,r),St("elements","html","container")(In(n),wu(n),n)},container:function(){return n},detach:function(){Oa(n)}}}(t,n),o=function(){t.cleanup();var e=r.contents();r.detach(),a.trigger.after(e.elements(),e.html(),r.container())},i=gl.tap(function(){a.trigger.before(),r.attach(e),r.focus(),Tl.run(yn(e),i,o)}),a=Xn.create({before:Vn([]),after:Vn(["elements","html","container"])}),u=S;return{instance:v(function(){i.instance()}),destroy:u,events:a.registry}}var Pp=function(e){return{startContainer:e.start,startOffset:e.soffset,endContainer:e.finish,endOffset:e.foffset}},kp={set:function(e,t){kl(e,t.startContainer(),t.startOffset(),t.endContainer(),t.endOffset())},get:function(e,t){return Rl(e).map(Pp)}};function Mp(a){return function(t){var u,r,o,c,n=Xn.create({after:Vn(["container"])}),i=(u=kp,r=It.fromTag("br"),o=N.none(),c=function(e){return yn(e).dom().defaultView},{cleanup:function(){Oa(r)},toOn:function(i,e){var a=c(e);o.each(function(e){var t=Ln(i),n=vn(i,e.startContainer())&&t<e.startOffset()?t:e.startOffset,r=vn(i,e.endContainer())&&t<e.endOffset()?t:e.endOffset,o=Tp(e.startContainer(),n,e.endContainer(),r);u.set(a,o)})},toOff:function(e,t){var n=c(t);_n(t,r),o=u.get(n,Tp),u.set(n,Tp(r,0,r,0))}}),e=Ap(i,t,a);return e.events.after.bind(function(e){i.toOn(t,e.container()),n.trigger.after(e.container())}),{run:function(){e.instance()()},events:n.registry}}}var Rp="powerpaste-styles",Fp="#"+Rp,jp={injectStyles:function(e){if(!jf(Fp)){var t="<style>.ephox-cement-flashbin-helpcopy-kbd {font-size: 3em !important; text-align: center !important; vertical-align: middle !important; margin: .5em 0} .ephox-cement-flashbin-helpcopy-kbd .ephox-polish-help-kbd {font-size: 3em !important; vertical-align: middle !important;} .ephox-cement-flashbin-helpcopy a {text-decoration: underline} .ephox-cement-flashbin-loading-spinner {background-image: url("+e+") !important; width: 96px !important; height:96px !important; display: block; margin-left: auto !important; margin-right: auto !important; margin-top: 2em !important; margin-bottom: 2em !important;} .ephox-cement-flashbin-loading p {text-align: center !important; margin: 2em 0 !important} .ephox-cement-flashbin-target {height: 1px !important;} .ephox-cement-flashbin-target.ephox-cement-flash-activate {height: 150px !important; width: 100% !important;} .ephox-cement-flashbin-target object {height: 1px !important;} .ephox-cement-flashbin-target.ephox-cement-flash-activate object {height: 150px !important; width: 100% !important;} .ephox-cement-flashbin-helpcopy p {white-space: normal;}</style>",n=It.fromHtml(t);ft(n,"type","text/css"),ft(n,"id",Rp);var r=ur("head").getOrDie("Head element could not be found.");_n(r,n)}},removeStyles:function(){if(jf(Fp)){var e=ur("head").getOrDie("Head element could not be found."),t=sr(e,Fp).getOrDie("The style element could not be removed");Oa(t)}}},Up="x-tinymce/html",Bp="\x3c!-- "+Up+" --\x3e",Yp={mark:function(e){return Bp+e},unmark:function(e){return e.replace(Bp,"")},isMarked:function(e){return-1!==e.indexOf(Bp)},retainContentEditable:function(e){return e.replace(/ contenteditable="([^"]+)"/g,' data-mce-contenteditable="$1"')},restoreContentEditable:function(e){return e.replace(/ data-mce-contenteditable="([^"]+)"/g,' contenteditable="$1"')},internalHtmlMime:v(Up)},Hp=function(){},Wp=function(e,t,n){if(r=e,!1!==tinymce.Env.iOS||void 0===r||"function"!=typeof r.setData)return!1;try{return e.clearData(),e.setData("text/html",t),e.setData("text/plain",n),e.setData(Yp.internalHtmlMime(),t),!0}catch(e){return!1}var r},qp=function(e,t,n,r){Wp(e.clipboardData,t.html,t.text)?(e.preventDefault(),r()):n(t.html,r)},$p=function(a){return function(e,t){var n=a.dom.create("div",{contenteditable:"false","data-mce-bogus":"all"}),r=a.dom.create("div",{contenteditable:"true","data-mce-bogus":"all"},e);a.dom.setStyles(n,{position:"fixed",top:"50%",left:"-3000px",width:"1000px",overflow:"hidden"}),n.appendChild(r),a.dom.add(a.getBody(),n);var o=a.selection.getRng();r.focus();var i=a.dom.createRng();i.selectNodeContents(r),a.selection.setRng(i),setTimeout(function(){a.selection.setRng(o),n.parentNode.removeChild(n),t()},0)}},Vp=function(e){var t=Yp.retainContentEditable(e.selection.getContent({contextual:!0}));return{html:Yp.mark(t),text:e.selection.getContent({format:"text"})}},Xp={register:function(e){var t,n;e.on("cut",(t=e,function(e){!1===t.selection.isCollapsed()&&qp(e,Vp(t),$p(t),function(){setTimeout(function(){t.execCommand("Delete")},0)})})),e.on("copy",(n=e,function(e){!1===n.selection.isCollapsed()&&qp(e,Vp(n),$p(n),Hp)}))}},Gp={nodeToString:function(e){var t=document.createElement("div");t.appendChild(e.cloneNode(!0));var n=t.innerHTML;return t=e=null,n},restoreStyleAttrs:function(e){M(k(e.getElementsByTagName("*"),It.fromDom),function(e){mt(e,"data-mce-style")&&!mt(e,"style")&&ft(e,"style",dt(e,"data-mce-style"))})}},zp=St("url","html"),Kp=function(e){return/^https?:\/\/[\w\?\-\/+=.&%@~#]+$/i.test(e)},Zp=Kp,Jp=function(e){return Kp(e)&&/.(gif|jpe?g|png)$/.test(e)},Qp=function(n){var e=/^<a href="([^"]+)">([^<]+)<\/a>$/.exec(n);return N.from(e).bind(function(e){var t=zp(e[1],n);return e[1]===e[2]?N.some(t):N.none()})},eg=function(e,t,n){return"extra"in e.undoManager?(e.undoManager.extra(function(){tg(e,t)},n),N.some(!0)):N.none()},tg=function(e,t){return e.insertContent(t,{merge:!1!==e.settings.paste_merge_formats,paste:!0}),N.some(!0)},ng={until:function(t,n,e){return Sa(e,function(e){return e(t,n)})},linkSelection:function(r,e){return Qp(e).bind(function(e){var t,n;return!1===r.selection.isCollapsed()&&Zp(e.url())?eg(t=r,(n=e).html(),function(){t.execCommand("mceInsertLink",!1,n.url())}):N.none()})},insertImage:function(r,e){return Qp(e).bind(function(e){return Jp(e.url())?eg(t=r,(n=e).html(),function(){t.insertContent('<img src="'+n.url()+'">')}):N.none();var t,n})},insertContent:tg},rg=function(e,t){return e.hasEventListeners(t)},og=function(e){return e.plugins.powerpaste},ig={process:function(e,t,n){var r,o,i,a,u,c,s,f,l,d,m,p,g,v=Yp.unmark(t);return d=v,m=n,o=rg(l=r=e,"PastePreProcess")?(p=d,g=m,l.fire("PastePreProcess",{internal:g,content:p}).content):d,i=n,rg(r,"PastePostProcess")?(u=o,c=i,s=(a=r).dom.add(a.getBody(),"div",{style:"display:none"},u),f=a.fire("PastePostProcess",{internal:c,node:s}).node.innerHTML,a.dom.remove(s),f):o},registerEvents:function(t){var n=t.settings;n.paste_preprocess&&t.on("PastePreProcess",function(e){n.paste_preprocess.call(t,og(t),e)}),n.paste_postprocess&&t.on("PastePostProcess",function(e){n.paste_postprocess.call(t,og(t),e)})}};function ag(g,v,e,t,h){var y,b,x,T,E=wc(N.none()),n=function(e,t){return e.replace(/\/$/,"")+"/"+t.replace(/^\//,"")};T=n(t?t.jsUrl:e,"/js"),b=n(t?t.swfUrl:e,"/flash/textboxpaste.swf"),x=n(t?t.imgUrl:e,"/img/spinner_96.gif");var w=function(e,t,n){var r,o=!1!==e.settings.smart_paste?[ng.linkSelection,ng.insertImage]:[];ng.until(e,t,o.concat([(r=n,function(e,t){return e.undoManager.transact(function(){ng.insertContent(e,t),Gp.restoreStyleAttrs(e.getBody()),h.prepareImages(r)}),N.some(!0)})]))},I=function(){y&&g.selection.moveToBookmark(y),y=null};g.on("init",function(e){jp.injectStyles(x);var s,t,n,r,o,i,a,u,c={baseUrl:T,swf:b,officeStyles:g.settings.powerpaste_word_import||ho.officeStyles,htmlStyles:g.settings.powerpaste_html_import||ho.htmlStyles,translations:We.translate,allowLocalImages:!1!==g.settings.powerpaste_allow_local_images,enableFlashImport:!1!==g.settings.powerpaste_enable_flash_import,pasteBinAttrs:{"data-mce-bogus":"all"},intraFlag:{clipboardType:Yp.internalHtmlMime,findClipboardTags:function(e){var t=R(e,function(e){return at(e)&&Zt(ms(e),Yp.internalHtmlMime())});return t.length?N.some(t):N.none()}},preprocessor:function(e){return z.pure(e)}},f=(s=g,{createDialog:function(){var r,o="",i="",a=[],u=null,t=Xn.create({close:Vn([])}),c=function(e){t.trigger.close()},e=function(){r.off("close",c),r.close("close")};return{events:t.registry,setTitle:function(e){o=e},setContent:function(e){var t=Gp.nodeToString(e.dom());i=[{type:"container",html:t}],u=e},setButtons:function(e){var r=[];e.forEach(function(e,t,n){r.push({text:e.text,ariaLabel:e.text,onclick:e.click})}),a=r},show:function(){0===a.length&&(a=[{text:"Close",onclick:function(){r.close()}}]);var e={title:o,spacing:10,padding:10,minWidth:300,minHeight:100,layout:"flex",items:i,buttons:a};r=s.windowManager.open(e);var t=It.fromDom(r.getEl()),n=sr(t,"."+dt(u,"class")).getOrDie("We must find this element or we cannot continue");Cn(n,u),Oa(n),r.on("close",c)},hide:function(){e()},destroy:function(){e()},reflow:function(){}}}}),l=It.fromDom(g.getBody()),d=(t=l,n=f.createDialog,r=S,i=xp((o=c).baseUrl),a=L(up,n),u=Mp(void 0!==o.pasteBinAttrs?o.pasteBinAttrs:{}),jl([$m.onlyText(),$m.wordimport(i,n,o,a),$m.googledocs(o,r,t),$m.nativeHtml(n,o,r,t),$m.image(o)],$m.fallback(n,o,r,u,t,a))),m=jl([$m.text()],$m.none());M([d,m],function(e){e.events.cancel.bind(function(){I()}),e.events.error.bind(function(e){I(),g.notificationManager?g.notificationManager.open({text:We.translate(e.message()),type:"error"}):qe.showDialog(g,We.translate(e.message()))}),e.events.insert.bind(function(e){var t=k(e.elements(),function(e){return Gp.nodeToString(e.dom())}).join(""),n=Yp.restoreContentEditable(t);g.focus(),h.importImages(e.assets()).get(function(){I(),w(g,ig.process(g,n,e.isInternal()),e.assets()),h.uploadImages(e.assets())})})}),g.addCommand("mceInsertClipboardContent",function(e,t){void 0!==t.content?d.pasteCustom(dl.fromHtml(t.content)):void 0!==t.text&&m.pasteCustom(dl.fromText(t.text))});var p=jd(l,"paste",function(e){y||(y=g.selection.getBookmark(1)),(v.isText()?m:d).paste(e.raw()),v.reset(),setTimeout(function(){g.windowManager.windows[0]&&g.windowManager.windows[0].getEl()&&g.windowManager.windows[0].getEl().focus()},1)});E.set(N.some(p)),Xp.register(g)}),g.on("remove",function(e){1===tinymce.editors.length&&jp.removeStyles(),E.get().each(function(e){return e.unbind()})})}var ug=function(e){return tinymce.util.VK.metaKeyPressed(e)&&86===e.keyCode&&e.shiftKey},cg=function(r,o){return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=r.console;n&&o in n&&n[o].apply(n,arguments)}},sg={log:cg(window,"log"),error:cg(window,"error"),warn:cg(window,"warm")},fg=function(e,t){e.dom.bind(t,"drop dragstart dragend dragover dragenter dragleave dragdrop draggesture",function(e){e.preventDefault(),e.stopImmediatePropagation()})},lg=function(t){t.on("init",function(e){fg(t,t.getBody()),t.inline||fg(t,t.getDoc())})};function dg(i){return e(tinymce,"4.0.28")?(sg.error('The "powerpaste" plugin requires at least 4.0.28 version of TinyMCE.'),function(){}):function(e,t){var n,r=function(t){var n=wc(t.settings.paste_as_text),r=wc(!1);t.on("keydown",function(e){ug(e)&&(r.set(!0),tinymce.Env.ie&&tinymce.Env.ie<10&&(e.preventDefault(),t.fire("paste",{})))});var o=Ye(function(){var e=t.translate("Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.");t.notificationManager.open({text:e,type:"info"})});return{toggle:function(){var e=!n.get();this.active(e),n.set(e),t.fire("PastePlainTextToggle",{state:e}),!0===e&&!1!==t.settings.paste_plaintext_inform&&o(),t.focus()},reset:function(){r.set(!1)},isText:function(){return r.get()||n.get()}}}(e),o=function(){var t=this;t.active(r.isText()),e.on("PastePlainTextToggle",function(e){t.active(e.state)})};tinymce.Env.ie&&tinymce.Env.ie<10?function(t,e,n){var r,o,i=this,a=Qi(t,We.translate),u=function(t){return function(e){t(e)}};r=go.getOnPasteFunction(t,a.showDialog,e),t.on("paste",u(r)),o=go.getOnKeyDownFunction(t,a.showDialog,e),t.on("keydown",u(o)),t.addCommand("mceInsertClipboardContent",function(e,t){a.showDialog(t.content||t)}),t.settings.paste_preprocess&&t.on("PastePreProcess",function(e){t.settings.paste_preprocess.call(i,i,e)})}(e,r):(n=so(e),ag(e,r,t,i,n),io(e)?lg(e):pa(e,0,0,n)),ig.registerEvents(e),e.addButton("pastetext",{icon:"pastetext",tooltip:"Paste as text",onclick:r.toggle,onPostRender:o}),e.addMenuItem("pastetext",{text:"Paste as text",selectable:!0,onclick:r.toggle,onPostRender:o})}}return function(e){tinymce.PluginManager.requireLangPack("powerpaste","ar,ca,cs,da,de,el,es,fa,fi,fr_FR,he_IL,hr,hu_HU,it,ja,kk,ko_KR,nb_NO,nl,pl,pt_BR,pt_PT,ro,ru,sk,sl_SI,sv_SE,th_TH,tr,uk,zh_CN,zh_TW"),tinymce.PluginManager.add("powerpaste",dg(e))}}()();
