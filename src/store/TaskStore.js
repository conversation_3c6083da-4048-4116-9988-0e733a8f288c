import {defineStore} from 'pinia'
import {ConfigModel} from "../model/ConfigModel";

export const TaskStore = defineStore('TaskStore', {
    state: () => ({
        // 任务开始时间
        startTime: 0,
        // 环境-初始化
        init: false,
        // 手机类型
        mobileType: "Android",
        // 浏览器类型
        browserType: "",
        // 屏幕方向 横屏landscape 竖屏portrait
        screenDirection: false,
        // 屏幕全屏
        screenFull: false,
        // 当前gps位置
        gpsLng: "",
        gpsLat: "",
        // 任务起始点gps位置
        gpsLngS1: "",
        gpsLatS1: "",
        // 当前任务进度
        nowTaskId: "",
        // 下一关
        nextTaskId: "",
        // 选择的角色
        roleId: "",
        // 任务记录id
        recordId: "",
        // 已完成的任务数量
        gotTaskNumber: 0,
        // 已获得线索数量
        gotClueNumber: 0,
        // 已完成决策数量
        gotDecisionNumber: 0,
        // 任务记录
        /**
         * {
         *     "S1":{
         *         taskId:"S1", // 任务编号
         *         clueList:[], // 已获取的线索列表
         *         selectQuestionAnswer:[[]], // 选择的选择题答案
         *         startTime:123,// 任务开启时间
         *         endTime:123, // 任务结束时间
         *         extraInfo:{}, // 保存的额外信息
         *         completed:true,// 是否已完成任务
         *         status:"", // 任务状态 opened开启了关卡但是未开始 arriveGpsLocation到达gps位置 scanEdQrCode 扫描了入口二维码 scanEdArCode 扫描了AR图片 doing进行中 completed已完成 fail失败 pass跳过
         *         logInfo:[
         *             {
         *                 type:"opened",
         *                 date:123,
         *                 info:"",
         *             }
         *         ],// 日志信息
         *     }
         * }
         */
        recordInfo: {},
        testInfo: {}
    }),
    getters: {},
    actions: {
        // 保存记录信息
        setRecordInfo(recordInfo) {
            this.recordInfo = recordInfo
        }
    },
})
