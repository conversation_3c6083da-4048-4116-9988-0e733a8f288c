import {defineStore} from 'pinia'
import {ConfigModel} from "../model/ConfigModel";

export const MainStore = defineStore('MainStore', {
    state: () => ({
        // 用户信息
        userInfo: {},
        // 标记是否登录
        hasLogin: false,
        // 配置-大厅配置
        hallConfig: undefined,
    }),
    getters: {},
    actions: {
        // 设置用户信息
        setUserInfo(userInfo) {
            this.userInfo = userInfo
        },
        // 获取大厅配置
        async getHallConfig() {
            if (this.hallConfig) {
                return this.hallConfig
            } else {
                let hallConfig = await ConfigModel.getConfig("hallConfig");
                this.hallConfig = JSON.parse(hallConfig)
            }
        },
    },
})
