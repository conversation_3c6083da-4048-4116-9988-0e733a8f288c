/**
 * 自定义设置面包导航
*/
// 学院教师详情页
export const breadList_departmentList=[
  {
    path: '/exp/school',
    redirect:"noRedirect",
    meta: {title: '用户管理'}
  },
  {
    path: 'school',
    meta: {title: '学校管理'},
  },
  {
    path: 'department',
    meta: {title: '学院信息'}
  },
]
export const breadList_experimentListDetail=[
  {
    path: '/exp/experiment',
    redirect:"noRedirect",
    meta: {title: '实验课程管理'}
  },
  {
    path: 'list',
    meta: {title: '实验列表'},
  },
  {
    path: 'detail',
    meta: {title: '实验详情'}
  },
]
export const breadList_experimentSettingDetail=[
  {
    path: '/exp/experiment',
    redirect:"noRedirect",
    meta: {title: '实验课程管理'}
  },
  {
    path: 'setting',
    meta: {title: '实验安排'},
  },
  {
    path: 'settingDetail',
    meta: {title: '实验安排详情'}
  },
]
export const breadList_experimentEdit=[
  {
    path: '/exp/experiment',
    redirect:"noRedirect",
    meta: {title: '实验课程管理'}
  },
  {
    path: 'setting',
    meta: {title: '实验安排'},
  },
  {
    path: 'settingEdit',
    meta: {title: '授权实验详情'}
  },
]

// 处理自定义面包屑显示
export function customBreadShow(path){
  if(path==="/exp/school/department"){
    sessionStorage.setItem("breadNavList",JSON.stringify(breadList_departmentList))
  }
  if(path==="/exp/experiment/detail"){
    sessionStorage.setItem("breadNavList",JSON.stringify(breadList_experimentListDetail))
  }
  if(path==="/exp/experiment/settingDetail"){
    sessionStorage.setItem("breadNavList",JSON.stringify(breadList_experimentSettingDetail))
  }
  if(path==="/exp/experiment/settingEdit"){
    sessionStorage.setItem("breadNavList",JSON.stringify(breadList_experimentEdit))
  }
  return
}
