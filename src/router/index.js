import {createRouter, createWebHistory} from 'vue-router'
import <PERSON>View from "@/views/index.vue"
import LoginView from "@/views/login.vue";
import RegView from "@/views/reg.vue";
import HallView from "@/views/hall.vue";
import ScriptInfoView from "@/views/scriptInfo.vue";
import MyTaskView from "@/views/myTask.vue"
import UserIndexView from "@/views/user/index.vue";
import ArticleViewView from "@/views/articleView.vue";
import HelpCenterView from "@/views/helpCenter.vue"
import QuestionViewView from "@/views/questionView.vue";
import UserChangePasswordView from "@/views/user/changePassword.vue";
import F_1_2View from "@/views/fdldq/p1.vue"
import AR_Main_View from "@/views/ar_main.vue"
import Map_Html_view from "@/views/map_html.vue"
import Task_Select_View from "@/views/task_select.vue"
import f_start_View from "@/views/fdldq/start.vue"
import f_s1_View from "@/views/fdldq/s1.vue"
import f_a2_1_View from "@/views/fdldq/a2_1.vue"
import f_a2_2_View from "@/views/fdldq/a2_2.vue"
import f_a2_3_View from "@/views/fdldq/a2_3.vue"
import f_a1_View from "@/views/fdldq/a1.vue"
import f_b1_View from "@/views/fdldq/b1.vue"
import f_b3_View from "@/views/fdldq/b3.vue"
import f_c1_View from "@/views/fdldq/c1.vue"
import f_c2_View from "@/views/fdldq/c2.vue"
import f_c3_View from "@/views/fdldq/c3.vue"
import f_d3_View from "@/views/fdldq/d3.vue"
import f_d4_View from "@/views/fdldq/d4.vue"
import f_end_View from "@/views/fdldq/end.vue"
import f_common2019_View from "@/views/fdldq/common2019.vue"
import f_common2022_View from "@/views/fdldq/common2022.vue"
import f_test2019_View from "@/views/fdldq/test2019.vue"
import f_test2022_View from "@/views/fdldq/test2022.vue"
import f_testOther_View from "@/views/fdldq/testOther.vue"
import mapTestView from "@/views/map/mapTest.vue"
import gpsNowView from "@/views/map/gpsNow.vue"

const router = createRouter({
    // history: createWebHistory(import.meta.env.BASE_URL),
    history: createWebHistory("/"),
    routes: [
        {
            path: '/',
            name: 'home',
            component: IndexView
        },
        {
            path: '/login',
            name: 'Login',
            component: LoginView
        },
        {
            path: '/reg',
            name: 'Reg',
            component: RegView
        },
        {
            path: '/hall',
            name: 'Hall',
            component: HallView
        },
        {
            path: '/scriptInfo',
            name: 'ScriptInfo',
            component: ScriptInfoView
        },
        {
            path: '/myTask',
            name: 'myTask',
            component: MyTaskView
        },
        {
            path: '/user/index',
            name: 'userIndex',
            component: UserIndexView
        },
        {
            path: '/user/changePassword',
            name: 'userChangePassword',
            component: UserChangePasswordView
        },
        {
            path: '/articleView',
            name: 'articleView',
            component: ArticleViewView
        },
        {
            path: '/HelpCenter',
            name: 'HelpCenter',
            component: HelpCenterView
        },
        {
            path: '/questionView',
            name: 'questionView',
            component: QuestionViewView
        },
        {
            path: '/fdldq/p1',
            name: 'f_1_2View',
            component: F_1_2View
        },
        {
            path: '/ar_main',
            name: 'AR_Main_View',
            component: AR_Main_View
        },
        {
            path: '/map_html',
            name: 'Map_Html_view',
            component: Map_Html_view
        },
        {
            path: '/task_select',
            name: 'Task_Select_View',
            component: Task_Select_View
        },
        {
            path: '/fdldq/start',
            name: 'f_start_View',
            component: f_start_View
        },
        {
            path: '/fdldq/s1',
            name: 'f_s1_View',
            component: f_s1_View
        },
        {
            path: '/fdldq/a1',
            name: 'f_a1_View',
            component: f_a1_View
        },
        {
            path: '/fdldq/a2_1',
            name: 'f_a2_1_View',
            component: f_a2_1_View
        },
        {
            path: '/fdldq/a2_2',
            name: 'f_a2_2_View',
            component: f_a2_2_View
        },
        {
            path: '/fdldq/a2_3',
            name: 'f_a2_3_View',
            component: f_a2_3_View
        },
        {
            path: '/fdldq/b1',
            name: 'f_b1_View',
            component: f_b1_View
        },
        {
            path: '/fdldq/b3',
            name: 'f_b3_View',
            component: f_b3_View
        },
        {
            path: '/fdldq/c1',
            name: 'f_c1_View',
            component: f_c1_View
        },
        {
            path: '/fdldq/c2',
            name: 'f_c2_View',
            component: f_c2_View
        },
        {
            path: '/fdldq/c3',
            name: 'f_c3_View',
            component: f_c3_View
        },
        {
            path: '/fdldq/d3',
            name: 'f_d3_View',
            component: f_d3_View
        },
        {
            path: '/fdldq/d4',
            name: 'f_d4_View',
            component: f_d4_View
        },
        {
            path: '/fdldq/end',
            name: 'f_end_View',
            component: f_end_View
        },
        {
            path: '/fdldq/common2019',
            name: 'f_common2019_View',
            component: f_common2019_View
        },
        {
            path: '/fdldq/common2022',
            name: 'f_common2022_View',
            component: f_common2022_View
        },
        {
            path: '/fdldq/test2019',
            name: 'f_test2019_View',
            component: f_test2019_View
        },
        {
            path: '/fdldq/test2022',
            name: 'f_test2022_View',
            component: f_test2022_View
        },
        {
            path: '/fdldq/testOther',
            name: 'f_testOther_View',
            component: f_testOther_View
        },
        {
            // 实时地图位置
            path: '/gpsNow',
            name: "gpsNow",
            component: gpsNowView
        },
        {
            // 美术地图测试
            path: '/mapTest',
            name: "mapTest",
            component: mapTestView
        },
        {
            path: '/shareScore',
            name: 'shareScore',
            component: () => import('@/views/shareScore.vue'),
            meta: {
                title: '成绩分享'
            }
        },
    ]
})

export default router
