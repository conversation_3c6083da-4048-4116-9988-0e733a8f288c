import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/* Router Modules */

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/auth-redirect',
    component: () => import('@/views/login/auth-redirect'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },
  {
    path: '/test',
    component: () => import('@/views/test/index'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: {title: 'dashboard', icon: 'international', breadcrumb: false}
      }
    ]
  }

]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [

  /** when your routing map is too long, you can split it into small modules **/
  // 剧本管理
  {
    path: '/script/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'scriptManage',
    alwaysShow: true,
    meta: {
      title: '剧本管理',
      icon: 'education',
      roles: ['administrator']
    },
    children: [
      {
        path: 'series',
        component: () => import('@/views/scriptManage/series'),
        name: 'seriesManage',
        meta: {title: '系列管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'script',
        component: () => import('@/views/scriptManage/script'),
        name: 'scriptManage',
        meta: {title: '剧本管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'scriptDetail',
        component: () => import('@/views/scriptManage/detail'),
        name: 'scriptDetail',
        hidden: true,
        meta: {title: '剧本信息', noCache: true, roles: ['administrator']}
      },
      {
        path: 'recordList',
        component: () => import('@/views/scriptManage/recordList'),
        name: 'recordList',
        meta: {title: '剧本记录', noCache: true, roles: ['administrator']}
      },
      // {
      //   path: 'scriptCpde',
      //   component: () => import('@/views/scriptManage/scriptCode'),
      //   name: 'scriptCodeManage',
      //   meta: {title: '实验码管理', noCache: true, roles: ['administrator']}
      // },
    ]
  },
  // 用户管理
  {
    path: '/user/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'userManage',
    alwaysShow: true,
    meta: {
      title: '用户管理',
      icon: 'user',
      roles: ['administrator']
    },
    children: [
      {
        path: 'list',
        component: () => import('@/views/userManage/userList'),
        name: 'userList',
        meta: {title: '用户列表', noCache: true, roles: ['administrator']}
      }
    ]
  },
  // 系统
  {
    path: '/system/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'systemManage',
    alwaysShow: true,
    meta: {
      title: '系统管理',
      icon: 'education',
      roles: ['administrator']
    },
    children: [
      {
        path: 'setting',
        component: () => import('@/views/systemManage/setting'),
        name: 'systemSetting',
        meta: {title: '系统设置', noCache: true, roles: ['administrator']}
      },
      {
        path: 'help',
        component: () => import('@/views/systemManage/help'),
        name: 'systemHelp',
        meta: {title: '帮助管理', noCache: true, roles: ['administrator']}
      },
      // {
      //   path: 'feedback',
      //   component: () => import('@/views/systemManage/feedback'),
      //   name: 'systemFeedback',
      //   meta: {title: '反馈管理', noCache: true, roles: ['administrator']}
      // },
      {
        path: 'log',
        component: () => import('@/views/systemManage/log'),
        name: 'systemLog',
        meta: {title: '日志管理', noCache: true, roles: ['administrator']}
      },
    ]
  },
  // 账号
  {
    path: '/account/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'accountManage',
    alwaysShow: true,
    meta: {
      title: '我的信息',
      icon: 'user'
    },
    children: [
      {
        path: 'info',
        component: () => import('@/views/account/info'),
        name: 'accountInfo',
        meta: {title: '我的信息', noCache: true}
      }
    ]
  },

  // 404 page must be placed at the end !!!
  {path: '*', redirect: '/404', hidden: true}
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({y: 0}),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-*********
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
