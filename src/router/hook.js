/**
 * 路由钩子
 */

import router from './index'
import {UserModel} from "../model/UserModel";
import {MainStore} from "../store/MainStore";
import {TaskStore} from "../store/TaskStore";
import {ScriptRecordModel} from "../model/ScriptRecordModel";
import {ToolsModel} from "../model/ToolsModel";
import {toast_text} from "../utils/nut_component";


router.beforeEach(async (to, from, next) => {
    /**
     * 每次刷新页面后的初始化
     */
    let mainStore = MainStore()
    let taskStore = TaskStore()
    // 环境初始化
    if (!taskStore.init) {
        // 横竖屏判断和切换监听
        ToolsModel.addOrientationListen()
        ToolsModel.addFullscreenListened()
        // 运行环境判断
        ToolsModel.getEnvironment()
        taskStore.$patch({
            init: true,
        })
    }
    if (UserModel.getToken()) {// 如果已登录过，cookies中存在token
        if (!mainStore.userInfo.hasOwnProperty("userId")) {// 如果没有获取过用户信息
            // 通过token获取用户信息并记录到store
            await UserModel.getInfoByToken()
        }
        if (ScriptRecordModel.getRecordIdFromCookie()) {// 如果cookie中存在记录id
            if (!taskStore["recordId"]) {// 如果没有获取过记录信息
                await ScriptRecordModel.getUserOneRecordInfoSaveToStore(ScriptRecordModel.getRecordIdFromCookie())
            }
        }
    } else {// 如果没有登录
        // window.location.href = "/login"
    }
    next()
})
