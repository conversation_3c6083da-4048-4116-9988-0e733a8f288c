// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    VanButton: typeof import('vant/es')['Button']
    VanCollapse: typeof import('vant/es')['Collapse']
    VanCollapseItem: typeof import('vant/es')['CollapseItem']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanPopup: typeof import('vant/es')['Popup']
  }
}
